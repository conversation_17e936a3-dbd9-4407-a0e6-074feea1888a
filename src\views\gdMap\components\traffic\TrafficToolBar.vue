<template>
	<div class="tool-bar-container">
		<div class="tool-bar-item" :class="{ 'is-panel': isPanel[0] }">
			<div class="container" @click="onClickToolBar(0)">
				<div class="icon">
					<footerTrafficLights />
				</div>
				<div class="label">实时路况</div>
			</div>
			<div class="panel">
				<div class="panel-content">
					<div class="panel-label">实时路况</div>
					<div class="panel-item is-disabled">
						<div class="panel-item-line is-green"></div>
						<div class="panel-item-label">畅通</div>
					</div>
					<div class="panel-item is-disabled">
						<div class="panel-item-line is-yellow"></div>
						<div class="panel-item-label">缓行</div>
					</div>
					<div class="panel-item is-disabled">
						<div class="panel-item-line is-red"></div>
						<div class="panel-item-label">拥堵</div>
					</div>
					<div class="panel-item is-disabled">
						<div class="panel-item-line is-deepRed"></div>
						<div class="panel-item-label">严重拥堵</div>
					</div>
				</div>
			</div>
		</div>
		<div class="tool-bar-item" :class="{ 'is-panel': isPanel[1] }">
			<div class="container" @click="onClickToolBar(1)">
				<div class="icon">
					<footerRoadBlock />
				</div>
				<div class="label">交通事故</div>
			</div>
			<div class="panel">
				<div class="panel-content">
					<div class="panel-label">交通事故</div>
					<div
						class="panel-item"
						:class="{ 'is-active': accidentVisible['jam'] }"
						@click="onClickTraffic('jam')"
					>
						<div class="panel-item-icon">
							<jam />
						</div>
						<div class="panel-item-label">交通拥堵</div>
					</div>
					<div
						class="panel-item"
						:class="{ 'is-active': accidentVisible['roadClosure'] }"
						@click="onClickTraffic('roadClosure')"
					>
						<div class="panel-item-icon">
							<roadClosure />
						</div>
						<div class="panel-item-label">道路封路</div>
					</div>
					<div
						class="panel-item"
						:class="{ 'is-active': accidentVisible['accident'] }"
						@click="onClickTraffic('accident')"
					>
						<div class="panel-item-icon">
							<accident />
						</div>
						<div class="panel-item-label">交通事故</div>
					</div>
					<div
						class="panel-item"
						:class="{ 'is-active': accidentVisible['otherAccident'] }"
						@click="onClickTraffic('otherAccident')"
					>
						<div class="panel-item-icon">
							<otherAccident />
						</div>
						<div class="panel-item-label">其他事故</div>
					</div>
				</div>
			</div>
		</div>
		<div class="tool-bar-item" :class="{ 'is-panel': isPanel[2] }">
			<div class="container" @click="onClickToolBar(2)">
				<div class="icon">
					<footerDevice />
				</div>
				<div class="label">设备</div>
			</div>
			<div class="panel">
				<div class="panel-content">
					<div class="panel-label">设备</div>
					<div
						class="panel-item"
						:class="{ 'is-active': deviceVisible['light'] }"
						@click="onClickDevice('light')"
					>
						<div class="panel-item-icon">
							<deviceLight />
						</div>
						<div class="panel-item-label">红绿灯</div>
					</div>
					<div
						class="panel-item"
						:class="{ 'is-active': deviceVisible['camera'] }"
						@click="onClickDevice('camera')"
					>
						<div class="panel-item-icon">
							<deviceCamera />
						</div>
						<div class="panel-item-label">摄像机</div>
					</div>
					<div
						class="panel-item"
						:class="{ 'is-active': deviceVisible['adjustment'] }"
						@click="onClickDevice('adjustment')"
					>
						<div class="panel-item-icon">
							<deviceAdjustment />
						</div>
						<div class="panel-item-label">交调设备</div>
					</div>
					<div
						class="panel-item"
						:class="{ 'is-active': deviceVisible['edge'] }"
						@click="onClickDevice('edge')"
					>
						<div class="panel-item-icon">
							<deviceEdge />
						</div>
						<div class="panel-item-label">边缘计算</div>
					</div>
				</div>
			</div>
		</div>
		<div class="tool-bar-item" :class="{ 'is-panel': isPanel[3] }">
			<div class="container" @click="onClickToolBar(3)">
				<div class="icon">
					<footerAlarm />
				</div>
				<div class="label">拥堵预警点</div>
			</div>
			<div class="panel">
				<div class="panel-content">
					<div class="panel-label">拥堵预警点</div>
					<div
						class="panel-item"
						:class="{ 'is-active': alarmVisible['danger'] }"
						@click="onClickAlarm('danger')"
					>
						<div class="panel-item-icon">
							<alarmBellDanger />
						</div>
						<div class="panel-item-label">异常拥堵</div>
					</div>
					<div
						class="panel-item"
						:class="{ 'is-active': alarmVisible['warning'] }"
						@click="onClickAlarm('warning')"
					>
						<div class="panel-item-icon">
							<alarmBellWarning />
						</div>
						<div class="panel-item-label">常规拥堵</div>
					</div>
					<div
						class="panel-item"
						:class="{ 'is-active': alarmVisible['primary'] }"
						@click="onClickAlarm('primary')"
					>
						<div class="panel-item-icon">
							<alarmBellPrimary />
						</div>
						<div class="panel-item-label">高疑似拥堵</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import emitter from "@/utils/emitter";

import footerTrafficLights from "@/assets/modules/traffic/icon/footer-traffic-lights.svg?component";
import footerRoadBlock from "@/assets/modules/traffic/icon/footer-road-block.svg?component";
import footerDevice from "@/assets/modules/traffic/icon/footer-device.svg?component";
import footerAlarm from "@/assets/modules/traffic/icon/footer-alarm.svg?component";

import jam from "@/assets/modules/traffic/icon/jam.svg?component";
import roadClosure from "@/assets/modules/traffic/icon/roadClosure.svg?component";
import accident from "@/assets/modules/traffic/icon/accident.svg?component";
import otherAccident from "@/assets/modules/traffic/icon/otherAccident.svg?component";

import deviceLight from "@/assets/modules/traffic/icon/footer-device-light.svg?component";
import deviceCamera from "@/assets/modules/traffic/icon/footer-device-camera.svg?component";
import deviceAdjustment from "@/assets/modules/traffic/icon/footer-device-adjustment.svg?component";
import deviceEdge from "@/assets/modules/traffic/icon/footer-device-edge.svg?component";

import alarmBellDanger from "@/assets/modules/traffic/icon/alarmBell-danger.svg?component";
import alarmBellWarning from "@/assets/modules/traffic/icon/alarmBell-warning.svg?component";
import alarmBellPrimary from "@/assets/modules/traffic/icon/alarmBell-primary.svg?component";

const isPanel = ref({
	0: false,
	1: false,
	2: false,
	3: false,
});

const accidentVisible = ref({
	jam: false,
	roadClosure: false,
	accident: false,
	otherAccident: false,
});
const deviceVisible = ref({
	light: false,
	camera: false,
	adjustment: false,
	edge: false,
});

const alarmVisible = ref({
	danger: false,
	warning: false,
	primary: false,
});

onMounted(() => {
	emitter.$on("traffic-marker-types", handleMarkerTypes);
});

onUnmounted(() => {
	emitter.$off("traffic-marker-types", handleMarkerTypes);
});

const handleMarkerTypes = ({ type, category }) => {
	if (type === "trafficAccident") {
		accidentVisible.value = { ...accidentVisible.value, ...category };
	} else if (type === "trafficDevice") {
		deviceVisible.value = { ...deviceVisible.value, ...category };
	} else if (type === "trafficAlarm") {
		alarmVisible.value = { ...alarmVisible.value, ...category };
	}
};

const onClickToolBar = (index) => {
	isPanel.value[index] = !isPanel.value[index];
};

const onClickTraffic = (key) => {
	accidentVisible.value[key] = !accidentVisible.value[key];
	emitter.$emit("update-markers", {
		type: "trafficAccident",
		category: key,
		visible: accidentVisible.value[key],
	});
};

const onClickDevice = (key) => {
	deviceVisible.value[key] = !deviceVisible.value[key];
	emitter.$emit("update-markers", {
		type: "trafficDevice",
		category: key,
		visible: deviceVisible.value[key],
	});
};

const onClickAlarm = (key) => {
	alarmVisible.value[key] = !alarmVisible.value[key];
	emitter.$emit("update-markers", {
		type: "trafficDevice",
		category: key,
		visible: alarmVisible.value[key],
	});
};
</script>

<style lang="scss" scoped>
.tool-bar-container {
	margin-bottom: 20px;
	width: 432px;
	height: 44px;
	display: flex;
	background: linear-gradient(
			180deg,
			rgba(0, 18, 33, 0.6) 0%,
			rgba(0, 18, 33, 0.6) 44%,
			#001221 100%
		)
		no-repeat;
	border: 1px solid;
	border-image: linear-gradient(180deg, rgba(23, 100, 167, 0), rgba(0, 30, 56, 1)) 1 1;
}

.tool-bar-item {
	width: 108px;
	height: 100%;
	position: relative;
	--bar-color: #7699b9;

	.container {
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
	}

	.icon {
		width: 24px;
		height: 24px;

		svg {
			width: 100%;
			height: 100%;
			fill: var(--bar-color);
		}
	}

	.label {
		margin-left: 4px;
		font-family: Alibaba PuHuiTi 0;
		font-size: 12px;
		color: var(--bar-color);
	}

	&:not(:last-child) {
		&::after {
			content: "";
			position: absolute;
			right: 0;
			top: 50%;
			transform: translateY(-50%);
			height: 20px;
			width: 0px;
			border-right: 1px dashed #41586c;
		}
	}

	&.is-panel {
		--bar-color: #3168ff;

		.panel {
			opacity: 1;
			pointer-events: initial;
		}
	}

	.panel {
		position: absolute;
		bottom: calc(100% + 8px);
		left: 50%;
		transform: translateX(-50%);
		width: 88px;
		height: 121px;
		background: rgba(9, 16, 27, 0.9);
		box-shadow: 4px 8px 14px 0px rgba(0, 0, 0, 0.05);
		border-radius: 4px 4px 4px 4px;
		border: 1px solid #242e39;
		pointer-events: none;
		opacity: 0;
		transition: opacity 300ms;
		will-change: opacity;

		&-content {
			padding: 8px;
			display: flex;
			flex-direction: column;
			row-gap: 8px;
		}

		&-label {
			height: 17px;
			font-family: Alibaba PuHuiTi;
			font-size: 12px;
			color: #ffffff;
			text-align: left;
		}

		&-item {
			height: 14px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			cursor: pointer;
			--item-color: #7699b9;

			&-line {
				width: 14px;
				border: 2px solid transparent;

				&.is-green {
					border-color: #00ff2b;
				}

				&.is-yellow {
					border-color: #fadd00;
				}

				&.is-red {
					border-color: #ff0000;
				}

				&.is-deepRed {
					border-color: #440001;
				}
			}

			&-icon {
				width: 14px;
				height: 14px;

				svg {
					width: 100%;
					height: 100%;
					fill: var(--item-color);
				}
			}

			&-label {
				width: 50px;
				font-family: Alibaba PuHuiTi;
				font-size: 10px;
				color: var(--item-color);
				text-align: left;
			}

			&.is-disabled {
				cursor: default;

				.panel-item-label {
					color: rgba(255, 255, 255, 0.7);
				}
			}

			&.is-active {
				--item-color: #3168ff;
			}
		}
	}
}
</style>

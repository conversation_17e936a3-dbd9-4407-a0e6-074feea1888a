<template>
  <div class="map">
    <EngineContainer ref="engineContainer" :initialType="modelValue" />
  </div>
</template>

<script setup>
import { ref, watch, shallowRef } from 'vue';
import EngineContainer from './EngineContainer.vue';

const props = defineProps({
  modelValue: {
    type: String,
    default: 'maplibre',
  },
});

const emit = defineEmits(['update:modelValue']);

const engineContainer = shallowRef(null);

const currentEngine = ref(props.modelValue);

watch(
  () => props.modelValue,
  async (newValue, oldValue) => {
    if (newValue !== oldValue && newValue !== currentEngine.value) {
      await switchEngine(newValue);
    }
  },
  { immediate: false }
);

// 切换到指定引擎
async function switchEngine(type) {
  try {
    const result = await engineContainer.value.switchToEngine(type);
    if (result) {
      // 成功切换后更新当前引擎状态
      currentEngine.value = type;
      console.log(`成功切换到${type}引擎`);
    }
    return result;
  } catch (error) {
    console.error(`切换到${type}引擎失败:`, error);
    return false;
  }
}

// 获取指定类型的引擎实例
function getEngineInstance(type) {
  if (!engineContainer.value) {
    console.error('引擎容器未初始化');
    return null;
  }

  return engineContainer.value.getActiveEngine();
}

defineExpose({
  switchEngine,
  engineContainer,
  getEngineInstance,
});
</script>

<style lang="scss">
.map {
  position: absolute;
  z-index: 1;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
}
</style>

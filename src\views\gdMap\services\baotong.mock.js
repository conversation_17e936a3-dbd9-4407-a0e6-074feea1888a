import axios from 'axios';

export async function getAverageSpeed() {
  const response = await axios.get('/mock/baotong/averageSpeed.json');
  return response.data;
}

export async function getTrafficLight() {
  const response = await axios.get('/mock/baotong/trafficLight.json');
  return response.data;
}

export async function getIncidentMonitor() {
  const response = await axios.get('/mock/baotong/incidentMonitor.json');
  return response.data;
}

export async function getEquipmentOnline() {
  const response = await axios.get('/mock/baotong/equipmentOnline.json');
  return response.data;
}

export async function getTrafficJam() {
  const response = await axios.get('/mock/baotong/trafficJam.json');
  return response.data;
}

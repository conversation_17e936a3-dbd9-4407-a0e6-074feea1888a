<template>
  <TrafficCard title="项目推进情况">
    <div class="projectadv-content">
      <div class="adv-list">
        <div class="adv-item" v-for="item in advList" :key="item.title">
          <img :src="item.icon" class="adv-icon" />
          <div class="adv-info">
            <div class="adv-title" :class="item.titleClass">{{ item.title }}</div>
            <div class="adv-value" :class="item.valueClass">
              {{ statusData[item.key] || 0 }}
            </div>
            <div class="adv-unit" :class="item.titleClass">{{ item.unit }}</div>
          </div>
        </div>
      </div>
      <div class="line"></div>
      <div class="project-chart-root">
        <div class="chart-header">
          <span class="chart-title">计划-实际进度分析</span>
          <span
            v-if="lineData[lineData.length - 1]?.progressStatus === '进度滞后'"
            class="chart-delay"
          >
            <span class="info-icon">i</span>
            <span class="delay-text">进度滞后</span>
          </span>
        </div>
        <ProjectChart :data="lineData" />
      </div>
    </div>
    <template #extra>
      <div class="link" @click="onInfo">进度明细 <icon-double-right /></div>
    </template>
  </TrafficCard>

  <ProjectProgressModal ref="ProjectProgressModalRef" />
</template>

<script setup>
import request from "@/utils/request";
import zcz from "@/assets/images/people/zcz.svg";
import zwcz from "@/assets/images/people/zwcz.svg";
import sygcl from "@/assets/images/people/sygcl.svg";
import ProjectChart from "./components/ProjectChart.vue";
import ProjectProgressModal from "./components/ProjectProgressModal.vue";
import emitter from "@/utils/emitter";
const advList = [
  {
    icon: zcz,
    title: "总产值",
    key: "totalNum",
    unit: "万",
    titleClass: "adv-title1",
    valueClass: "adv-value1",
  },
  {
    icon: zwcz,
    title: "完成产值",
    key: "completedNum",
    unit: "万",
    titleClass: "adv-title2",
    valueClass: "adv-value2",
  },
  {
    icon: sygcl,
    title: "剩余工程量",
    key: "remainNum",
    unit: "万",
    titleClass: "adv-title3",
    valueClass: "adv-value3",
  },
];

const statusData = ref({});
const lineData = ref([]);
const ProjectProgressModalRef = ref(null);
const currentDepartmentId = ref(null);

onMounted(() => {
  // 监听区域双击事件
  emitter.$on("division-area-dblclick", handleAreaChange);
  getData();
});

onBeforeUnmount(() => {
  emitter.$off("division-area-dblclick", handleAreaChange);
});

const handleAreaChange = (eventData) => {
  console.log("项目推进情况 - 接收到区域双击事件:", eventData);
  currentDepartmentId.value = eventData.properties.id;
  getData();
};

const getData = () => {
  getStatus();
  getLine();
};

const getStatus = () => {
  const params = currentDepartmentId.value ? { departmentId: currentDepartmentId.value } : {};
  request.get("/api/screen/board/left/project/schedule/stat", params).then((res) => {
    if (res.code === 200) {
      statusData.value = res.data;
    }
  });
};

const getLine = () => {
  const params = currentDepartmentId.value ? { departmentId: currentDepartmentId.value } : {};
  request.get("/api/screen/board/left/project/schedule/line", params).then((res) => {
    if (res.code === 200) {
      lineData.value = res.data;
    }
  });
};

function onInfo(item) {
  ProjectProgressModalRef.value?.open();
}
</script>

<style scoped lang="scss">
.projectadv-content {
  height: 270px;
  display: flex;
  flex-direction: column;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;

  .adv-list {
    width: 100%;
    display: flex;
    justify-content: space-around;
  }

  .adv-item {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .adv-icon {
    width: 38px;
    height: 38px;
  }

  .adv-info {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    align-items: flex-start;
  }

  .adv-title {
    font-family: Alibaba PuHuiTi;
    font-weight: normal;
    font-size: 12px;
    margin-bottom: 4px;
  }

  .adv-title1 {
    color: #f5e6cb;
  }

  .adv-title2 {
    color: #c3fded;
  }

  .adv-title3 {
    color: #c4e5ff;
  }

  .adv-value {
    font-family: D-DIN-PRO, D-DIN-PRO;
    font-weight: bold;
    font-size: 18px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .adv-value1 {
    color: #f5e6cb;
    text-shadow: 0px 0px 10px rgba(255, 157, 0, 0.25);
    background: -webkit-linear-gradient(top, #f7fdfd 0%, rgba(255, 157, 0, 0.25) 100%);
    background: linear-gradient(to bottom, #f7fdfd 0%, rgba(255, 157, 0, 0.25) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .adv-value2 {
    color: #c3fded;
    text-shadow: 0px 0px 10px rgba(0, 255, 187, 0.25);
    background: -webkit-linear-gradient(top, #f7fdfd 0%, rgba(0, 255, 187, 0.25) 100%);
    background: linear-gradient(to bottom, #f7fdfd 0%, rgba(0, 255, 187, 0.25) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .adv-value3 {
    color: #c4e5ff;
    text-shadow: 0px 0px 10px rgba(0, 198, 255, 0.25);
    background: -webkit-linear-gradient(top, #f7fdfd 0%, rgba(0, 198, 255, 0.25) 100%);
    background: linear-gradient(to bottom, #f7fdfd 0%, rgba(0, 198, 255, 0.25) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .adv-unit {
    font-family: Alibaba PuHuiTi;
    font-weight: normal;
    font-size: 10px;
  }

  .line {
    margin-top: 12px;
    width: 100%;
    height: 0px;
    box-shadow: inset 0px 0px 8px 0px rgba(1, 86, 127, 0.4);
    border-radius: 0px 0px 0px 0px;
    border: 1px solid;
    border-image: linear-gradient(
        90deg,
        rgba(0, 138, 255, 0),
        rgba(131, 198, 255, 1),
        rgba(0, 138, 255, 0)
      )
      1 1;
  }

  .project-chart-root {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    background: transparent;
    position: relative;
  }

  .chart-header {
    position: absolute;
    top: 12px;
    left: 12px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 2px;

    .chart-title {
      font-family: Alibaba PuHuiTi;
      font-size: 12px;
      color: #c4e5ff;
      // font-weight: bold;
      margin-right: 18px;
    }

    .chart-delay {
      display: flex;
      align-items: center;
      margin-left: 8px;

      .info-icon {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #ca335c;
        color: #244a6a;
        font-family: Alibaba PuHuiTi;
        font-size: 12px;
        text-align: center;
        line-height: 12px;
        margin-right: 4px;
        font-style: normal;
        font-family: inherit;
      }

      .delay-text {
        font-size: 12px;
        color: #ca335c;
      }
    }
  }
}
</style>

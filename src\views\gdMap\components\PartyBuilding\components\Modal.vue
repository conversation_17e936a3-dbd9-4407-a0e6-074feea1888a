<template>
	<a-modal
		v-model:visible="visible"
		:modal-class="['party-building-modal', props.size]"
		popup-container="#teleport-container"
		:mask-style="{
			'backdrop-filter': 'blur(12px)',
			'background-color': props.mask ? '' : 'transparent',
		}"
		:mask-closable="false"
		simple
		:footer="false"
	>
		<template #title>
			<div class="title">
				{{ props.title }}
				<div class="divider" />
			</div>
			<modelClose class="icon-close" @click="close" />
		</template>
		<slot />
	</a-modal>
</template>

<script setup>
import modelClose from "@/assets/modules/PartyBuilding/icon/modal-close.svg?component";
const props = defineProps({
	title: {
		type: String,
		default: "",
	},
	size: {
		type: String,
		default: "medium",
	},
	mask: {
		type: Boolean,
		default: true,
	},
});
const visible = ref(false);

const close = () => {
	visible.value = false;
};

const open = () => {
	visible.value = true;
};

defineExpose({ open });
</script>

<style lang="scss">
.party-building-modal {
	&.arco-modal {
		height: auto;
		aspect-ratio: calc(945 / 686);
		padding: 0;
		background-image: url("@/assets/modules/PartyBuilding/bg/modal-bg.svg");
		background-size: 100% 100%;
		background-color: transparent;
		display: inline-flex;
		flex-direction: column;

		&.medium {
			width: 945px;

			.arco-modal-header {
				padding-top: 18px;
				padding-left: 24px;
				padding-right: 12px;
				margin-bottom: 30px;
			}

			.arco-modal-body {
				padding: 0 24px 46px 24px;
			}
		}

		&.large {
			width: 1128px;

			.arco-modal-header {
				padding-top: 24px;
				padding-left: 32px;
				padding-right: 16px;
				margin-bottom: 30px;
			}

			.arco-modal-body {
				padding: 0 32px 50px 32px;
			}
		}

		.arco-modal-body {
			flex: 1;
			display: flex;
			flex-direction: column;
		}

		.arco-modal-title {
			font-family: Alibaba PuHuiTi;
			font-size: 24px;
			line-height: 24px;
			color: #ffffff;
			text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
			justify-content: space-between;

			.title {
				position: relative;

				.divider {
					position: absolute;
					left: 0;
					bottom: -8px;
					width: 100%;
					height: 0;
					border-radius: 0px 0px 0px 0px;
					border: 1px solid;
					border-image: linear-gradient(
							90deg,
							rgba(255, 255, 255, 0),
							rgba(255, 255, 255, 1),
							rgba(255, 255, 255, 0)
						)
						1 1;
				}
			}

			.icon-close {
				width: 30px;
				height: 30px;
				cursor: pointer;
			}
		}

		.tab {
			width: 100%;
			height: 40px;
			background: #74190f;
			display: flex;
			flex-direction: row;

			&-item {
				width: 180px;
				height: 100%;
				font-family: Alibaba PuHuiTi;
				font-size: 18px;
				color: #f8cac5;
				line-height: 21px;
				letter-spacing: 4px;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;

				&.is-active {
					background: linear-gradient(
						180deg,
						rgba(250, 236, 1, 0.35) 0%,
						rgba(250, 236, 1, 0) 100%
					);
					color: #ffffff;
					position: relative;

					&::after {
						position: absolute;
						content: "";
						top: 0;
						left: 0;
						width: 100%;
						height: 2px;
						background-color: #edcb04;
					}
				}
			}
		}

		.table {
			width: 100%;
			flex: 1;
			overflow: hidden;
		}

		.arco-table-container {
			border: none;

			.arco-table-header {
				position: relative;
				background: none;
				border-radius: 0;
				border: 1px solid;
				border-image: linear-gradient(90deg, rgba(255, 63, 0, 1), rgba(255, 63, 0, 0)) 1 1;

				.arco-table-tr {
					background-image: linear-gradient(
						90deg,
						rgba(223, 42, 27, 1) 0%,
						rgba(223, 42, 27, 0) 100%
					);
					background-repeat: no-repeat;

					.arco-table-th {
						background-color: transparent;
						border: none;

						.arco-table-th-title {
							font-family: Alibaba PuHuiTi;
							font-size: 18px;
							color: #f8cac5;
						}
					}
				}
			}

			.arco-table-body {
				background: none;

				table {
					border-collapse: separate;
					border-spacing: 0 12px;
				}

				.arco-table-tr {
					.arco-table-td {
						line-height: 1em;
						background-color: transparent;
						border: none;
						border-bottom: 1px solid;
						border-image: linear-gradient(180deg, rgba(255, 180, 44, 0), rgba(255, 180, 44, 1)) 1 1;
						background: linear-gradient(
								180deg,
								rgba(235, 156, 0, 0) 47%,
								rgba(235, 156, 0, 0.5) 100%
							),
							rgba(210, 145, 18, 0.05);
						background-repeat: no-repeat;

						&:first-of-type {
							border-left: 1px solid;
						}

						&:last-of-type {
							border-right: 1px solid;
						}

						.arco-table-td-content {
							font-family: Alibaba PuHuiTi;
							font-size: 18px;
							background-image: linear-gradient(
								180deg,
								rgba(255, 249, 222, 1) 40%,
								rgba(255, 102, 0, 1) 77%
							);
							background-clip: text;
							-webkit-background-clip: text;
							-webkit-text-fill-color: transparent;
						}
					}
				}
			}
		}

		.news {
			flex: 1;
			display: flex;
			flex-direction: column;
			row-gap: 12px;

			&-item {
				height: 40px;
				border: 1px solid;
				border-image: linear-gradient(180deg, rgba(255, 64, 44, 0), rgba(255, 64, 44, 1)) 1 1;
				background: linear-gradient(180deg, rgba(200, 54, 35, 0) 47%, #c83623 100%),
					rgba(172, 69, 56, 0.05);
				background-repeat: no-repeat;
				display: flex;
				flex-direction: row;
				align-items: center;
				padding-left: 32px;
				padding-right: 24px;
				font-family: Alibaba PuHuiTi;
				font-size: 18px;

				&:hover {
					border-image: linear-gradient(180deg, rgba(255, 180, 44, 0), rgba(255, 180, 44, 1)) 1 1;
					background: linear-gradient(180deg, rgba(235, 156, 0, 0) 47%, #eb9c00 100%),
						rgba(210, 145, 18, 0.05);
					background-repeat: no-repeat;
				}

				&-time {
					background-image: linear-gradient(
						180deg,
						rgba(255, 249, 222, 1) 40%,
						rgba(255, 102, 0, 1) 77%
					);
					background-clip: text;
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
					white-space: nowrap;
				}

				&-desc {
					flex: 1;
					margin-left: 40px;
					color: #ffffff;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}

				&-link {
					margin-left: 48px;
					display: flex;
					flex-direction: row;
					align-items: center;
					cursor: pointer;
				}
			}
		}
	}
}
</style>

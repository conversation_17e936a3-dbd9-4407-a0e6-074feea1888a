<template>
	<div class="tool-bar" :class="{ 'is-collapsed': isCollapsed }">
		<div class="tool-bar-button" @click="resetView" title="视角恢复">
			<img
				:src="viewpointHover ? viewpointActiveIcon : viewpointIcon"
				alt="视角恢复"
				class="icon-img"
			/>
		</div>
		<!-- <div
      class="tool-bar-button"
      @click="toggleSidebars"
      :title="isCollapsed ? '全局展开' : '全局收起'"
      @mouseenter="collapseHover = true"
      @mouseleave="collapseHover = false"
    >
      <img
        :src="isCollapsed || collapseHover ? collapseActiveIcon : collapseIcon"
        :alt="isCollapsed ? '全局展开' : '全局收起'"
        class="icon-img"
      />
    </div> -->
	</div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import emitter from "@/utils/emitter";

// 导入图标
import viewpointIcon from "@/assets/modules/common/icons/viewpoint-rest.svg";
import viewpointActiveIcon from "@/assets/modules/common/icons/viewpoint-rest-active.svg";
import collapseIcon from "@/assets/modules/common/icons/global-collapse.svg";
import collapseActiveIcon from "@/assets/modules/common/icons/global-collapse-active.svg";

const isCollapsed = ref(false);
const viewpointHover = ref(false);
const collapseHover = ref(false);

// 重置视角
const resetView = () => {
	emitter.$emit("resetMapView");
};

// 切换侧边栏显示状态
const toggleSidebars = () => {
	isCollapsed.value = !isCollapsed.value;
	emitter.$emit("toggleSidebars", isCollapsed.value);
};

// 监听来自 index.vue 的状态更新
onMounted(() => {
	emitter.$on("uiElementsToggled", (isVisible) => {
		isCollapsed.value = !isVisible;
	});
});

onUnmounted(() => {
	emitter.$off("uiElementsToggled");
});
</script>

<style lang="scss" scoped>
.tool-bar {
	display: flex;
	flex-direction: column;
	gap: 16px;
	pointer-events: auto;
	transition: right 0.5s cubic-bezier(0.23, 1, 0.32, 1);
	&.is-collapsed {
		right: 50px;
	}
}

.tool-bar-button {
	width: 40px;
	height: 40px;
	border-radius: 4px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s;

	.icon-img {
		width: 100%;
		height: 100%;
	}
}
</style>

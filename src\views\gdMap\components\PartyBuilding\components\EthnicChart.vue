<template>
	<v-chart ref="chartRef" class="chart" :option="option" autoresize @mouseover="onMouseover" />
</template>

<script setup>
import * as echarts from "echarts";
import VChart from "vue-echarts";

const props = defineProps({
	data: {
		type: Array,
		default: [],
	},
});

const chartRef = ref(null);
const option = ref(null);

onMounted(() => {
	setOptions();
});

watch(
	() => props.data,
	() => setOptions()
);

const setOptions = () => {
	option.value = {
		series: [
			{
				type: "pie",
				radius: ["68%", "98%"],
				label: {
					show: false,
					fontFamily: "Alibaba PuHuiTi",
					fontSize: 14,
					fontWeight: 500,
					color: "#FFF9DE",
					formatter: ({ name, value }) => {
						return [`{name|${name} }`, `{value|${value}} {unit|人}`].join("\n");
					},
					rich: {
						name: {
							padding: [0, 0, 4, 0],
						},
						value: {
							fontSize: 30,
							fontWeight: "bold",
							verticalAlign: "bottom",
						},
						unit: {
							verticalAlign: "bottom",
							padding: [0, 0, 4, 0],
						},
					},
					position: "center",
				},
				emphasis: {
					scaleSize: 1,
					label: {
						show: true,
					},
				},
				itemStyle: {
					color: ({ dataIndex }) => {
						return [
							new echarts.graphic.LinearGradient(0, 0, 0, 1, [
								{ offset: 0, color: "red" },
								{ offset: 1, color: "orange" },
							]),
							new echarts.graphic.LinearGradient(0, 0, 0, 1, [
								{ offset: 0, color: "red" },
								{ offset: 1, color: "orange" },
							]),
							new echarts.graphic.LinearGradient(0, 0, 0, 1, [
								{ offset: 0, color: "red" },
								{ offset: 1, color: "orange" },
							]),
							new echarts.graphic.LinearGradient(0, 0, 0, 1, [
								{ offset: 0, color: "red" },
								{ offset: 1, color: "orange" },
							]),
							new echarts.graphic.LinearGradient(0, 0, 0, 1, [
								{ offset: 0, color: "red" },
								{ offset: 1, color: "orange" },
							]),
						][dataIndex];
					},
				},
				data: props.data.map(({ name, value }) => ({ name, value })),
			},
		],
	};
	nextTick(() => {
		chartRef.value.dispatchAction({
			type: "highlight",
			seriesIndex: 0,
			dataIndex: 0,
		});
	});
};

const onMouseover = (e) => {
	chartRef.value.dispatchAction({
		type: "downplay",
		seriesIndex: 0,
	});
	chartRef.value.dispatchAction({
		type: "highlight",
		seriesIndex: 0,
		dataIndex: e.dataIndex,
	});
};
</script>

<style lang="scss" scoped>
.chart {
	width: 100%;
	height: 100%;
}
</style>

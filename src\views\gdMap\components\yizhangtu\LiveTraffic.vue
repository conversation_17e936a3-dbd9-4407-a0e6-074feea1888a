<template>
	<div class="traffic-card">
		<a-spin class="card-loading" dot :loading="loading">
			<BasicCard title="实况交通">
				<div class="card-content">
					<!-- 顶部指标区域 -->
					<div class="traffic-indicators">
						<div class="indicator-item">
							<div class="indicator-icon">
								<img
									src="@/assets/modules/yizhangtu/icons/total-congestion-index.png"
									alt="拥堵指数"
								/>
							</div>
							<div class="indicator-content">
								<div class="indicator-label">全程拥堵指数</div>
								<div class="indicator-value">
									{{ trafficData.totalCongestionIndex }}
								</div>
							</div>
						</div>
						<div class="indicator-item">
							<div class="indicator-icon">
								<img
									src="@/assets/modules/yizhangtu/icons/total-congestion-mileage.png"
									alt="拥堵里程"
								/>
							</div>
							<div class="indicator-content">
								<div class="indicator-label">全程拥堵里程</div>
								<div class="indicator-value">
									{{ trafficData.totalCongestionDistance }}<span class="unit">km</span>
								</div>
							</div>
						</div>
					</div>

					<!-- 各分区拥堵指数 -->
					<div class="traffic-section">
						<div class="traffic-section-left">
							<div class="section-title">
								{{ currentArea === "all" ? "各分区拥堵指数" : "各区段拥堵指数" }}
							</div>
							<div ref="listContainerRef" class="scroll-container">
								<div class="section-list">
									<div class="section-item" v-for="(item, index) in congestionData" :key="index">
										<div class="color-dot" :style="{ backgroundColor: item.color }"></div>
										<div class="section-name">
											<a-tooltip
												:content="currentArea === 'all' ? item.area : item.section || item.area"
											>
												<div class="section-name-text">
													{{ currentArea === "all" ? item.area : item.section || item.area }}
												</div>
											</a-tooltip>
										</div>
										<div class="section-value" :style="{ color: item.color }">
											{{ item.congestionIndex }}
										</div>
									</div>
								</div>
							</div>
						</div>

						<!-- 各分区平均速度 -->
						<div class="traffic-section-right">
							<div class="section-title">
								{{ currentArea === "all" ? "各分区平均速度" : "各区段平均速度" }}
							</div>
							<div class="chart-container">
								<div class="chart-nav-btn left" @click="prevChart">
									<icon-left />
								</div>
								<v-chart ref="chartRef" class="speed-chart" :option="chartOption" autoresize />
								<div class="chart-nav-btn right" @click="nextChart">
									<icon-right />
								</div>
							</div>
						</div>
					</div>
				</div>
			</BasicCard>
		</a-spin>
	</div>
</template>

<script setup>
import BasicCard from "@/components/BasicCard/index.vue";
import { ref, reactive, computed, watch, onMounted, onBeforeUnmount } from "vue";
import { useMarquee } from "@/hooks/useMarquee";
import { use } from "echarts/core";
import { BarChart } from "echarts/charts";
import { GridComponent, TooltipComponent, LegendComponent } from "echarts/components";
import { CanvasRenderer } from "echarts/renderers";
import { IconLeft, IconRight } from "@arco-design/web-vue/es/icon";
import { Tooltip as ATooltip } from "@arco-design/web-vue";
import VChart from "vue-echarts";
import emitter from "@/utils/emitter";
import {
	getLiveTrafficOverview,
	getLiveTrafficCongestionData,
	getLiveTrafficSpeedData,
} from "../../services/yizhangtu.mock.js";

// 当前区域类型
const currentArea = ref("all");

// 交通概览数据
const trafficData = reactive({
	totalCongestionIndex: 0,
	totalCongestionDistance: 0,
});

// 初始化加载数据
async function initData() {
	loading.value = true;
	try {
		// 始终使用总览数据
		const dataKey = "all";

		// 获取交通概览数据
		const overviewResult = await getLiveTrafficOverview(dataKey);
		Object.assign(trafficData, overviewResult);

		// 获取拥堵数据列表
		const congestionResult = await getLiveTrafficCongestionData(dataKey);
		congestionData.value = congestionResult;

		// 获取速度数据
		const speedResult = await getLiveTrafficSpeedData(dataKey);
		speedData.value = speedResult;
	} catch (error) {
		console.error("加载实时交通数据失败:", error);
	} finally {
		loading.value = false;
	}
}

// 处理区域变化的函数
const handleAreaChange = async (value) => {
	// 更新当前区域
	currentArea.value = value;

	// 重新加载数据
	loading.value = true;
	try {
		// 获取交通概览数据
		const overviewResult = await getLiveTrafficOverview(currentArea.value);
		Object.assign(trafficData, overviewResult);

		// 获取拥堵数据列表
		const congestionResult = await getLiveTrafficCongestionData(currentArea.value);
		congestionData.value = congestionResult;

		// 获取速度数据
		const speedResult = await getLiveTrafficSpeedData(currentArea.value);
		speedData.value = speedResult;

		// console.log("speedData.value", speedData.value);
	} catch (error) {
		console.error("加载实时交通数据失败:", error);
	} finally {
		setTimeout(() => {
			loading.value = false;
		}, 1000); // 延迟一点时间确保DOM已更新
	}
};

// 组件挂载时初始化数据并监听事件
onMounted(() => {
	initData();
	emitter.$on("pageNavChange", handleAreaChange);
});

// 组件销毁前移除事件监听
onBeforeUnmount(() => {
	emitter.$off("pageNavChange", handleAreaChange);
});

// 拥堵数据列表
const congestionData = ref([]);

// 数据加载状态
const loading = ref(false);

// 注册必要的组件
use([BarChart, GridComponent, TooltipComponent, LegendComponent, CanvasRenderer]);

// 使用跑马灯效果
const { containerRef: listContainerRef, resetScroll } = useMarquee({
	speed: 10, // 滚动速度
	delay: 3000, // 滚动到底部后停顿时间
	step: 1, // 每次滚动的像素
});

// 当拥堵数据变化时，重置滚动
watch(
	congestionData,
	() => {
		setTimeout(resetScroll, 300); // 延迟一点时间确保DOM已更新
	},
	{ deep: true }
);

// 各区平均速度数据
const speedData = ref([]);

// 当前显示的起始索引
const currentIndex = ref(0);
// 每页显示的数量
const pageSize = 3;

// 图表配置
const chartOption = computed(() => {
	// 如果没有数据，返回空配置
	if (!speedData.value || speedData.value.length === 0) {
		return {
			color: ["#0084FF", "#FFD43A"],
			tooltip: {
				trigger: "axis",
				axisPointer: {
					type: "shadow",
				},
				backgroundColor: "rgba(10, 35, 75, 0.8)",
				borderColor: "#174272",
				borderWidth: 1,
				textStyle: {
					color: "#ffffff",
					fontSize: 10, // 缩小文字大小
				},
				padding: [5, 8], // 缩小内边距
				extraCssText: "max-width: 150px; white-space: normal;", // 限制最大宽度
				formatter: function (params) {
					let result = `${params[0].name}<br/>`;
					params.forEach((item) => {
						result += `${item.seriesName}: ${item.value} km/h<br/>`;
					});
					return result;
				},
			},
			legend: {
				data: ["限制速度", "平均速度"],
				top: 0,
				left: "center",
				itemWidth: 6,
				itemHeight: 6,
				icon: "circle",
				textStyle: {
					color: "#ffffff",
					fontSize: 10,
				},
				itemGap: 20,
			},
			grid: {
				top: "30px",
				left: "3%",
				right: "3%",
				bottom: "3%",
				containLabel: true,
			},
			xAxis: {
				type: "category",
				data: [],
				axisLine: {
					lineStyle: {
						color: "#fff",
					},
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					color: "#ffffff",
					fontSize: 10,
				},
			},
			yAxis: {
				type: "value",
				axisLine: {
					show: false,
				},
				axisTick: {
					show: false,
				},
				splitLine: {
					lineStyle: {
						color: "rgba(255, 255, 255, 0.15)",
						type: "dashed",
					},
				},
				axisLabel: {
					color: "#ffffff",
					fontSize: 10,
				},
				minInterval: 20, // 设置最小间隔为20
				// splitNumber: 3, // 控制分割段数
			},
			series: [
				{
					name: "限制速度",
					type: "bar",
					barWidth: 6,
					data: [],
				},
				{
					name: "平均速度",
					type: "bar",
					barWidth: 6,
					data: [],
				},
			],
		};
	}

	// 获取当前页的数据
	const currentData = speedData.value.slice(currentIndex.value, currentIndex.value + pageSize);

	// 根据当前区域类型调整显示字段
	const nameField = currentArea.value === "all" ? "name" : "section";

	// 计算数据中的最大值，用于设置Y轴最大值
	const maxValue = Math.max(
		...currentData.map((item) => Math.max(item.limitSpeed || 0, item.avgSpeed || 0))
	);
	// 向上取整到最接近的20的倍数
	const maxAxisValue = Math.ceil(maxValue / 20) * 20;

	return {
		color: ["#0084FF", "#FFD43A"],
		tooltip: {
			trigger: "axis",
			axisPointer: {
				type: "shadow",
			},
			backgroundColor: "rgba(10, 35, 75, 0.8)",
			borderColor: "#174272",
			borderWidth: 1,
			textStyle: {
				color: "#ffffff",
				fontSize: 10, // 缩小文字大小
			},
			padding: [5, 8], // 缩小内边距
			extraCssText: "max-width: 150px; white-space: normal;", // 限制最大宽度
			formatter: function (params) {
				let result = `${params[0].name}<br/>`;
				params.forEach((item) => {
					result += `${item.seriesName}: ${item.value} km/h<br/>`;
				});
				return result;
			},
		},
		legend: {
			data: ["限制速度", "平均速度"],
			top: 0,
			left: "center",
			itemWidth: 6,
			itemHeight: 6,
			icon: "circle",
			textStyle: {
				color: "#ffffff",
				fontSize: 10,
			},
			itemGap: 20,
		},
		grid: {
			top: "30px",
			left: "3%",
			right: "3%",
			bottom: "3%",
			containLabel: true,
		},
		xAxis: {
			type: "category",
			data: currentData.map((item) => item[nameField] || item.name),
			axisLine: {
				lineStyle: {
					color: "#fff",
				},
			},
			axisTick: {
				show: false,
			},
			axisLabel: {
				color: "#ffffff",
				fontSize: 10,
				interval: 0,

				formatter: function (value) {
					if (value.length > 5) {
						return value.substring(0, 5);
					}
					return value;
				},
				overflow: "truncate", // 截断过长文本
				width: 60, // 限制宽度
				rich: {
					tooltip: {
						backgroundColor: "rgba(10, 35, 75, 0.8)",
						color: "#fff",
						padding: [4, 8],
						borderRadius: 4,
						borderColor: "#174272",
						borderWidth: 1,
					},
				},
			},
		},
		yAxis: {
			type: "value",
			axisLine: {
				show: false,
			},
			axisTick: {
				show: false,
			},
			splitLine: {
				lineStyle: {
					color: "rgba(255, 255, 255, 0.15)",
					type: "dashed",
				},
			},
			axisLabel: {
				color: "#ffffff",
				fontSize: 10,
			},
			max: maxAxisValue, // 设置最大值
			minInterval: 20, // 设置最小间隔为20
			// splitNumber: 3, // 控制分割段数
		},
		series: [
			{
				name: "限制速度",
				type: "bar",
				barWidth: 6,
				data: currentData.map((item) => item.limitSpeed),
			},
			{
				name: "平均速度",
				type: "bar",
				barWidth: 6,
				data: currentData.map((item) => item.avgSpeed),
			},
		],
	};
});

// 下一页
const nextChart = () => {
	if (!speedData.value || speedData.value.length === 0) return;

	if (currentIndex.value + pageSize < speedData.value.length) {
		currentIndex.value += 1;
		if (currentIndex.value + pageSize > speedData.value.length) {
			currentIndex.value = speedData.value.length - pageSize;
		}
	}
};

// 上一页
const prevChart = () => {
	if (!speedData.value || speedData.value.length === 0) return;

	if (currentIndex.value > 0) {
		currentIndex.value -= 1;
	}
};
</script>

<style lang="scss" scoped>
.traffic-card {
	height: 244px;
	display: flex;
	flex-direction: column;

	.card-content {
		margin-top: 8px;

		display: flex;
		flex-direction: column;
		width: 100%;
		background: rgba(10, 35, 75, 0.6);
		border: 1px solid #174272;
	}

	// 顶部指标区域
	.traffic-indicators {
		padding: 12px 0 12px 10px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		.indicator-item {
			display: flex;
			align-items: center;
			height: 32px;

			.indicator-icon {
				width: 32px;
				height: 100%;
				margin-right: 3px;
				img {
					width: 100%;
					height: 100%;
				}
			}

			.indicator-content {
				display: flex;
				align-items: center;
				justify-content: space-between;
				.indicator-label {
					margin-right: 2px;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-size: 12px;
					color: #ffffff;
					white-space: nowrap;
				}
				.indicator-value {
					font-family: D-DIN-PRO-Bold, D-DIN-PRO;
					font-weight: bold;
					font-size: 16px;
					color: #48d6ff;
					.unit {
						font-family: Source Han Sans CN-Regular, Source Han Sans CN;
						font-size: 10px;
						color: #99adcd;
					}
				}
			}
		}
	}

	// 各分区数据区域
	.traffic-section {
		padding: 0 10px 12px 10px;
		display: flex; // 添加flex布局
		justify-content: space-between; // 两侧对齐
		flex: 1;
		overflow: hidden;
		&-left,
		&-right {
			display: flex;
			flex-direction: column;
			width: 50%; // 各占48%宽度，留出一点间距

			box-sizing: border-box;
		}

		.section-title {
			font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
			font-size: 16px;
			color: #ffffff;
			text-shadow: 0px 0px 9px #158eff;
		}

		.section-list {
			flex: 1;
			overflow: auto;
			display: flex;
			flex-direction: column;

			.section-item {
				display: flex;
				align-items: center;
				height: 24px;
				width: 145px;
				position: relative;

				&::after {
					content: "";
					position: absolute;
					left: 0;
					right: 0;
					bottom: 0;
					height: 1px;
					background: rgba(41, 147, 251, 0.3);
					transform: scaleY(0.5);
					transform-origin: 0 100%;
				}

				&:last-child {
					&::after {
						display: none;
					}
				}

				.color-dot {
					width: 6px;
					height: 6px;
					border-radius: 1px;
					margin-right: 4px;
				}

				.section-name {
					flex: 1;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-size: 12px;
					color: #ffffff;
					cursor: pointer;
					overflow: hidden;
					&-text {
						width: 100%;
						text-overflow: ellipsis;
						white-space: nowrap;
						overflow: hidden;
					}
				}

				.section-value {
					font-family: DINPro-Regular, DINPro;

					font-size: 14px;
					color: #ff4042;
					line-height: 16px;
					text-align: right;
				}
			}
		}

		.chart-container {
			height: 100%;
			position: relative;

			.speed-chart {
				padding: 0 14px;
				width: 100%;
				height: 100%;
				box-sizing: border-box;
			}

			.chart-nav-btn {
				position: absolute;
				top: 50%;
				transform: translateY(-50%);
				width: 12px;
				height: 12px;
				background: rgba(10, 35, 75, 0.8);
				border: 1px solid #174272;
				border-radius: 2px;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;
				z-index: 10;
				color: #ffffff;

				&:hover {
					background: rgba(23, 66, 114, 0.8);
				}

				&.left {
					left: 0;
				}

				&.right {
					right: 0;
				}
			}
		}
	}
}

.scroll-container {
	height: 100%; // 容器需要有固定高度
	overflow: hidden; // 重要：设置为hidden，由hooks控制滚动
	position: relative; // 相对定位
}
</style>

<svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#229;&#186;&#148;&#229;&#143;&#152;&#231;&#155;&#145;&#230;&#181;&#139;">
<g id="Ellipse 15" filter="url(#filter0_d_342_3847)">
<circle cx="10.9956" cy="11.2041" r="8" fill="url(#paint0_radial_342_3847)" fill-opacity="0.7"/>
<circle cx="10.9956" cy="11.2041" r="7.55492" stroke="url(#paint1_linear_342_3847)" stroke-width="0.890162"/>
</g>
<circle id="Ellipse 16" cx="10.9954" cy="11.2039" r="7.01097" fill="url(#paint2_radial_342_3847)" stroke="url(#paint3_linear_342_3847)" stroke-width="0.200286"/>
<g id="Group 1321315401" filter="url(#filter1_d_342_3847)">
<g id="Frame" clip-path="url(#clip0_342_3847)">
<path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M6.24573 11.2021C6.24573 13.8277 8.3702 15.9521 10.9957 15.9521C13.6012 15.9521 15.7457 13.8277 15.7457 11.2021C15.7457 8.59666 13.6012 6.45215 10.9957 6.45215C8.3702 6.45215 6.24573 8.59666 6.24573 11.2021ZM7.04742 11.2021C7.04742 9.03759 8.81113 7.25384 10.9957 7.25384C13.1603 7.25384 14.944 9.01755 14.944 11.2021C14.944 13.3867 13.1803 15.1505 10.9957 15.1505C8.81113 15.1505 7.04742 13.3667 7.04742 11.2021ZM9.19178 10.5608L10.0937 9.49853L9.75296 8.6768L8.12954 10.5808L9.33208 11.8234L8.35001 12.9057L8.71077 13.7274L10.3743 11.8234L9.19178 10.5608ZM12.6591 10.5608L13.561 9.49853L13.2203 8.6768L11.5968 10.5808L12.7994 11.8234L11.8173 12.9057L12.1781 13.7274L13.8416 11.8234L12.6591 10.5608ZM10.9555 10.5608L11.8373 9.49853L11.5166 8.6768L9.89322 10.5808L11.0957 11.8234L10.1137 12.9057L10.4744 13.7274L12.1379 11.8234L10.9555 10.5608Z" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_342_3847" x="0.325119" y="0.533616" width="21.341" height="21.341" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.33524"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.388235 0 0 0 0 0.640523 0 0 0 0 0.807843 0 0 0 0.51 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_342_3847"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3847" result="shape"/>
</filter>
<filter id="filter1_d_342_3847" x="5.99573" y="6.20801" width="10" height="10.4451" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.445081"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.150327 0 0 0 0 0.247059 0 0 0 0 0.145098 0 0 0 0.75 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_342_3847"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3847" result="shape"/>
</filter>
<radialGradient id="paint0_radial_342_3847" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(10.9956 11.2041) scale(8)">
<stop stop-color="#D3D3D3"/>
<stop offset="0.03" stop-color="#D3D3D3" stop-opacity="0.9216"/>
<stop offset="0.13" stop-color="#D3D3D3" stop-opacity="0.7056"/>
<stop offset="0.23" stop-color="#D3D3D3" stop-opacity="0.5329"/>
<stop offset="0.35" stop-color="#D3D3D3" stop-opacity="0.4225"/>
<stop offset="0.47" stop-color="#D3D3D3" stop-opacity="0.3364"/>
<stop offset="0.6" stop-color="#D3D3D3" stop-opacity="0.2916"/>
<stop offset="0.76" stop-color="#D3D3D3" stop-opacity="0.2601"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.5"/>
</radialGradient>
<linearGradient id="paint1_linear_342_3847" x1="10.9956" y1="19.2041" x2="10.9956" y2="3.2041" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<radialGradient id="paint2_radial_342_3847" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(10.9954 11.2039) scale(7.11111)">
<stop stop-color="#D3D3D3"/>
<stop offset="1" stop-color="#0B103B" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint3_linear_342_3847" x1="10.9954" y1="18.315" x2="10.9954" y2="4.09277" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<clipPath id="clip0_342_3847">
<rect width="10" height="10" fill="white" transform="translate(5.99573 6.20801)"/>
</clipPath>
</defs>
</svg>

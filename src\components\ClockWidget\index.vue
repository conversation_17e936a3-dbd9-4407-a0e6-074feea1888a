<template>
	<div class="clock-widget">
		<div class="date">{{ dateStr }}</div>
		<div class="time">{{ timeStr }}</div>
	</div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from "vue";

const dateStr = ref("");
const timeStr = ref("");
let timer = null;

// 更新时间函数
const updateTime = () => {
	const now = new Date();

	// 格式化日期：YYYY-MM-DD
	const year = now.getFullYear();
	const month = String(now.getMonth() + 1).padStart(2, "0");
	const day = String(now.getDate()).padStart(2, "0");
	dateStr.value = `${year}-${month}-${day}`;

	// 格式化时间：HH:MM:SS
	const hours = String(now.getHours()).padStart(2, "0");
	const minutes = String(now.getMinutes()).padStart(2, "0");
	const seconds = String(now.getSeconds()).padStart(2, "0");
	timeStr.value = `${hours}:${minutes}:${seconds}`;
};

onMounted(() => {
	// 立即更新一次时间
	updateTime();

	// 设置定时器，每秒更新一次
	timer = setInterval(updateTime, 1000);
});

onBeforeUnmount(() => {
	// 组件销毁前清除定时器
	if (timer) {
		clearInterval(timer);
		timer = null;
	}
});
</script>

<style scoped lang="scss">
.clock-widget {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: end;
	font-family: D-DIN-PRO;
	font-size: 14px;
	color: var(--header-right-text-color);

	.date {
		font-size: 26px;
		font-weight: 500;
	}

	.time {
		font-size: 16px;
	}
}
</style>

<template>
  <div class="left-card">
    <m-card title="时程曲线" :height="323" :isRight="true">
      <template #extra>
        <m-select class="monitor-select" :options="pointOptions" :selected="selectedPonit" />
      </template>
      <div class="time-curve">
        <v-chart ref="vChart" :option="option" :autoresize="true" />
      </div>
    </m-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import * as echarts from 'echarts';
import VChart from 'vue-echarts';
import mCard from '@/components/mCard/index.vue';
import mSelect from '@/components/mSelect/index.vue';
import { getTimeCurve, getPointSelect } from '@/views/gdMap/services/dongtu.mock.js';

const option = ref();
const pointOptions = ref([]);
const selectedPonit = ref('');

onMounted(async () => {
  const defaultData = await getTimeCurve();
  const pointList = await getPointSelect();
  pointOptions.value = pointList.list;
  selectedPonit.value = pointList.list?.[0] || '';
  setOptions(defaultData.listData);
});

const setOptions = data => {
  option.value = {
    title: {
      text: '单位: m',
      left: '1.5%',
      top: '5%',
      textStyle: {
        color: '#A7BFDE',
        fontSize: 12,
      },
    },
    grid: {
      left: '12%',
      top: '15%',
      right: '2%',
      bottom: '15%',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
        shadowStyle: { opacity: 0.2 },
      },
      backgroundColor: 'rgba(0,0,0,1)',
      borderWidth: 1,
      borderColor: '#999999',
      textStyle: {
        color: '#ffffff',
        fontSize: 10,
      },
    },
    color: ['#6BC7F6'],
    xAxis: [
      {
        type: 'category',
        boundaryGap: true,
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)',
          },
        },
        axisTick: {
          show: true,
          alignWithLabel: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)',
          },
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          color: '#A7BFDE',
          fontSize: 10,
          padding: [0, 0, 0, 0],
        },
      },
    ],
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: ['rgba(255, 255, 255, 0.2)'],
          opacity: 1,
          width: 1,
        },
      },
      axisLabel: {
        color: '#A7BFDE',
        fontSize: 10,
      },
    },
    series: [
      {
        data: data,
        type: 'line',
        symbol:
          'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAQ5JREFUOE+1la1OQ0EQRs8kCFIQdVCCxIBpCK1AInkDHC+B4QkwvASub4GsaE0NGCRpKhGFVDQZ+Mjem9vNbkphmWTMfjNnd3b2x0iYuxuwH7wNbIewBfAGzORm5nG6ElfM3QU6AXZSkzXG3oEnMxO8thWgux8DR2tAsfxiZs/VYA38Jazi1NBvYCizv+HK4vCRyrfQgIsf7Nm6+bSnjwIeAGeZaGnXQDfoE+ABmGbixwKeAoeJAMHuv3w30ubATQb6KqDKjZPEuAXOMysZAncJbS7gJbCVEAdAKwP8AK4S2vJfgMVLLt6UDtAreWx0W8od7OJXryq16OPwR2j6+WpAyz2wDagape7vARt9AZ+G3HmhiKS3xwAAAABJRU5ErkJggg==',
        symbolSize: 10,
        showSymbol: false, // 是否显示 symbol, 如果 false 则只有在 tooltip hover 的时候显示。
        yAxisIndex: 0,
        smooth: true,
        z: 0,
        label: {
          show: false,
        },
        lineStyle: {
          width: 2,
          color: 'rgba(72, 214, 255, 1)',
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(72, 249, 255, 0.4)',
            },
            {
              color: 'rgba(63, 248, 254, 0)',
              offset: 1,
            },
          ]),
        },
      },
    ],
  };
};
</script>

<style lang="scss">
.time-curve {
  color: #fff;
  height: 100%;
  box-sizing: border-box;
}
</style>

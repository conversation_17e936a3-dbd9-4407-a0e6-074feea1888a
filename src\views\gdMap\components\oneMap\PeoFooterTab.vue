<template>
	<div v-if="headTab === 1" class="footer-tab">
		<div
			v-for="(tab, idx) in tabs"
			:key="tab.label"
			:class="['tab-item', { active: idx === activeIndex }]"
			@click="selectTab(idx)"
		>
			<div class="icon-wrap">
				<img :src="tab.icon" class="tab-icon" />
			</div>
			<div class="tab-label">{{ tab.label }}</div>
		</div>
	</div>
</template>

<script setup>
import emitter from "@/utils/emitter";

import people from "@/assets/images/people/people.svg";
import proj from "@/assets/images/people/proj.svg";
import Equipment from "@/assets/images/people/Equipment.svg";
import cool from "@/assets/images/people/cool.svg";
import light from "@/assets/images/people/light.svg";
import env from "@/assets/images/people/env.svg";
import alarm from "@/assets/images/people/alarm.svg";

const tabs = [
	{ icon: people, label: "人员分布" },
	{ icon: proj, label: "工程类型" },
	{ icon: Equipment, label: "机械设备" },
	{ icon: cool, label: "冻土监测" },
	{ icon: light, label: "实时路况" },
	{ icon: env, label: "生态环保" },
	{ icon: alarm, label: "安全隐患" },
];

const activeIndex = ref(0);
const headTab = ref(0);

watch(
	() => [activeIndex.value, headTab.value],
	([value, display]) => {
		emitter.$emit("footer-tab-change", value, display);
	},
	{
		immediate: true,
	}
);

onMounted(() => {
	emitter.$on("header-tab-change", handleHeadTabChange);
});

onUnmounted(() => {
	emitter.$off("header-tab-change", handleHeadTabChange);
});

const handleHeadTabChange = (value) => {
	headTab.value = value;
};

function selectTab(idx) {
	activeIndex.value = idx;
}
</script>

<style lang="scss" scoped>
.footer-tab {
	margin-bottom: 20px;
	display: flex;
	justify-content: center;
	align-items: flex-end;
	background: transparent;
	padding: 16px 0 8px 0;
	gap: 24px;
}
.tab-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	cursor: pointer;
	opacity: 0.5;
	transition: opacity 0.2s;
	.icon-wrap {
		width: 60px;
		height: 60px;
		border-radius: 50%;
		// background: rgba(0, 80, 255, 0.08);
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 6px;
		transition: background 0.2s;
		background: linear-gradient(
			180deg,
			rgba(0, 18, 33, 0.6) 0%,
			rgba(0, 18, 33, 0.6) 44%,
			#001221 100%
		);
		border-radius: 50%;
		// border: 1px solid;
		border-image: linear-gradient(180deg, rgba(23, 100, 167, 0), rgba(0, 30, 56, 1)) 1 1;
	}
	.tab-icon {
		width: 36px;
		height: 36px;
	}
	.tab-label {
		font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
		color: #7699b9;
		font-size: 12px;
		margin-top: 2px;
		letter-spacing: 1px;
	}
}
.tab-item.active {
	opacity: 1;
	.icon-wrap {
		background: linear-gradient(180deg, rgba(0, 138, 255, 0) 47%, #008aff 100%),
			radial-gradient(0% 87% at -13% 112%, #64c6ff 0%, rgba(8, 62, 115, 0) 100%),
			rgba(0, 138, 255, 0.3);
		box-shadow: inset 0px 0px 8px 1px rgba(0, 138, 255, 0.25);
		border-radius: 50%;
		// border: 1px solid;
		border-image: linear-gradient(
				90deg,
				rgba(0, 138, 255, 0),
				rgba(0, 138, 255, 1),
				rgba(0, 138, 255, 0.2),
				rgba(0, 138, 255, 0)
			)
			1 1;
	}
	.tab-label {
		color: #acd7ff;
	}
}
</style>

# G109 BIM-GIS 可视化中心

## 项目介绍

G109 BIM-GIS 可视化中心是一个基于 Vue 3 和 Vite 构建的现代化前端应用，专注于地理信息展示和数据可视化。

## 技术栈

- 前端框架 ：Vue 3
- 构建工具 ：Vite
- UI 组件库 ：Arco Design Vue
- 数据可视化 ：ECharts、ECharts GL
- 地图引擎 ：MapLibre GL
- HTTP 请求 ：Axios
- 动画效果 ：GSAP

## 项目启动

### 环境要求

- Node.js >= 18.0.0
- npm >= 9.0.0

### 安装依赖

```base
pnpm install
```

### 开发模式启动

```base
pnpm dev
```

## 项目打包

### 构建生产环境版本

```base
pnpm build
```

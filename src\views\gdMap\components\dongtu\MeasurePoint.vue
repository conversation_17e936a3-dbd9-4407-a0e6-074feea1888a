<template>
  <div class="left-card">
    <m-card title="测点状态统计" :height="387">
      <div class="measure-point">
        <div class="card-content-main">
          <ScreenTable :columns="tableColumns">
            <div ref="containerRef" class="measure-table">
              <div class="table-body">
                <div class="table-row" v-for="(item, index) in measureData" :key="index">
                  <div class="col name" :title="item.name">{{ item.name }}</div>
                  <div class="col time">{{ item.time }}</div>
                  <div class="col type">
                    <span :class="['dot', setBg(item.type)]"></span>{{ item.type }}
                  </div>
                </div>
              </div>
            </div>
          </ScreenTable>
        </div>
      </div>
    </m-card>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import ScreenTable from '../ScreenTable.vue';
import { useMarquee } from '@/hooks/useMarquee';
import { getMeasurePoint } from '@/views/gdMap/services/dongtu.mock.js';

const measureData = ref();

const { containerRef, resetScroll } = useMarquee({
  speed: 10, // 滚动速度
  delay: 2000, // 滚动到底部后停顿时间
  step: 1, // 每次滚动的像素
});

// 当数据变化时，重置滚动
watch(
  measureData,
  () => {
    setTimeout(resetScroll, 300); // 延迟一点时间确保DOM已更新
  },
  { deep: true }
);

onMounted(async () => {
  const defaultData = await getMeasurePoint();
  measureData.value = defaultData.list;
});

// 表格列配置
const tableColumns = [
  { title: '设备名称', width: '35%' },
  { title: '时间', width: '50%' },
  { title: '状态', width: '15%' },
];

const setBg = type => {
  if (type === '正常') {
    return 'green';
  } else if (type === '报警') {
    return 'red';
  } else {
    return 'grey';
  }
};
</script>

<style lang="scss">
.measure-point {
  height: 100%;
  display: flex;
  flex-direction: column;
  .card-content-main {
    flex: 1;
    overflow: hidden;
  }
  .measure-table {
    height: 100%;
    overflow-y: hidden;
    .table-body {
      .table-row {
        display: flex;
        height: 28px;
        align-items: center;

        // 单行背景色
        &:nth-child(odd) {
          background-color: #091e3f;
        }

        // 双行背景色
        &:nth-child(even) {
          background-color: #142b50;
        }

        &:last-child {
          border-bottom: none;
        }

        .col {
          padding: 0 6px;
          font-size: 12px;
          color: #fff;
          font-family: Source Han Sans CN, Source Han Sans CN;
          box-sizing: border-box;
          &.time {
            width: 50%;
            text-align: left;
          }

          &.type {
            width: 15%;
          }

          &.name {
            width: 35%;
            text-align: left;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }
  .dot {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 1px;
    margin-right: 6px;
    &.green {
      background: #06d239;
    }
    &.red {
      background: #ff4042;
    }
    &.grey {
      background: #c4c4c4;
    }
  }
}
</style>

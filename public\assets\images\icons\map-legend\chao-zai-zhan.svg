<svg width="23" height="23" viewBox="0 0 23 23" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#232;&#167;&#130;&#230;&#153;&#175;&#229;&#143;&#176;">
<g id="Ellipse 15" filter="url(#filter0_d_342_3719)">
<circle cx="11.4917" cy="11.3672" r="8" fill="url(#paint0_radial_342_3719)" fill-opacity="0.7"/>
<circle cx="11.4917" cy="11.3672" r="7.55492" stroke="url(#paint1_linear_342_3719)" stroke-width="0.890162"/>
</g>
<circle id="Ellipse 16" cx="11.5654" cy="11.4416" r="7.07681" fill="url(#paint2_radial_342_3719)" stroke="url(#paint3_linear_342_3719)" stroke-width="0.200286"/>
<g id="&#229;&#176;&#143;&#230;&#161;&#165;" filter="url(#filter1_d_342_3719)">
<g id="Frame" clip-path="url(#clip0_342_3719)">
<path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M7.0564 11.4581C7.0564 8.96627 9.06654 6.95347 11.5557 6.95215C14.0396 6.9495 16.0604 8.97553 16.059 11.4621C16.0577 13.9367 14.0396 15.9521 11.565 15.9521C9.06522 15.9535 7.0564 13.9499 7.0564 11.4581ZM11.6378 14.0836H14.2937C14.3241 13.8613 14.389 13.6138 14.4843 13.3452C12.5549 13.4563 11.3665 13.4563 10.9205 13.3452V12.4122H11.5425V11.8141H10.9205V11.2159H11.5425V10.594H10.7538V10.0673H11.6153V9.58823H12.1896C12.142 9.97068 11.9421 10.3531 11.5915 10.7356C11.7833 10.8944 11.9249 11.0307 12.0215 11.1418H11.7344V13.1956H12.4278V12.9336H13.3131V13.1718H14.0542V11.1392H12.142C12.5721 10.6615 12.8195 10.1427 12.883 9.58426H13.3833C13.3833 9.79203 13.3687 10.0077 13.3356 10.2314C13.3515 10.3439 13.2006 10.3849 12.8817 10.3518C12.977 10.5926 13.0405 10.807 13.0736 10.9989C13.4865 10.9989 13.7419 10.9592 13.8385 10.8785C13.9668 10.815 14.0383 10.631 14.0542 10.328C14.0701 10.2009 14.078 10.0488 14.078 9.87407C14.0877 9.67207 14.0986 9.47006 14.1095 9.26806C14.1149 9.16702 14.1204 9.06598 14.1256 8.96494H11.5901V9.44399H10.7525V9.10919C10.7525 9.04567 10.7604 8.96759 10.7763 8.87099C10.7922 8.82202 10.8001 8.79026 10.8001 8.77438C10.7829 8.71086 10.6082 8.67778 10.2734 8.67778H10.0352V9.44267H9.05463V10.0646H10.0352V10.5913H8.86407V11.2133H10.2271V12.6716C10.1331 12.5432 10.0445 12.3593 9.96509 12.1211C9.96509 11.9782 9.98891 11.8273 10.0365 11.6672C9.87642 11.6341 9.63028 11.5865 9.29548 11.5229C9.24652 12.3844 9.08771 13.1017 8.81643 13.676C8.99111 13.7872 9.17506 13.9314 9.36694 14.1061C9.38282 14.0598 9.4159 13.9791 9.46354 13.8679C9.59058 13.5185 9.67792 13.2631 9.72556 13.103C10.0445 13.5331 10.3065 13.7951 10.5143 13.8917C10.7525 14.0201 11.127 14.0836 11.6378 14.0836ZM13.3116 11.7382H12.4278V12.3354H13.3116V11.7382Z" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_342_3719" x="0.821213" y="0.696702" width="21.341" height="21.341" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.33524"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.388235 0 0 0 0 0.640523 0 0 0 0 0.807843 0 0 0 0.51 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_342_3719"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3719" result="shape"/>
</filter>
<filter id="filter1_d_342_3719" x="6.49182" y="6.36719" width="10" height="10.4451" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.445081"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.150327 0 0 0 0 0.247059 0 0 0 0 0.145098 0 0 0 0.75 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_342_3719"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3719" result="shape"/>
</filter>
<radialGradient id="paint0_radial_342_3719" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(11.4917 11.3672) scale(8)">
<stop stop-color="#D3D3D3"/>
<stop offset="0.03" stop-color="#D3D3D3" stop-opacity="0.9216"/>
<stop offset="0.13" stop-color="#D3D3D3" stop-opacity="0.7056"/>
<stop offset="0.23" stop-color="#D3D3D3" stop-opacity="0.5329"/>
<stop offset="0.35" stop-color="#D3D3D3" stop-opacity="0.4225"/>
<stop offset="0.47" stop-color="#D3D3D3" stop-opacity="0.3364"/>
<stop offset="0.6" stop-color="#D3D3D3" stop-opacity="0.2916"/>
<stop offset="0.76" stop-color="#D3D3D3" stop-opacity="0.2601"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.5"/>
</radialGradient>
<linearGradient id="paint1_linear_342_3719" x1="11.4917" y1="19.3672" x2="11.4917" y2="3.36719" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<radialGradient id="paint2_radial_342_3719" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(11.5654 11.4416) scale(7.17695)">
<stop stop-color="#D3D3D3"/>
<stop offset="1" stop-color="#0B103B" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint3_linear_342_3719" x1="11.5654" y1="18.6186" x2="11.5654" y2="4.26465" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<clipPath id="clip0_342_3719">
<rect width="10" height="10" fill="white" transform="translate(6.49182 6.36719)"/>
</clipPath>
</defs>
</svg>

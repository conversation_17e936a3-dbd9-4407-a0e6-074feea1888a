<template>
  <div class="layer-tree-container">
    <div class="opt" @click="toggleLayerTree">
      <img v-show="isOpen" src="@/assets/images/open-icon.png" alt="open" />
      <img v-show="!isOpen" src="@/assets/images/fold-icon.png" alt="fold" />
    </div>
    <div class="layer-tree" v-show="isOpen">
      <a-tree ref="treeRef" blockNode :data="layerTree" @select="onSelect">
        <template #icon>
          <IconFolder />
        </template>
        <template #extra="nodeData">
          <icon-eye v-if="nodeData.visiblity" @click="() => hide(nodeData)" />
          <icon-eye-invisible v-else @click="() => show(nodeData)" />
        </template>
        <template #title="nodeData">
          <span :title="nodeData.title">{{ nodeData.title }}</span>
        </template>
      </a-tree>
    </div>
  </div>
</template>

<script setup>
import { ref, h, onBeforeUnmount, onMounted, nextTick } from 'vue';
import emitter from '@/utils/emitter';
import { IconEye, IconEyeInvisible, IconFolder, IconCommon } from '@arco-design/web-vue/es/icon';

onMounted(() => {
  emitter.$on('unrealReady', setLayerTree);

  // 如果 unrealApi 已经准备好，直接调用 setLayerTree
  if (window.unrealApi?.infoTree) {
    setLayerTree();
  }
});

const isOpen = ref(false);
const layerTree = ref([]);
const treeRef = ref();

const toggleLayerTree = () => {
  isOpen.value = !isOpen.value;
};

const setLayerTree = () => {
  window.unrealApi?.infoTree.get(val => {
    console.log(val);

    // TODO: 需要过滤掉最底下四个文件夹，过滤条件待定 item.type !== 'EPT_ModelActor' && item.type !== 'EPT_Tag' && item.type !== 'EPT_EffectPoint'
    const data = val.infotree?.filter(
      item => item.type !== 'EPT_ModelActor' && item.type !== 'EPT_Tag'
    );
    layerTree.value = formatTree(toTree(data, -1));
    // 去掉最后四个文件夹
    layerTree.value[0].children = layerTree.value[0].children.slice(0, -4);
    nextTick(() => {
      treeRef.value?.expandNode([layerTree.value[0].key], true);
    });
  });
};

const formatTree = data => {
  return data.map(item => {
    const node = {
      ...item,
      title: item.name,
      key: item.iD,
      icon: !(item.type === 'EPT_Folder') ? () => h(IconCommon) : undefined,
    };
    if (item.children) {
      node.children = formatTree(item.children);
    }
    return node;
  });
};

function toTree(list, parId) {
  const obj = {};
  const result = [];
  // 将数组中数据转为键值对结构 (这里的数组和obj会相互引用)
  list.forEach(el => {
    const id2 = el.index;
    obj[id2] = el;
  });
  for (let i = 0, len = list.length; i < len; i++) {
    const item = list[i];
    const id = item.parentIndex;
    if (id === parId) {
      item.name = 'G10X';
      result.push(item);
      continue;
    }
    if (obj[id].children) {
      obj[id].children.push(item);
    } else {
      obj[id].children = [item];
    }
  }
  return result;
}

const onSelect = (val, data) => {
  console.log(val, data);
  if (data.node?.type === 'EPT_Folder') {
    // 点击文件夹，展开/折叠文件夹
    treeRef.value?.expandNode(val, true);
  } else {
    // 点击其他类型，选中节点并聚焦
    window.unrealApi?.infoTree.focus(val[0]);
  }
};

const setNodeVisiblity = (data, visiblity) => {
  data.visiblity = visiblity;
  if (data.children) {
    data.children.forEach(child => {
      setNodeVisiblity(child, visiblity);
    });
  }
};

const hide = data => {
  window.unrealApi?.infoTree.hide(data.key);
  // setNodeVisiblity(data, false);
  window.unrealApi?.infoTree.get(val => {
    const data = val.infotree?.filter(
      item => item.type !== 'EPT_ModelActor' && item.type !== 'EPT_Tag'
    );
    layerTree.value = formatTree(toTree(data, -1));
    layerTree.value[0].children = layerTree.value[0].children.slice(0, -4);
  });
};

const show = data => {
  window.unrealApi?.infoTree.show(data.key);
  // setNodeVisiblity(data, true);
  window.unrealApi?.infoTree.get(val => {
    const data = val.infotree?.filter(
      item => item.type !== 'EPT_ModelActor' && item.type !== 'EPT_Tag'
    );
    layerTree.value = formatTree(toTree(data, -1));
    layerTree.value[0].children = layerTree.value[0].children.slice(0, -4);
  });
  // 显示时若父节点是隐藏的，则一并显示
};

onBeforeUnmount(() => {
  emitter.$off('unrealReady', setLayerTree);
});
</script>

<style lang="scss">
.layer-tree-container {
  color: #fff;
  .opt {
    width: 32px;
    height: 32px;
    img {
      width: 100%;
      height: 100%;
    }
    margin-bottom: 4px;
    cursor: pointer;
  }
}
.layer-tree {
  width: 260px;
  max-height: 460px;
  position: absolute;
  color: #fff;
  border-radius: 4px;
  border: 1px solid #58a1e7;
  background: rgba(19, 51, 92, 0.6); /* 降低不透明度以便看到模糊效果 */
  backdrop-filter: blur(4px); /* 添加8px的模糊效果 */
  -webkit-backdrop-filter: blur(4px); /* Safari浏览器兼容 */
  padding: 16px 10px;
  box-sizing: border-box;
  overflow: auto;
  .arco-tree-node-title {
    flex: 1;
    overflow: hidden;
  }
  .arco-tree-node-title-text {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>

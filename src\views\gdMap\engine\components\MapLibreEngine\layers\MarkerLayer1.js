import maplibregl from 'maplibre-gl';
import { createApp } from 'vue';
import ProjectMarker from '../components/ProjectMarker.vue';

export class MarkerLayer {
  constructor(map) {
    this.map = map;
    this.markers = new Map();
    this.visible = true;
    this.minZoom = map.getMinZoom();
    this.maxZoom = map.getMaxZoom();

    // 添加图标标记的图层ID
    this.iconLayerId = 'icon-markers-layer';
    this.iconSourceId = 'icon-markers-source';

    this.loadFromAreaMarkJson();

    // 只监听缩放事件来控制可见性
    this.map.on('zoom', () => {
      this.updateMarkersVisibility();
    });

    // 图标加载状态
    this.iconsLoaded = false;
  }

  /**
   * 加载自定义图标
   * @private
   */
  async loadIcons() {
    if (this.iconsLoaded) return;

    // 定义需要加载的图标列表
    const icons = [
      { name: 'command-center-icon', fileName: 'general-command-marker.svg' },
      { name: 'gegong1', fileName: 'ge-gong-yi-w0zj.svg' },
      { name: 'gegong2', fileName: 'ge-gong-er-t1wr.svg' },
      { name: 'gegong3', fileName: 'ge-gong-san-r2yx.svg' },
      { name: 'gegong4', fileName: 'ge-gong-si-gwqa.svg' },
      { name: 'gongna1', fileName: 'gong-na-yi-dknk.svg' },
      { name: 'gongna2', fileName: 'gong-na-er-hgys.svg' },
      // 添加更多图标...
    ];

    // 创建所有图标的加载任务
    const loadTasks = icons.map(icon => {
      return new Promise(resolve => {
        const img = new Image();
        img.src = `/assets/images/icons/map/${icon.fileName}`;
        img.onload = () => {
          this.map.addImage(icon.name, img);
          resolve();
        };
        img.onerror = error => {
          console.error(`Failed to load icon: ${icon.name}`, error);
          resolve(); // 即使某个图标加载失败，也继续加载其他图标
        };
      });
    });

    // 等待所有图标加载完成
    await Promise.all(loadTasks);

    this.iconsLoaded = true;
  }

  // 从area-mark.json加载GeoJSON数据
  async loadFromAreaMarkJson() {
    try {
      const response = await fetch('/config/engine/map/json/area-mark.json');
      const geoJsonData = await response.json();

      // 验证是否为有效的GeoJSON
      if (
        !geoJsonData ||
        geoJsonData.type !== 'FeatureCollection' ||
        !Array.isArray(geoJsonData.features)
      ) {
        console.error('无效的GeoJSON数据格式');
        return this;
      }

      // 清除现有标记
      this.clear();

      // 加载自定义图标
      await this.loadIcons();

      // 分离图标标记和立牌标记
      const iconMarkers = {
        type: 'FeatureCollection',
        features: [],
      };

      // 为每个特征创建标记，并添加索引信息用于左右交替显示
      geoJsonData.features.forEach((feature, index) => {
        const id = feature.id;
        const coordinates = feature.geometry.coordinates;
        const properties = feature.properties;

        // 添加索引信息到properties中，用于决定左右显示
        properties.markerIndex = index;
        properties.markerPosition = index % 2 === 0 ? 'left' : 'right';

        // 修改判断逻辑，允许同时渲染图标标记和立牌标记
        if (properties.types && properties.types.includes('area_marker')) {
          // 添加到图标标记集合
          iconMarkers.features.push(feature);
        }

        if (properties.types && properties.types.includes('area_board')) {
          // 渲染立牌标记
          this.addMarkerWithProperties(id, coordinates, properties.label, properties);
        }
      });

      console.log('iconMarkers', iconMarkers);

      // 添加图标标记图层
      if (iconMarkers.features.length > 0) {
        this.addIconMarkersLayer(iconMarkers);
      }

      return this;
    } catch (error) {
      console.error('加载区域标记数据失败:', error);
      throw error;
    }
  }

  // 添加图标标记图层
  addIconMarkersLayer(geoJson) {
    // 如果已存在源，先移除
    if (this.map.getSource(this.iconSourceId)) {
      if (this.map.getLayer(this.iconLayerId)) {
        this.map.removeLayer(this.iconLayerId);
      }
      this.map.removeSource(this.iconSourceId);
    }

    // 添加数据源
    this.map.addSource(this.iconSourceId, {
      type: 'geojson',
      data: geoJson,
    });

    // 添加图层
    this.map.addLayer({
      id: this.iconLayerId,
      type: 'symbol',
      source: this.iconSourceId,
      layout: {
        // 修复获取icon的表达式
        'icon-image': ['get', 'icon', ['get', 'style']],
        // 'icon-image': 'command-center-icon',
        // 'icon-size': 0.5,
        'icon-allow-overlap': true,
        'icon-ignore-placement': true,
      },
    });
  }

  // 添加带属性的标记
  addMarkerWithProperties(id, lngLat, label, properties = {}) {
    const el = document.createElement('div');
    const app = createApp(ProjectMarker, {
      label,
      properties, // 传递所有属性到组件
      position: properties.markerPosition || 'left', // 传递位置信息
    });
    app.mount(el);

    // 创建 Marker
    const marker = new maplibregl.Marker({
      element: el,
      anchor: properties.markerPosition === 'right' ? 'right' : 'left', // 根据位置设置锚点
    })
      .setLngLat(lngLat)
      .addTo(this.map);

    // 设置立牌标记的缩放级别范围
    properties.minZoom = 5; // 最小缩放级别
    properties.maxZoom = 9; // 最大缩放级别

    // 初始化时根据可见性设置显示状态
    this.updateMarkerVisibility(marker, properties);

    this.markers.set(id, {
      marker,
      app,
      el,
      properties,
    });

    return marker;
  }

  // 添加标记 (保留原有方法兼容性)
  addMarker(id, lngLat, label) {
    return this.addMarkerWithProperties(id, lngLat, label);
  }

  // 更新单个标记可见性
  updateMarkerVisibility(marker, properties = {}) {
    const zoom = this.map.getZoom();
    const minZoom = properties.minZoom || this.minZoom;
    const maxZoom = properties.maxZoom || this.maxZoom;
    const shouldBeVisible = zoom >= minZoom && zoom <= maxZoom;
    marker.getElement().style.display = shouldBeVisible && this.visible ? '' : 'none';
  }

  // 更新所有标记可见性
  updateMarkersVisibility() {
    this.markers.forEach(({ marker, properties }) => {
      this.updateMarkerVisibility(marker, properties);
    });
  }

  // 显示所有标记
  show() {
    if (!this.visible) {
      this.markers.forEach(({ marker }) => {
        marker.getElement().style.display = '';
      });

      // 显示图标标记图层
      if (this.map.getLayer(this.iconLayerId)) {
        this.map.setLayoutProperty(this.iconLayerId, 'visibility', 'visible');
      }

      this.visible = true;
    }
  }

  // 隐藏所有标记
  hide() {
    if (this.visible) {
      this.markers.forEach(({ marker }) => {
        marker.getElement().style.display = 'none';
      });

      // 隐藏图标标记图层
      if (this.map.getLayer(this.iconLayerId)) {
        this.map.setLayoutProperty(this.iconLayerId, 'visibility', 'none');
      }

      this.visible = false;
    }
  }

  // 清除所有标记
  clear() {
    this.markers.forEach(({ marker, app }) => {
      marker.remove();
      app.unmount();
    });
    this.markers.clear();

    // 移除图标标记图层
    if (this.map.getLayer(this.iconLayerId)) {
      this.map.removeLayer(this.iconLayerId);
    }
    if (this.map.getSource(this.iconSourceId)) {
      this.map.removeSource(this.iconSourceId);
    }
  }

  // 计算偏移量
  calculateOffset() {
    const zoom = this.map.getZoom();
    const baseOffset = 43;
    const scale = Math.pow(2, zoom - this.baseZoom); // 使用初始缩放级别作为基准
    return [-baseOffset / scale, 0];
  }

  // 更新所有标记的偏移量
  updateMarkersOffset() {
    const offset = this.calculateOffset();
    this.markers.forEach(({ marker }) => {
      marker.setOffset(offset);
    });
  }

  // 移除标记
  removeMarker(id) {
    const markerInfo = this.markers.get(id);
    if (markerInfo) {
      markerInfo.marker.remove();
      markerInfo.app.unmount();
      this.markers.delete(id);
    }
  }

  // 更新标记位置
  updateMarkerPosition(id, lngLat) {
    const markerInfo = this.markers.get(id);
    if (markerInfo) {
      markerInfo.marker.setLngLat(lngLat);
    }
  }
}

<template>
	<div ref="markerRef" style="width: 100%; height: 100%">
		<div class="common-marker" :class="markerClass">
			<div class="icon" @click.stop="onClickIcon">
				<component :is="icon" />
			</div>
		</div>
		<MarkerPanel v-if="isExpanded" :title="title" :alarm="isAlarm" anchor="right-bottom">
			<template #content>
				<div class="data-list">
					<template v-if="props.type === 'people'">
						<div class="data-item">
							<span class="data-key">在场人员数:</span>
							<span class="data-value"> {{ props.properties["staffNumber"] }} 人 </span>
						</div>
						<div class="data-item">
							<span class="data-key">终端在线数:</span>
							<span class="data-value"> {{ props.properties["deviceOnlineNumber"] }} 人 </span>
						</div>
						<div class="data-item">
							<span class="data-key">人员在线率:</span>
							<span class="data-value"> {{ props.properties["staffOnlinePercent"] }} 人 </span>
						</div>
					</template>
					<template v-else-if="props.type === 'fence'">
						<div class="data-item">
							<span class="data-key">围栏总数:</span>
							<span class="data-value"> {{ props.properties["totalNumber"] }} 个 </span>
						</div>
						<div class="data-item">
							<span class="data-key">工作围栏数:</span>
							<span class="data-value"> {{ props.properties["workNumber"] }} 个 </span>
						</div>
						<div class="data-item">
							<span class="data-key">危险围栏数:</span>
							<span class="data-value"> {{ props.properties["dangerNumber"] }} 个 </span>
						</div>
						<div class="data-item">
							<span class="data-key">入围栏告警:</span>
							<span class="data-value"> {{ props.properties["inFenceAlarmNumber"] }} 个 </span>
						</div>
						<div class="data-item">
							<span class="data-key">出围栏告警:</span>
							<span class="data-value"> {{ props.properties["outFenceAlarmNumber"] }} 个 </span>
						</div>
					</template>
					<template v-else-if="props.type === 'sos'">
						<div class="data-item">
							<span class="data-key">姓名:</span>
							<span class="data-value"> {{ sosData.staffName }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">性别:</span>
							<span class="data-value"> {{ sosData.gender }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">身份证号:</span>
							<span class="data-value"> {{ sosData.idCard }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">所在工区:</span>
							<span class="data-value"> {{ sosData.workArea }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">绑定终端号:</span>
							<span class="data-value"> {{ sosData.deviceCode }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">人员类型:</span>
							<span class="data-value"> {{ sosData.staffType }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">告警时间:</span>
							<span class="data-value"> {{ sosData.alarmTime }} </span>
						</div>
					</template>
					<template v-else-if="props.type === 'mechanical-device'">
						<div class="data-item">
							<span class="data-key">设备名称:</span>
							<span class="data-value"> {{ props.properties["name"] || "固定机械设备" }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">设备类型:</span>
							<span class="data-value">
								{{ props.properties["equipmentType"] || "机械设备" }}
							</span>
						</div>
						<div class="data-item">
							<span class="data-key">设备状态:</span>
							<span class="data-value"> {{ props.properties["status"] || "正常" }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">位置信息:</span>
							<span class="data-value"> {{ props.properties["location"] || "固定位置" }} </span>
						</div>
					</template>
					<template v-else-if="props.type === 'vehicle-equipment'">
						<div class="data-item">
							<span class="data-key">装备车辆数:</span>
							<span class="data-value"> {{ props.properties["canNumber"] || 0 }} 辆 </span>
						</div>
						<div class="data-item">
							<span class="data-key">终端在线数:</span>
							<span class="data-value"> {{ props.properties["onlineNumber"] || 0 }} 个 </span>
						</div>
						<div class="data-item">
							<span class="data-key">车辆在线率:</span>
							<span class="data-value"> {{ props.properties["onlinePercent"] || "0.00" }}% </span>
						</div>
					</template>
					<template v-else-if="props.type === 'vehicle-detail'">
						<div class="data-item">
							<span class="data-key">车辆名称:</span>
							<span class="data-value"> {{ vehicleData["carName"] || "未知" }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">车牌号:</span>
							<span class="data-value"> {{ vehicleData["vehicleNo"] || "未知" }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">装备编码:</span>
							<span class="data-value"> {{ vehicleData["equipCode"] || "未知" }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">终端编号:</span>
							<span class="data-value"> {{ vehicleData["deviceCode"] || "未知" }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">司机姓名:</span>
							<span class="data-value"> {{ vehicleData["driverName"] || "未知" }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">司机电话:</span>
							<span class="data-value"> {{ vehicleData["driverPhone"] || "未知" }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">告警时间:</span>
							<span class="data-value">
								{{ formatDateTime(vehicleData["alarmTime"]) || "无" }}
							</span>
						</div>
						<!-- <div class="data-item">
            <span class="data-key">告警数量:</span>
            <span class="data-value"> {{ vehicleData["alarmNumber"] || 0 }} 次 </span>
          </div> -->
					</template>
					<template v-else-if="props.type === 'people-actual'">
						<div class="data-item">
							<span class="data-key">姓名:</span>
							<span class="data-value"> {{ staffData.staffName }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">性别:</span>
							<span class="data-value"> {{ staffData.gender }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">身份证号:</span>
							<span class="data-value"> {{ staffData.idCard }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">所在工区:</span>
							<span class="data-value"> {{ staffData.workArea }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">绑定终端号:</span>
							<span class="data-value"> {{ staffData.deviceCode }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">人员类型:</span>
							<span class="data-value"> {{ staffData.staffType }} </span>
						</div>
						<div v-if="props.properties['alarm']" class="data-item">
							<span class="data-key">告警时间:</span>
							<span class="data-value"> {{ staffData.alarmTime }} </span>
						</div>
					</template>
				</div>
			</template>
		</MarkerPanel>
	</div>
</template>

<script setup>
import { Dict, departmentDict } from "@/utils/dict";
import request from "@/utils/request";
import emitter from "@/utils/emitter";
import { v4 } from "uuid";
import StaffPeople from "@/assets/modules/oneMap/icon/staff-people.svg?component";
import StaffFence from "@/assets/modules/oneMap/icon/staff-fence.svg?component";
import StaffSos from "@/assets/modules/oneMap/icon/staff-sos.svg?component";
import DeviceOut from "@/assets/modules/oneMap/icon/device-out.svg?component";
import DeviceType1 from "@/assets/modules/oneMap/icon/device-type1.svg?component";
import MarkerPanel from "./MarkerPanel.vue";

const props = defineProps({
	properties: {
		type: Object,
		default: {},
	},
	type: String,
});

const typeList = new Dict([
	{
		value: "people",
		label: "人员",
		class: "is-cyan",
		icon: StaffPeople,
	},
	{
		value: "fence",
		label: "电子围栏",
		class: "is-blue",
		icon: StaffFence,
	},
	{
		value: "sos",
		label: "SOS",
		class: "is-red",
		icon: StaffSos,
	},
	{
		value: "mechanical-device",
		label: "机械设备",
		icon: DeviceOut,
	},
	{
		value: "vehicle-equipment",
		label: "车辆设备",
		icon: DeviceOut,
	},
	{
		value: "vehicle-detail",
		label: "车辆详情",
		icon: DeviceType1,
	},
	{
		value: "people-actual",
		class: "is-cyan",
		label: "人员基本信息",
		icon: StaffPeople,
	},
	{
		value: "fence-actual",
		class: "is-blue",
		label: "电子围栏",
	},
]);

const isAlarm = computed(() => {
	return props.properties["alarm"] || props.type === "sos" || staffData.alarm;
});

const markerClass = computed(() => {
	const item = typeList.getItem(props.type);
	return item?.class || "is-blue";
});

const icon = computed(() => {
	const item = typeList.getItem(props.type);
	if (item) {
		return item.icon;
	}
	return null;
});

const title = computed(() => {
	if (props.type === "vehicle-equipment") {
		// 车辆设备类型使用特殊的标题格式
		const departmentName =
			props.properties["location"] || departmentDict.getLabel(props.properties["departmentId"]);
		return `【车辆】 ${departmentName}`;
	}
	if (props.type === "vehicle-detail") {
		// 车辆详情类型使用固定标题
		return "车辆基本信息";
	}
	return `【${typeList.getLabel(props.type)}】 ${departmentDict.getLabel(
		props.properties["departmentId"]
	)}`;
});

let inited = false;
const markerId = ref(v4());
const markerRef = ref(null);
const isExpanded = ref(false);
const sosData = ref({});
const staffData = ref({});
const vehicleData = ref({});

const getData = async () => {
	if (props.type === "sos") {
		await getSosDetail();
	}
	if (props.type === "people-actual") {
		await getStaffDetail();
	}
	if (props.type === "vehicle-detail") {
		getVehicleDetail();
	}
};

const getSosDetail = async () => {
	await request
		.get("/api/screen/staff/center/sos/detail", { staffId: props.properties["staffId"] })
		.then((res) => {
			if (res.code == 200) {
				sosData.value = res.data;
			}
		});
};

const getStaffDetail = async () => {
	await request
		.get("/api/screen/staff/center/staff/detail", { staffId: props.properties["staffId"] })
		.then((res) => {
			if (res.code == 200) {
				staffData.value = res.data;
			}
		});
};

const getVehicleDetail = () => {
	const equipId = props.properties["equipId"];
	if (!equipId) {
		console.warn("车辆设备ID不存在，无法获取详情");
		return;
	}

	console.log(`正在获取车辆详情，equipId: ${equipId}`);

	request
		.get("/api/screen/equipment/center/car/detail", { equipId: equipId })
		.then((res) => {
			console.log("车辆详情接口返回数据:", res);
			if (res.code == 200) {
				vehicleData.value = res.data;
				console.log("车辆详情数据:", res.data);
			} else {
				console.warn("获取车辆详情失败:", res.msg);
			}
		})
		.catch((error) => {
			console.error("车辆详情接口调用失败:", error);
		});
};

// 获取设备类型名称
const getDeviceTypeName = (deviceType) => {
	const deviceTypeMap = {
		1: "土方机械",
		2: "运输设备",
		3: "压实设备",
		4: "起重设备",
		5: "桩基设备",
		6: "混凝土设备",
		7: "路面设备",
		8: "辅助设备",
		9: "钢筋加工设备",
	};
	return deviceTypeMap[deviceType] || `设备类型${deviceType}`;
};

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
	if (!dateTimeStr) return null;

	try {
		const date = new Date(dateTimeStr);
		if (isNaN(date.getTime())) return null;

		// 格式化为 YYYY-MM-DD HH:mm:ss
		const year = date.getFullYear();
		const month = String(date.getMonth() + 1).padStart(2, "0");
		const day = String(date.getDate()).padStart(2, "0");
		const hours = String(date.getHours()).padStart(2, "0");
		const minutes = String(date.getMinutes()).padStart(2, "0");
		const seconds = String(date.getSeconds()).padStart(2, "0");

		return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
	} catch (error) {
		console.warn("日期格式化失败:", dateTimeStr, error);
		return null;
	}
};

const onClickIcon = () => {
	if (!isExpanded.value) {
		emitter.$emit("marker-expand", markerId.value);
	}
	isExpanded.value = !isExpanded.value;
};

const handleClickOutside = (event) => {
	if (isExpanded.value && markerRef.value && !markerRef.value.contains(event.target)) {
		isExpanded.value = false;
	}
};

const handleOtherMarkerExpand = (id) => {
	if (id !== markerId.value && isExpanded.value) {
		isExpanded.value = false;
	}
};

watch(
	() => isExpanded.value,
	async (val) => {
		if (val && !inited) {
			await getData();
			inited = true;
		}

		const parent = markerRef.value.parentElement;
		parent.style["z-index"] = val ? 1 : 0;
	}
);

onMounted(() => {
	document.addEventListener("click", handleClickOutside);
	emitter.$on("marker-expand", handleOtherMarkerExpand);
});

onUnmounted(() => {
	document.removeEventListener("click", handleClickOutside);
	emitter.$off("marker-expand", handleOtherMarkerExpand);
});
</script>

<style lang="scss" scoped>
@import "./common.scss";

.data-key {
	width: 68px;
}

.alarm-text {
	color: #ff4d4f !important;
}

.normal-text {
	color: #52c41a !important;
}
</style>

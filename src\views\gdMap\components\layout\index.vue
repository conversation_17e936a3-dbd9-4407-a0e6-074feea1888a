<template>
  <div class="main-layout">
    <!-- 内容区 -->
    <main class="main-layout-main">
      <slot></slot>
    </main>

    <div class="main-layout-overlay" id="main-layout-overlay">
      <!-- 添加上方背景渐变 -->
      <div class="main-layout-gradient main-layout-gradient-top"></div>
      <!-- 添加左侧背景渐变 -->
      <div class="main-layout-gradient main-layout-gradient-left"></div>
      <!-- 添加右侧背景渐变 -->
      <div class="main-layout-gradient main-layout-gradient-right"></div>
      <!-- 添加底部背景渐变 -->
      <div class="main-layout-gradient main-layout-gradient-bottom"></div>

      <div class="main-layout-header">
        <slot name="header"></slot>
      </div>

      <div class="main-layout-sides">
        <!-- 左侧边栏 -->
        <div
          v-if="showLeftSidebar"
          class="main-layout-sidebar main-layout-sidebar-left"
          :class="{ 'is-collapsed': leftCollapsed }"
        >
          <div class="main-layout-sidebar-content">
            <slot name="left-sidebar">
              <slot>1111</slot>
            </slot>
          </div>
          <!-- 收起按钮 -->
          <div class="main-layout-sidebar-toggle" @click="toggleLeftSidebar" v-if="!leftCollapsed">
            <img
              src="@/assets/modules/common/icons/hide-btn-left.svg"
              alt="收起"
              class="toggle-icon-img"
            />
          </div>
        </div>

        <!-- 左侧展开按钮 - 固定在屏幕边缘 -->
        <div
          v-if="showLeftSidebar && leftCollapsed"
          class="main-layout-edge-toggle main-layout-edge-toggle-left"
          @click="toggleLeftSidebar"
        >
          <img
            src="@/assets/modules/common/icons/hide-btn-right.svg"
            alt="展开"
            class="toggle-icon-img toggle-icon-img-reverse"
          />
        </div>

        <!-- 右侧边栏 -->
        <div
          v-if="showRightSidebar"
          class="main-layout-sidebar main-layout-sidebar-right"
          :class="{ 'is-collapsed': rightCollapsed }"
        >
          <div class="main-layout-sidebar-content">
            <slot name="right-sidebar"></slot>
          </div>
          <!-- 收起按钮 -->
          <div
            class="main-layout-sidebar-toggle"
            @click="toggleRightSidebar"
            v-if="!rightCollapsed"
          >
            <img
              src="@/assets/modules/common/icons/hide-btn-right.svg"
              alt="收起"
              class="toggle-icon-img"
            />
          </div>
        </div>

        <!-- 右侧展开按钮 - 固定在屏幕边缘 -->
        <div
          v-if="showRightSidebar && rightCollapsed"
          class="main-layout-edge-toggle main-layout-edge-toggle-right"
          @click="toggleRightSidebar"
        >
          <img
            src="@/assets/modules/common/icons/hide-btn-left.svg"
            alt="展开"
            class="toggle-icon-img"
          />
        </div>
      </div>

      <div class="main-layout-footer">
        <slot name="footer"></slot>
      </div>

      <slot name="extra"></slot>
    </div>

    <div class="main-layout-bg"></div>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref, watch } from 'vue';
import autofit from 'autofit.js';
import gsap from 'gsap';
import emitter from '@/utils/emitter';

// 定义props，控制侧边栏显示隐藏
const props = defineProps({
  showLeftSidebar: {
    type: Boolean,
    default: true,
  },
  showRightSidebar: {
    type: Boolean,
    default: true,
  },
  // 添加额外的autofit元素选择器
  extraAutofitElements: {
    type: Array,
    default: () => [],
  },
});

// 侧边栏收起状态
const leftCollapsed = ref(false);
const rightCollapsed = ref(false);

// 通用侧边栏切换方法
const toggleSidebar = side => {
  const isLeft = side === 'left';
  const stateRef = isLeft ? leftCollapsed : rightCollapsed;
  const sidebarClass = isLeft ? '.main-layout-sidebar-left' : '.main-layout-sidebar-right';
  const moveDirection = isLeft ? -680 : 680;

  stateRef.value = !stateRef.value;

  if (stateRef.value) {
    // 隐藏侧边栏内容
    gsap.to(`${sidebarClass} .main-layout-sidebar-content`, {
      duration: 0.5,
      opacity: 0,
      ease: 'power2.inOut',
    });

    // 移动整个侧边栏
    gsap.to(sidebarClass, {
      duration: 0.5,
      x: moveDirection,
      ease: 'power2.inOut',
    });
  } else {
    // 显示侧边栏内容
    gsap.to(`${sidebarClass} .main-layout-sidebar-content`, {
      duration: 0.5,
      opacity: 1,
      ease: 'power2.inOut',
    });

    // 移动整个侧边栏
    gsap.to(sidebarClass, {
      duration: 0.5,
      x: 0,
      ease: 'power2.inOut',
    });
  }
};

// 切换左侧边栏
const toggleLeftSidebar = () => {
  toggleSidebar('left');
};

// 切换右侧边栏
const toggleRightSidebar = () => {
  toggleSidebar('right');
};

// autofit配置
let autofitInstance = null;
let extraAutofitInstances = [];

onMounted(() => {
  // 初始化主autofit
  autofitInstance = autofit.init({
    el: '#main-layout-overlay',
    dw: 1920,
    dh: 1080,
    resize: true,
  });

  // 初始化额外的autofit元素
  if (props.extraAutofitElements && props.extraAutofitElements.length > 0) {
    props.extraAutofitElements.forEach(selector => {
      const instance = autofit.init({
        el: selector,
        dw: 1920,
        dh: 1080,
        resize: true,
      });
      extraAutofitInstances.push(instance);
    });
  }
});

onUnmounted(() => {
  // 销毁主autofit实例
  if (autofitInstance) {
    autofitInstance.destroy();
  }

  // 销毁额外的autofit实例
  if (extraAutofitInstances.length > 0) {
    extraAutofitInstances.forEach(instance => {
      if (instance) {
        instance.destroy();
      }
    });
    extraAutofitInstances = [];
  }
});
</script>

<style lang="scss" scoped>
.main-layout {
  margin: 0 auto;
  position: relative;
  width: 100%;
  height: 100%;
  background-image: url('@/assets/modules/common/bg/home-bg.webp');
  background-repeat: no-repeat;
  background-size: cover;
  &-gradient {
    position: absolute;
    pointer-events: none;
    z-index: 1;
    &-top {
      top: 0;
      left: 0;
      width: 100%;
      height: 186px;
      // 476px
      background: linear-gradient(180deg, #081935 0%, rgba(8, 25, 53, 0) 100%);
    }

    &-left {
      top: 0;
      left: 0;
      width: 400px;
      height: 100%;
      background: linear-gradient(
        269deg,
        rgba(8, 25, 53, 0) 5%,
        rgba(8, 25, 53, 0.49) 35%,
        #081935 100%
      );
    }

    &-right {
      top: 0;
      right: 0;
      width: 400px;
      height: 100%;
      background: linear-gradient(
        89deg,
        rgba(8, 25, 53, 0) 5%,
        rgba(8, 25, 53, 0.49) 35%,
        #081935 100%
      );
    }

    &-bottom {
      bottom: 0;
      left: 0;
      width: 100%;
      height: 350px;
      background: linear-gradient(
        180deg,
        rgba(8, 25, 53, 0) 0%,
        rgba(8, 25, 53, 0.49) 48%,
        #081935 100%
      );
    }
  }

  &-main {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 2;
  }

  &-overlay {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 3;
    pointer-events: none;
    &:after {
      // 预留是否需要背景图
    }
  }

  &-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('@/assets/modules/common/bg/home-bg.webp') no-repeat;
    background-size: cover;
    pointer-events: none;
    z-index: 1;
  }

  &-sides {
    .main-layout-sidebar {
      position: absolute;
      top: 87px;
      bottom: 52px;
      width: 350px;
      z-index: 3;
      pointer-events: auto;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        height: 100%;
        border: 1px solid;
        border-color: rgba(149, 197, 254, 1);
        opacity: 0.7;
        border-image: linear-gradient(
            to bottom,
            rgba(149, 197, 254, 0) 20px,
            rgba(149, 197, 254, 1) 30%,
            rgba(149, 197, 254, 1) 70%,
            rgba(149, 197, 254, 0) calc(100% - 20px)
          )
          1 100%;
        transition: opacity 0.5s ease;
      }

      &-left {
        &::after {
          right: -10px;
        }
      }
      &-right {
        &::after {
          left: -10px;
        }
      }

      &.is-collapsed {
        &::after {
          opacity: 0;
        }
      }

      &-content {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
        justify-content: space-between;
        overflow: hidden;
        > * {
          width: 100%;
          // margin-bottom: 20px;
          flex: 1;
        }
      }

      &-left {
        left: 40px;
      }

      &-right {
        right: 40px;

        .main-layout-sidebar-toggle {
          left: -27px;
        }
      }

      &-toggle {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: -27px;
        width: 20px;
        height: 38px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        pointer-events: auto;
        z-index: 10;
      }
    }

    .toggle-icon-img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  // 边缘展开按钮样式
  &-edge-toggle {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: -27px;
    width: 20px;
    height: 38px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: auto;
    z-index: 10;

    &-left {
      left: 0;
    }

    &-right {
      right: 0;
    }

    .toggle-icon-img {
      &-reverse {
        transform: rotate(180deg);
      }
    }
  }

  &-footer {
    position: absolute;
    bottom: 90px;
    left: 50%;
    transform: translateX(-50%);
    pointer-events: auto;
    z-index: 3;
  }

  :deep(.arco-spin) {
    width: 100%;
    height: 100%;
    .arco-spin-mask {
      background: rgba(19, 51, 92, 0.3); /* 降低不透明度以便看到模糊效果 */
      // backdrop-filter: blur(8px); /* 添加8px的模糊效果 */
    }
  }
}
</style>

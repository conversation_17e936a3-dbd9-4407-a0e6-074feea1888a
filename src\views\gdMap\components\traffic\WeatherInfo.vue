<template>
	<TrafficCard title="气象信息">
		<div class="weather-info">
			<div class="card-content">
				<div class="left">
					<a-dropdown @select="getData">
						<a-button class="city">
							<location />
							<div class="city-name">格尔木市</div>
							<caret-down />
						</a-button>
						<template #content>
							<a-doption v-for="{ label, value } in cityList" :value="value">{{ label }}</a-doption>
						</template>
					</a-dropdown>
					<img class="weather-icon" src="/assets/images/icons/weather/100.png" />
					<div class="weather-desc">
						<div class="weather-desc-text">{{ weatherData.weather }}</div>
						<div class="weather-desc-split">/</div>
						<div class="weather-desc-temp">{{ weatherData.temp }}</div>
						<div class="weather-desc-unit">°C</div>
					</div>
					<div class="warning">
						<warning-yellow class="warning-icon" />
						<div class="warning-text">{{ weatherData.alarmType }}预警</div>
					</div>
				</div>
				<div class="divider"></div>
				<div class="right">
					<div class="right-top">
						<div class="weather-metrics">
							<div v-for="item in 4" class="weather-metrics-item">
								<warning-yellow />
								<div class="weather-metrics-item-text">湿度32°C</div>
							</div>
						</div>
					</div>
					<div class="right-divider"></div>
					<div class="right-footer">
						<div class="weather-hours">
							<div v-for="i in 6" class="weather-hours-item">
								<div class="weather-hours-item-time">11时</div>
								<div class="weather-hours-item-icon">
									<img src="/assets/images/icons/weather/104.png" />
								</div>
								<div class="weather-hours-item-temp">32°C</div>
								<div class="weather-hours-item-humi">
									<warning-yellow />
									0.0
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<template #extra>
			<div class="link" @click="onInfo">更多天气 <icon-double-right /></div>
		</template>
	</TrafficCard>
</template>

<script setup>
import request from "@/utils/request";
import location from "@/assets/modules/traffic/icon/location.svg?component";
import caretDown from "@/assets/modules/traffic/icon/caret-down.svg?component";
import warningYellow from "@/assets/modules/traffic/icon/warning-yellow.svg?component";

const cityList = [{ label: "格尔木市", value: "格尔木" }];

const weatherData = ref({});

onMounted(() => {
	getData(cityList[0].value);
});

const getData = (value) => {
	request.get("/api/screen/baotong/weather", { city: value }).then((res) => {
		weatherData.value = res.data;
	});
};

const onInfo = () => {};
</script>

<style lang="scss" scoped>
.weather-info {
	height: 138px;
	width: 100%;
}

.card-content {
	display: flex;
	flex-direction: row;
	width: 100%;
	height: 100%;
	overflow: hidden;

	.left {
		width: 102px;
		padding: 12px 8px;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.divider {
		width: 0;
		height: 129px;
		border: 1px solid;
		border-image: linear-gradient(
				90deg,
				rgba(0, 138, 255, 0),
				rgba(0, 138, 255, 0.4),
				rgba(0, 138, 255, 0)
			)
			1 1;
	}

	.right {
		width: 292px;

		&-top {
			height: 58px;
		}

		&-divider {
			width: 100%;
			height: 0px;
			border: 1px solid;
			border-image: linear-gradient(
					90deg,
					rgba(0, 138, 255, 0),
					rgba(0, 138, 255, 0.4),
					rgba(0, 138, 255, 0)
				)
				1 1;
		}

		&-footer {
			height: 78px;
		}
	}
}

.city {
	height: 22px;
	margin: 0;
	padding: 0 4px;
	border: none;
	background-color: transparent;
	display: flex;
	align-items: center;

	&-name {
		flex: 1;
		font-family: Alibaba PuHuiTi;
		font-size: 16px;
		color: #ffffff;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		margin: 0 4px;
	}
}

.weather-icon {
	width: 36px;
	aspect-ratio: 1;
	margin-top: 6px;
}

.weather-desc {
	display: flex;
	flex-direction: row;
	margin-top: 8px;
	font-size: 14px;
	color: #ffffff;

	&-text {
		font-family: Alibaba PuHuiTi;
		font-weight: 500;
	}

	&-split {
		font-family: Alibaba PuHuiTi;
		font-weight: 500;
		padding: 0 4px;
	}

	&-temp {
		font-family: D-DIN-PRO;
		font-weight: 500;
	}

	&-unit {
		font-family: D-DIN-PRO;
		font-weight: 700;
	}
}

.warning {
	margin-top: 4px;
	display: flex;
	flex-direction: row;

	&-icon {
		width: 18px;
		aspect-ratio: 1;
	}

	&-text {
		margin-left: 4px;
		font-family: Alibaba PuHuiTi;
		font-size: 12px;
		color: #ffad29;
		display: flex;
		align-items: center;
	}
}

.weather-metrics {
	height: 100%;
	display: flex;
	flex-direction: row;
	justify-content: space-evenly;

	&-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;

		svg {
			width: 24px;
			height: 24px;
		}

		&-text {
			font-family: Alibaba PuHuiTi, D-DIN-PRO;
			font-size: 12px;
			color: #008aff;
		}
	}
}

.weather-hours {
	height: 100%;
	display: flex;
	flex-direction: row;
	justify-content: space-evenly;
	&-item {
		display: flex;
		flex-direction: column;
		row-gap: 4px;
		align-items: center;
		justify-content: center;
		font-family: Alibaba PuHuiTi, D-DIN-PRO;
		font-size: 12px;
		color: #ffffff;

		// &-time {
		// }

		&-icon {
			img {
				width: 16px;
				height: 16px;
			}
		}

		// &-temp {
		// }

		&-humi {
			display: flex;
			align-items: center;

			svg {
				width: 12px;
				height: 12px;
			}
		}
	}
}

.link {
	display: flex;
	flex-direction: row;
	align-items: center;
	cursor: pointer;
}
</style>

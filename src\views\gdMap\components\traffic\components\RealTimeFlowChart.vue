<template>
	<v-chart class="chart" :option="option" autoresize />
</template>

<script setup>
import * as echarts from "echarts";
import VChart from "vue-echarts";

const props = defineProps({
	data: {
		type: Array,
		default: [],
	},
});

const option = ref(null);

onMounted(() => {
	nextTick(() => {
		setOptions();
	});
});

watch(
	() => props.data,
	() => setOptions()
);

const setOptions = () => {
	const half = Math.ceil(props.data.length / 2);
	const leftList = props.data.slice(0, half);
	const rightList = props.data.slice(half);

	option.value = {
		color: ["#008AFF", "#29FE82"],
		tooltip: {
			trigger: "axis",
			axisPointer: {
				type: "shadow",
			},
			backgroundColor: "rgba(7,7,7,0.8)",
			borderWidth: 0,
			textStyle: {
				color: "#fff",
			},
		},
		legend: {
			left: "center",
			top: "0",
			textStyle: {
				fontFamily: "Alibaba PuHuiTi",
				fontSize: 12,
				color: "#C4E5FF",
			},
			itemWidth: 6,
			itemHeight: 6,
		},
		grid: [
			{
				show: false,
				left: "1%",
				top: "45",
				bottom: "0%",
				width: "18%",
			},
			{
				show: false,
				left: "27.5%",
				top: "45",
				bottom: "0%",
				width: "14%",
			},
			{
				show: false,
				left: "32%",
				top: "45",
				bottom: "0%",
				width: "18%",
			},
			{
				show: false,
				right: "32%",
				top: "45",
				bottom: "0%",
				width: "18%",
			},
			{
				show: false,
				right: "9.5%",
				top: "45",
				bottom: "0%",
				width: "14%",
			},
			{
				show: false,
				right: "1%",
				top: "45",
				bottom: "0%",
				width: "18%",
			},
		],
		xAxis: [
			{
				gridIndex: 0,
				type: "value",
				inverse: true,
				axisLine: {
					show: false,
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					show: false,
				},
				splitLine: {
					show: false,
				},
			},
			{
				gridIndex: 1,
				show: false,
			},
			{
				gridIndex: 2,
				axisLine: {
					show: false,
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					show: false,
				},
				splitLine: {
					show: false,
				},
			},
			{
				gridIndex: 3,
				type: "value",
				inverse: true,
				axisLine: {
					show: false,
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					show: false,
				},
				splitLine: {
					show: false,
				},
			},
			{
				gridIndex: 4,
				show: false,
			},
			{
				gridIndex: 5,
				axisLine: {
					show: false,
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					show: false,
				},
				splitLine: {
					show: false,
				},
			},
		],
		yAxis: [
			{
				gridIndex: 0,
				type: "category",
				name: "格尔木方向",
				nameGap: 0,
				nameTextStyle: {
					fontFamily: "Alibaba PuHuiTi",
					fontSize: 12,
					color: "#C4E5FF",
					padding: [0, 70, 0, 0],
				},
				axisLine: {
					show: false,
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					show: false,
				},
				data: leftList.map(({ label }) => label),
			},
			{
				gridIndex: 1,
				type: "category",
				axisLine: {
					show: false,
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					show: true,
					align: "center",
					fontFamily: "Alibaba PuHuiTi",
					fontSize: 12,
					color: "rgba(196,229,255,0.7)",
				},
				axisPointer: {
					show: false,
				},
				data: leftList.map(({ label }) => label),
			},
			{
				gridIndex: 2,
				type: "category",
				name: "那曲方向",
				nameGap: 0,
				nameTextStyle: {
					fontFamily: "Alibaba PuHuiTi",
					fontSize: 12,
					color: "#C4E5FF",
					padding: [0, 0, 0, 70],
				},
				axisLine: {
					show: false,
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					show: false,
				},
				data: leftList.map(({ label }) => label),
			},
			{
				gridIndex: 3,
				type: "category",
				name: "格尔木方向",
				nameGap: 0,
				nameTextStyle: {
					fontFamily: "Alibaba PuHuiTi",
					fontSize: 12,
					color: "#C4E5FF",
					padding: [0, 70, 0, 0],
				},
				axisLine: {
					show: false,
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					show: false,
				},
				data: rightList.map(({ label }) => label),
			},
			{
				gridIndex: 4,
				type: "category",
				axisLine: {
					show: false,
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					show: true,
					align: "center",
					fontFamily: "Alibaba PuHuiTi",
					fontSize: 12,
					color: "rgba(196,229,255,0.7)",
				},
				axisPointer: {
					show: false,
				},
				data: rightList.map(({ label }) => label),
			},
			{
				gridIndex: 5,
				type: "category",
				name: "那曲方向",
				nameGap: 0,
				nameTextStyle: {
					fontFamily: "Alibaba PuHuiTi",
					fontSize: 12,
					color: "#C4E5FF",
					padding: [0, 0, 0, 70],
				},
				axisLine: {
					show: false,
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					show: false,
				},
				data: rightList.map(({ label }) => label),
			},
		],
		series: [
			{
				name: "车流量(辆)",
				type: "bar",
				itemStyle: {
					color: "#008AFF",
				},
			},
			{
				name: "平均通过速度(km/h)",
				type: "bar",
				itemStyle: {
					color: "#29FF82",
				},
			},
			{
				xAxisIndex: 0,
				yAxisIndex: 0,
				name: "车流量(辆)",
				type: "bar",
				barWidth: 4,
				itemStyle: {
					color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
						{ offset: 0, color: "#008AFF" },
						{ offset: 1, color: "#002544" },
					]),
				},
				data: leftList.map(({ car2 }) => car2),
			},
			{
				xAxisIndex: 0,
				yAxisIndex: 0,
				name: "平均通过速度(km/h)",
				type: "bar",
				barWidth: 4,
				itemStyle: {
					color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
						{ offset: 0, color: "#29FF82" },
						{ offset: 1, color: "#00471E" },
					]),
				},
				data: leftList.map(({ speed2 }) => speed2),
			},
			{
				xAxisIndex: 2,
				yAxisIndex: 2,
				name: "车流量(辆)",
				type: "bar",
				barWidth: 4,
				itemStyle: {
					color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
						{ offset: 0, color: "#008AFF" },
						{ offset: 1, color: "#002544" },
					]),
				},
				data: leftList.map(({ car1 }) => car1),
			},
			{
				xAxisIndex: 2,
				yAxisIndex: 2,
				name: "平均通过速度(km/h)",
				type: "bar",
				barWidth: 4,
				itemStyle: {
					color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
						{ offset: 0, color: "#29FF82" },
						{ offset: 1, color: "#00471E" },
					]),
				},
				data: leftList.map(({ speed1 }) => speed1),
			},
			{
				xAxisIndex: 3,
				yAxisIndex: 3,
				name: "车流量(辆)",
				type: "bar",
				barWidth: 4,
				itemStyle: {
					color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
						{ offset: 0, color: "#008AFF" },
						{ offset: 1, color: "#002544" },
					]),
				},
				data: rightList.map(({ car2 }) => car2),
			},
			{
				xAxisIndex: 3,
				yAxisIndex: 3,
				name: "平均通过速度(km/h)",
				type: "bar",
				barWidth: 4,
				itemStyle: {
					color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
						{ offset: 0, color: "#29FF82" },
						{ offset: 1, color: "#00471E" },
					]),
				},
				data: rightList.map(({ speed2 }) => speed2),
			},
			{
				xAxisIndex: 5,
				yAxisIndex: 5,
				name: "车流量(辆)",
				type: "bar",
				barWidth: 4,
				itemStyle: {
					color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
						{ offset: 0, color: "#008AFF" },
						{ offset: 1, color: "#002544" },
					]),
				},
				data: rightList.map(({ car1 }) => car1),
			},
			{
				xAxisIndex: 5,
				yAxisIndex: 5,
				name: "平均通过速度(km/h)",
				type: "bar",
				barWidth: 2,
				itemStyle: {
					color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
						{ offset: 0, color: "#29FF82" },
						{ offset: 1, color: "#00471E" },
					]),
				},
				data: rightList.map(({ speed1 }) => speed1),
			},
		],
	};
};
</script>

<style lang="scss" scoped>
.chart {
	width: 100%;
	height: 100%;
}
</style>

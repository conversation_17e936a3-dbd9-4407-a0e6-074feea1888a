<svg width="35" height="72" viewBox="0 0 35 72" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#232;&#174;&#190;&#229;&#164;&#135;&#233;&#162;&#132;&#232;&#173;&#166;">
<g id="Group 1321316351">
<g id="Group 1321316348">
<g id="&#230;&#137;&#169;&#230;&#149;&#163;&#229;&#133;&#137;">
<g id="&#230;&#164;&#173;&#229;&#156;&#134;&#229;&#189;&#162;" filter="url(#filter0_f_508_940)">
<ellipse cx="17.1964" cy="57.1852" rx="5.87197" ry="3.91465" fill="#EB5042"/>
</g>
<g id="&#230;&#164;&#173;&#229;&#156;&#134;&#229;&#189;&#162;_2" filter="url(#filter1_f_508_940)">
<ellipse cx="17.1965" cy="55.7871" rx="3.77484" ry="2.51656" fill="#EB5042"/>
</g>
<g id="&#230;&#164;&#173;&#229;&#156;&#134;&#229;&#189;&#162;_3" filter="url(#filter2_f_508_940)">
<ellipse cx="17.1963" cy="55.263" rx="1.41556" ry="0.943709" fill="white"/>
</g>
</g>
<g id="&#232;&#183;&#175;&#229;&#190;&#132;">
<g filter="url(#filter3_i_508_940)">
<path d="M17.1965 0C26.6938 0 34.393 7.69913 34.393 17.1965C34.393 20.6166 32.9277 24.0551 31.0017 27.0974C26.2189 34.6528 20.5009 41.8585 17.8327 50.3931L17.737 50.6992C17.5714 51.229 16.8216 51.229 16.656 50.6992C13.9287 41.9738 7.96922 34.6582 3.1782 26.8726C1.35824 23.9151 0 20.5758 0 17.1965C0 7.69913 7.69913 0 17.1965 0Z" fill="url(#paint0_linear_508_940)"/>
</g>
<path d="M17.1965 0.5C26.4177 0.5 33.893 7.97527 33.893 17.1965C33.893 20.4808 32.4823 23.8238 30.5793 26.83C29.5 28.5348 28.3668 30.2299 27.2282 31.9329C23.363 37.7141 19.4367 43.5869 17.3555 50.2439L17.2598 50.55C17.2519 50.5753 17.2424 50.5823 17.238 50.5854C17.2302 50.5907 17.2159 50.5965 17.1965 50.5965C17.1771 50.5965 17.1628 50.5907 17.1551 50.5854C17.1506 50.5823 17.1411 50.5753 17.1332 50.55C15.7511 46.1282 13.554 42.0763 11.1151 38.1687C9.95511 36.3101 8.73695 34.479 7.52433 32.6561C7.46224 32.5628 7.40016 32.4694 7.33811 32.3761C6.06264 30.4585 4.79653 28.5484 3.60403 26.6105C1.80868 23.693 0.5 20.4464 0.5 17.1965C0.5 7.97527 7.97527 0.5 17.1965 0.5Z" stroke="url(#paint1_linear_508_940)"/>
</g>
<g id="&#230;&#164;&#173;&#229;&#156;&#134;&#229;&#189;&#162;_4" filter="url(#filter4_i_508_940)">
<path d="M6.51967 28.1024C0.623002 22.2057 0.623002 12.6453 6.51967 6.74867C12.4163 0.852006 21.9767 0.852006 27.8734 6.74867C33.77 12.6453 33.77 22.2057 27.8734 28.1024C21.9767 33.999 12.4163 33.999 6.51967 28.1024Z" fill="url(#paint2_radial_508_940)"/>
</g>
</g>
</g>
<g id="Subtract" filter="url(#filter5_di_508_940)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M9.73211 17.59V24.1552H7.41696C7.32354 24.155 7.23102 24.1732 7.14466 24.2088C7.05831 24.2444 6.97985 24.2967 6.91379 24.3627C6.84773 24.4286 6.79537 24.507 6.75972 24.5932C6.72406 24.6794 6.70582 24.7718 6.70604 24.8651C6.70604 25.257 7.02503 25.575 7.41698 25.575H25.5632C25.9551 25.575 26.2741 25.2565 26.2741 24.8651C26.2741 24.4737 25.9551 24.1552 25.5632 24.1552H11.1518L11.1524 24.1552H23.2458V17.59C23.2458 13.8649 20.2219 10.8426 16.4892 10.8426C12.756 10.8426 9.73211 13.8643 9.73211 17.59ZM22.3944 7.93907C22.3495 7.77124 22.24 7.62795 22.0897 7.54048L22.0858 7.54046C22.0115 7.49796 21.9296 7.4705 21.8447 7.45963C21.7598 7.44876 21.6736 7.4547 21.591 7.47711C21.5084 7.49953 21.431 7.53797 21.3633 7.59025C21.2956 7.64253 21.2389 7.70762 21.1963 7.7818L19.9988 9.86206L21.131 10.5165L22.3292 8.43629C22.4158 8.28565 22.4392 8.10691 22.3944 7.93907ZM17.1958 6.66044C17.1958 6.29461 16.879 6 16.4871 6C16.0951 6 15.7783 6.29461 15.7783 6.65879V9.01791H17.1958V6.66044ZM11.13 7.45838C11.0448 7.46961 10.9627 7.49751 10.8883 7.54048L10.8878 7.54046C10.7376 7.62751 10.6281 7.77041 10.5832 7.9379C10.5383 8.10538 10.5617 8.2838 10.6483 8.43411L11.8475 10.5149L12.9798 9.85988L11.7816 7.77964C11.7388 7.7053 11.6816 7.64012 11.6135 7.58785C11.5454 7.53558 11.4676 7.49723 11.3847 7.47502C11.3017 7.4528 11.2151 7.44715 11.13 7.45838ZM6.95711 11.4594C6.88906 11.5118 6.83204 11.5771 6.78934 11.6515H6.78875C6.70205 11.8018 6.67857 11.9804 6.72347 12.148C6.76836 12.3156 6.87797 12.4586 7.02826 12.5456L9.10451 13.7497L9.75772 12.6147L7.68209 11.4123C7.60781 11.3692 7.52573 11.3412 7.44057 11.3299C7.35542 11.3186 7.26886 11.3243 7.18589 11.3465C7.10292 11.3687 7.02517 11.4071 6.95711 11.4594ZM26.2539 12.1487C26.2988 11.9809 26.2753 11.8021 26.1887 11.6515C26.1887 11.6507 26.1883 11.6503 26.188 11.6499C26.1875 11.6494 26.1871 11.6488 26.1871 11.6477C26.1001 11.4985 25.9576 11.3898 25.7905 11.3454C25.6235 11.3009 25.4456 11.3244 25.2959 11.4107L23.2208 12.6125L23.874 13.748L25.9492 12.5473C26.0995 12.4598 26.209 12.3165 26.2539 12.1487ZM18.8502 17.3195L20.3812 16.4451C20.4189 16.4236 20.4605 16.4097 20.5036 16.4042C20.5467 16.3987 20.5905 16.4018 20.6324 16.4132C20.6743 16.4247 20.7136 16.4442 20.7479 16.4708C20.7823 16.4974 20.811 16.5305 20.8326 16.5682C20.8541 16.6059 20.868 16.6475 20.8735 16.6906C20.879 16.7337 20.8759 16.7774 20.8645 16.8193C20.853 16.8612 20.8334 16.9004 20.8068 16.9348C20.7802 16.9691 20.7471 16.9979 20.7094 17.0194L18.9439 18.0277C18.8778 18.0655 18.8006 18.0793 18.7254 18.0668C18.6503 18.0544 18.5817 18.0163 18.5314 17.9591L17.7805 17.1049H17.1709C17.0662 17.1046 16.9641 17.0724 16.8782 17.0127C16.7923 16.953 16.7266 16.8685 16.6899 16.7705H15.238C15.3276 16.5456 15.3742 16.3059 15.3753 16.0638C15.3753 15.8299 15.3271 15.6086 15.2513 15.4006H16.6899C16.7267 15.3027 16.7924 15.2183 16.8783 15.1586C16.9642 15.0989 17.0662 15.0668 17.1709 15.0664H17.7804L18.5314 14.2124C18.5816 14.155 18.6501 14.1168 18.7254 14.1043C18.8006 14.0918 18.8779 14.1058 18.9439 14.1439L20.7094 15.1523C20.7856 15.1958 20.8413 15.2677 20.8645 15.3523C20.8876 15.4369 20.8761 15.5273 20.8326 15.6034C20.7891 15.6796 20.7171 15.7353 20.6324 15.7584C20.5478 15.7815 20.4574 15.7701 20.3812 15.7266L18.8502 14.8526L18.177 15.6176V16.5537L18.8502 17.3195ZM17.2729 21.22H18.8587C18.9174 21.22 18.9757 21.2316 19.03 21.254C19.0843 21.2765 19.1336 21.3095 19.1752 21.351C19.2167 21.3926 19.2497 21.4419 19.2722 21.4962C19.2947 21.5505 19.3062 21.6087 19.3062 21.6674V22.4275C19.3062 22.4862 19.2947 22.5444 19.2722 22.5987C19.2497 22.653 19.2168 22.7023 19.1752 22.7438C19.1336 22.7854 19.0843 22.8183 19.03 22.8408C18.9757 22.8633 18.9174 22.8748 18.8587 22.8748H13.5807C13.522 22.8748 13.4638 22.8632 13.4095 22.8408C13.3552 22.8183 13.3058 22.7853 13.2643 22.7438C13.2227 22.7022 13.1898 22.6529 13.1673 22.5986C13.1448 22.5444 13.1333 22.4862 13.1333 22.4275V21.6674C13.1333 21.6087 13.1448 21.5505 13.1673 21.4962C13.1898 21.4419 13.2227 21.3926 13.2643 21.3511C13.3058 21.3095 13.3552 21.2766 13.4095 21.2541C13.4638 21.2316 13.522 21.22 13.5807 21.22H15.0697L13.2062 17.9939C13.2274 17.9963 13.2484 17.9991 13.2694 18.002C13.3198 18.0089 13.3703 18.0158 13.4225 18.0158C14.0566 18.0158 14.6154 17.7073 14.9724 17.2379L17.2729 21.22ZM13.423 14.7433C14.1516 14.7433 14.744 15.3357 14.744 16.0638C14.744 16.7924 14.151 17.3844 13.4225 17.3844C12.694 17.3844 12.1011 16.7919 12.1011 16.0638C12.1011 15.3357 12.6944 14.7433 13.423 14.7433ZM12.9812 15.7686C12.9227 15.856 12.8915 15.9587 12.8915 16.0638H12.8906C12.8907 16.2049 12.9469 16.3402 13.0468 16.4399C13.1468 16.5396 13.2822 16.5956 13.4234 16.5955C13.5286 16.5954 13.6313 16.5642 13.7188 16.5058C13.8062 16.4474 13.8743 16.3644 13.9145 16.2673C13.9548 16.1702 13.9653 16.0633 13.9447 15.9602C13.9242 15.8572 13.8736 15.7625 13.7992 15.6882C13.7249 15.6138 13.6301 15.5632 13.527 15.5427C13.4239 15.5222 13.317 15.5327 13.2198 15.5729C13.1227 15.6131 13.0396 15.6812 12.9812 15.7686Z" fill="url(#paint3_linear_508_940)"/>
</g>
</g>
<defs>
<filter id="filter0_f_508_940" x="0.723164" y="42.6692" width="32.9465" height="29.0317" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="5.30065" result="effect1_foregroundBlur_508_940"/>
</filter>
<filter id="filter1_f_508_940" x="9.88786" y="49.7367" width="14.6171" height="12.1007" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.76688" result="effect1_foregroundBlur_508_940"/>
</filter>
<filter id="filter2_f_508_940" x="14.0139" y="52.5525" width="6.36482" height="5.42146" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.883442" result="effect1_foregroundBlur_508_940"/>
</filter>
<filter id="filter3_i_508_940" x="0" y="0" width="34.3931" height="51.0967" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.858582 0 0 0 0 0.156106 0 0 0 0 0.156106 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_508_940"/>
</filter>
<filter id="filter4_i_508_940" x="2.09717" y="2.32617" width="30.1987" height="32.1982" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_508_940"/>
</filter>
<filter id="filter5_di_508_940" x="4.67617" y="3.975" width="23.625" height="23.6252" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.0125"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.225296 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_508_940"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_508_940" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.0125"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_508_940"/>
</filter>
<linearGradient id="paint0_linear_508_940" x1="14.5" y1="9" x2="20.7084" y2="51.9203" gradientUnits="userSpaceOnUse">
<stop stop-color="#F4A8A1"/>
<stop offset="1" stop-color="#D93526"/>
</linearGradient>
<linearGradient id="paint1_linear_508_940" x1="17.1965" y1="0" x2="17.1965" y2="52.4283" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFA06B"/>
<stop offset="1" stop-color="#FF3426"/>
</linearGradient>
<radialGradient id="paint2_radial_508_940" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(6.51974 6.74893) rotate(-135) scale(30.1987)">
<stop stop-color="#FF8A8B"/>
<stop offset="1" stop-color="#932021"/>
</radialGradient>
<linearGradient id="paint3_linear_508_940" x1="16.4887" y1="6" x2="16.4887" y2="25.575" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#FFC1C1"/>
</linearGradient>
</defs>
</svg>

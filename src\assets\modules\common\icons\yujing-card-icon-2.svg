<svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Subtract" filter="url(#filter0_i_508_1043)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M2.89512 10.1207V15.487H1.00279C0.92643 15.4868 0.850803 15.5017 0.780221 15.5308C0.709638 15.5599 0.645508 15.6026 0.591512 15.6565C0.537516 15.7104 0.494719 15.7745 0.465578 15.845C0.436437 15.9154 0.421526 15.991 0.421702 16.0672C0.421702 16.3876 0.682439 16.6475 1.00281 16.6475H15.8349C16.1553 16.6475 16.4161 16.3871 16.4161 16.0672C16.4161 15.7473 16.1553 15.487 15.8349 15.487H4.05553L4.056 15.487H13.9408V10.1207C13.9408 7.07596 11.4692 4.60569 8.41819 4.60569C5.36679 4.60569 2.89512 7.07552 2.89512 10.1207ZM13.2449 2.2324C13.2082 2.09522 13.1186 1.9781 12.9958 1.9066L12.9927 1.90658C12.932 1.87185 12.865 1.8494 12.7956 1.84052C12.7262 1.83163 12.6557 1.83649 12.5882 1.85481C12.5207 1.87313 12.4575 1.90455 12.4021 1.94728C12.3468 1.99001 12.3004 2.04322 12.2656 2.10385L11.2868 3.80419L12.2122 4.33912L13.1916 2.63881C13.2624 2.51568 13.2815 2.36958 13.2449 2.2324ZM8.99571 1.18728C8.99571 0.888266 8.73676 0.647461 8.41641 0.647461C8.09604 0.647461 7.83711 0.888266 7.83709 1.18593V3.11421H8.99571V1.18728ZM4.03769 1.8395C3.96807 1.84868 3.90095 1.87148 3.84018 1.9066L3.83974 1.90658C3.71701 1.97774 3.6275 2.09454 3.59081 2.23144C3.55411 2.36833 3.57323 2.51417 3.64396 2.63703L4.62417 4.3378L5.54965 3.80241L4.57033 2.10209C4.5353 2.04132 4.48861 1.98804 4.43294 1.94532C4.37726 1.90259 4.31369 1.87125 4.24587 1.8531C4.17804 1.83494 4.1073 1.83032 4.03769 1.8395ZM0.626924 5.10984C0.571297 5.15263 0.524691 5.20597 0.489788 5.26681H0.489306C0.41844 5.38971 0.399253 5.53564 0.435949 5.67264C0.472645 5.80963 0.562231 5.92651 0.685076 5.99767L2.38214 6.98179L2.91605 6.0541L1.21949 5.07133C1.15878 5.0361 1.09169 5.01321 1.02209 5.00398C0.952484 4.99474 0.881737 4.99934 0.81392 5.01751C0.746102 5.03568 0.682551 5.06706 0.626924 5.10984ZM16.3995 5.67322C16.4362 5.53604 16.417 5.38994 16.3462 5.26681C16.3462 5.26548 16.3449 5.26548 16.3449 5.2637C16.2738 5.14175 16.1573 5.05289 16.0208 5.01658C15.8842 4.98028 15.7389 4.99948 15.6165 5.06999L13.9204 6.05233L14.4543 6.98046L16.1504 5.99902C16.2733 5.92753 16.3628 5.81041 16.3995 5.67322ZM10.3481 9.89925L11.5994 9.18456C11.6302 9.16695 11.6643 9.15559 11.6995 9.15112C11.7347 9.14666 11.7705 9.14917 11.8047 9.15851C11.839 9.16786 11.8711 9.18386 11.8992 9.2056C11.9272 9.22734 11.9507 9.25439 11.9684 9.28522C11.986 9.31604 11.9973 9.35003 12.0018 9.38524C12.0063 9.42045 12.0038 9.4562 11.9944 9.49044C11.9851 9.52468 11.9691 9.55675 11.9473 9.58481C11.9256 9.61287 11.8985 9.63637 11.8676 9.65398L10.4246 10.4781C10.3706 10.509 10.3075 10.5203 10.2461 10.5101C10.1846 10.4999 10.1286 10.4688 10.0874 10.4221L9.47365 9.72388H8.9754C8.88985 9.72361 8.80642 9.69732 8.73619 9.6485C8.66597 9.59968 8.61227 9.53064 8.58226 9.45058H7.39555C7.46877 9.26673 7.50682 9.07077 7.50772 8.87289C7.50772 8.68175 7.46838 8.50085 7.40637 8.33084H8.58226C8.61231 8.25082 8.66601 8.18182 8.73624 8.13303C8.80646 8.08424 8.88987 8.05797 8.9754 8.0577H9.47357L10.0874 7.35966C10.1284 7.31271 10.1845 7.28146 10.246 7.27124C10.3075 7.26101 10.3706 7.27245 10.4246 7.3036L11.8676 8.12785C11.9299 8.1634 11.9755 8.22222 11.9944 8.29138C12.0133 8.36053 12.0039 8.43436 11.9684 8.49661C11.9328 8.55885 11.8739 8.60443 11.8047 8.62331C11.7356 8.64218 11.6617 8.63282 11.5994 8.59726L10.3481 7.88289L9.7978 8.50822V9.27336L10.3481 9.89925ZM9.0588 13.0874H10.3549C10.403 13.0874 10.4506 13.0969 10.495 13.1152C10.5393 13.1336 10.5797 13.1606 10.6136 13.1945C10.6476 13.2285 10.6746 13.2688 10.6929 13.3132C10.7113 13.3575 10.7208 13.4051 10.7208 13.4531V14.0744C10.7208 14.1224 10.7113 14.1699 10.693 14.2143C10.6746 14.2587 10.6476 14.299 10.6137 14.333C10.5797 14.3669 10.5394 14.3939 10.495 14.4122C10.4506 14.4306 10.403 14.44 10.3549 14.44H6.04092C5.99288 14.44 5.94532 14.4305 5.90094 14.4122C5.85656 14.3938 5.81624 14.3669 5.78228 14.3329C5.74831 14.299 5.72138 14.2586 5.703 14.2143C5.68462 14.1699 5.67517 14.1224 5.67518 14.0744V13.4531C5.67517 13.4051 5.68462 13.3576 5.703 13.3132C5.72137 13.2688 5.74831 13.2285 5.78227 13.1946C5.81623 13.1606 5.85655 13.1337 5.90093 13.1153C5.94531 13.0969 5.99288 13.0874 6.04092 13.0874H7.25799L5.73479 10.4505C5.75209 10.4524 5.76929 10.4548 5.78649 10.4571C5.82767 10.4628 5.86888 10.4684 5.91161 10.4684C6.42989 10.4684 6.88664 10.2163 7.17843 9.83255L9.0588 13.0874ZM5.91201 7.7936C6.50753 7.7936 6.99176 8.27775 6.99176 8.87289C6.99176 9.46843 6.50705 9.95235 5.91161 9.95235C5.31617 9.95235 4.83154 9.46803 4.83154 8.87289C4.83154 8.27775 5.3165 7.7936 5.91201 7.7936ZM5.55085 8.6316C5.50308 8.70302 5.47756 8.78699 5.47753 8.87289H5.47681C5.47692 8.98824 5.52286 9.09883 5.60453 9.18033C5.68621 9.26183 5.79692 9.30756 5.91233 9.30748C5.99828 9.30745 6.08229 9.28194 6.15374 9.2342C6.22519 9.18645 6.28088 9.1186 6.31376 9.03923C6.34663 8.95985 6.35523 8.87252 6.33845 8.78826C6.32168 8.70401 6.28028 8.62661 6.21951 8.56587C6.15873 8.50512 6.0813 8.46375 5.997 8.44698C5.91271 8.43022 5.82533 8.43881 5.74592 8.47167C5.6665 8.50453 5.59862 8.56018 5.55085 8.6316Z" fill="url(#paint0_linear_508_1043)"/>
</g>
<defs>
<filter id="filter0_i_508_1043" x="0.417725" y="0.647461" width="16" height="16" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.858582 0 0 0 0 0.156106 0 0 0 0 0.156106 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_508_1043"/>
</filter>
<linearGradient id="paint0_linear_508_1043" x1="7.16329" y1="3.39407" x2="8.42087" y2="16.6472" gradientUnits="userSpaceOnUse">
<stop stop-color="#F4A8A1"/>
<stop offset="1" stop-color="#D93526"/>
</linearGradient>
</defs>
</svg>

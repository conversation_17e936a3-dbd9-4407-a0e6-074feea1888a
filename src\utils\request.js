/**
 * 使用示例
 * GET请求
 * FetchUtil.get("/api/users", { page: 1, limit: 10 }).then((data) => console.log("用户数据:", data));
 * 
 * POST请求
 * FetchUtil.post("/api/users", { name: "张三", age: 25 }).then((data) => console.log("创建结果:", data);
 * 
 * 带自定义请求头
 * FetchUtil.post(
	"/api/auth/login",
	{ username: "admin", password: "123456" },
	{ "X-Custom-Header": "value" }
    ).then((data) => console.log("登录结果:", data));
);
 */

const FetchUtil = {
	/**
	 * GET 请求
	 * @param {string} url 请求地址
	 * @param {Object} params 查询参数对象
	 * @param {Object} headers 自定义请求头
	 * @returns {Promise} 返回Promise
	 */
	get: (url, params = {}, headers = {}) => {
		const queryString = Object.keys(params).length
			? `?${new URLSearchParams(params).toString()}`
			: "";

		return fetch(`${url}${queryString}`, {
			method: "GET",
			headers: {
				"Content-Type": "application/json",
				...headers,
			},
		})
			.then((response) => {
				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}
				return response.json();
			})
			.catch((error) => {
				console.error("GET请求错误:", error);
				throw error;
			});
	},

	/**
	 * POST 请求
	 * @param {string} url 请求地址
	 * @param {Object} data 请求体数据
	 * @param {Object} headers 自定义请求头
	 * @returns {Promise} 返回Promise
	 */
	post: (url, data = {}, headers = {}) => {
		return fetch(url, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				...headers,
			},
			body: JSON.stringify(data),
		})
			.then((response) => {
				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}
				return response.json();
			})
			.catch((error) => {
				console.error("POST请求错误:", error);
				throw error;
			});
	},
};

export default FetchUtil;

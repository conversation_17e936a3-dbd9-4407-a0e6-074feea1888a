// 支持 TypeScript 的配置提示（JSDoc 实现）
/**
 * @type {{
 *  inputDir: string
 *  outputDir?: string
 *  nameStyle: 'kebab' | 'camel' | 'pascal' | 'snake'
 *  hashLength: number
 *  excludeReg: RegExp
 *  dryRun: boolean
 * }}
 */
const config = {
  // 必填：需要处理的中文图标目录
  inputDir: 'public/assets/images/icons/map',

  // 可选：处理后的输出目录（不设置时原地覆盖）
  outputDir: '',

  // 命名风格（默认kebab-case）
  nameStyle: 'kebab',

  // 重名时追加的哈希长度（0表示禁用）
  hashLength: 4,

  // 排除文件的正则（例如排除带数字的文件）
  excludeReg: /[\d]/,

  // 试运行模式
  dryRun: false,
};

export default config;

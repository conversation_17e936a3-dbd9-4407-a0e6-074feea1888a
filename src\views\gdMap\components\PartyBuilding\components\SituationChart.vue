<template>
	<v-chart class="chart" :option="option" autoresize />
</template>

<script setup>
import VChart from "vue-echarts";

const props = defineProps({
	data: {
		type: Array,
		default: [],
	},
});

const option = ref(null);

onMounted(() => {
	setOptions();
});

watch(
	() => props.data,
	() => setOptions()
);

const setOptions = () => {
	option.value = {
		grid: {
			left: "8%",
			right: "4%",
			top: "24%",
			bottom: "16%",
		},
		legend: {
			top: 4,
			itemWidth: 18,
			itemHeight: 10,
			icon: "rect",
			textStyle: {
				fontFamily: "Alibaba PuHuiTi",
				fontSize: 12,
				color: " #FFFFFF",
			},
			data: ["应开会议次数", "实际开会议次数"],
		},
		tooltip: {
			show: true,
		},
		xAxis: {
			type: "category",
			data: props.data.map(({ name }) => name),
			axisLine: {
				lineStyle: {
					color: "#fff",
					opacity: 0.5,
				},
			},
			axisTick: {
				show: false,
			},
		},
		yAxis: {
			type: "value",
			name: "单位:  次",
			nameTextStyle: {
				color: "#F8CAC5",
				align: "center",
				padding: [0, 0, 0, -10],
			},
			splitLine: {
				lineStyle: {
					type: "dashed",
					color: "rgba(255, 255, 255, 0.2)",
				},
			},
		},
		axisLabel: {
			fontFamily: "Alibaba PuHuiTi",
			fontSize: "12px",
			color: "#F8CAC5",
		},
		series: [
			{
				name: "应开会议次数",
				type: "bar",
				barWidth: 12,
				itemStyle: {
					color: "#EF531D",
				},
				data: props.data.map(({ value1 }) => value1),
			},
			{
				name: "实际开会议次数",
				type: "bar",
				barWidth: 12,
				itemStyle: {
					color: "#F6E103",
				},
				data: props.data.map(({ value2 }) => value2),
			},
		],
	};
};
</script>

<style lang="scss" scoped>
.chart {
	width: 100%;
	height: 100%;
}
</style>

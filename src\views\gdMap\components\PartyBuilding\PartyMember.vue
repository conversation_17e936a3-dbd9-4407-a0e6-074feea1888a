<template>
	<BasicCard useHeaderSlot>
		<template #header>
			<div class="card-header">
				<div class="nav-tab">
					<div class="nav-tab-list">
						<template v-for="tab in tabList" :key="tab.value">
							<div
								class="nav-tab-item"
								:class="{ 'is-active': tab.value === activeTab }"
								@click="onClickTab(tab)"
							>
								<div class="nav-tab-item-label">
									{{ tab.label }}
								</div>
							</div>
						</template>
					</div>
					<div class="nav-tab-line" />
				</div>
				<DepartmentRadioGroup
					v-model="activeRadio"
					class="radio-group"
					@change="onChangeDepartment"
				/>
			</div>
		</template>
		<div class="party-member">
			<div class="card-content">
				<div v-show="activeTab === '0'" class="card-content-top">
					<div class="card-content-label">党组织人员构成</div>
					<div class="card-content-link" @click="onInfo">
						查看详情
						<icon-double-right />
					</div>
				</div>
				<div class="card-content-main" :class="activeTab === '1' ? 'col-2' : 'col-3'">
					<div v-for="item in dataList" :key="item.dataName" class="type">
						<div class="type-pic">
							<img :src="item.iconUrl" />
						</div>
						<div class="type-right">
							<div class="type-label">{{ item.label }}</div>
							<div class="type-value">{{ item.value }}</div>
						</div>
					</div>
				</div>
				<div class="card-content-footer">
					<div class="all">
						<div class="all-label">全部党员</div>
						<div class="all-bottom">
							<div class="all-value">{{ formattedTotal }}</div>
							<div class="all-unit">人</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</BasicCard>
	<PartyMemberModal ref="PartyMemberModalRef" />
</template>

<script setup>
import { usePartyBuildingStore } from "@/store";
import request from "@/utils/request";
import DepartmentRadioGroup from "./components/DepartmentRadioGroup.vue";
import PartyMemberModal from "./components/PartyMemberModal.vue";

const { proxy } = getCurrentInstance();
const PartyBuildingStore = usePartyBuildingStore();

const tabList = [
	{ label: "人员构成", value: "0" },
	{ label: "党员发展", value: "1" },
];

const dataList1 = [
	{
		label: "正式党员",
		value: 0,
		dataName: "zhengshiMember",
		iconUrl: new URL("@/assets/modules/PartyBuilding/icon/party-member-1.svg", import.meta.url)
			.href,
	},
	{
		label: "预备党员",
		value: 0,
		dataName: "yubeiMember",
		iconUrl: new URL("@/assets/modules/PartyBuilding/icon/party-member-2.svg", import.meta.url)
			.href,
	},
	{
		label: "少数名族党员",
		value: 0,
		dataName: "shaoshuMember",
		iconUrl: new URL("@/assets/modules/PartyBuilding/icon/party-member-3.svg", import.meta.url)
			.href,
	},
	{
		label: "女党员",
		value: 0,
		dataName: "nvMember",
		iconUrl: new URL("@/assets/modules/PartyBuilding/icon/party-member-4.svg", import.meta.url)
			.href,
	},
	{
		label: "党务干部",
		value: 0,
		dataName: "dangwuMember",
		iconUrl: new URL("@/assets/modules/PartyBuilding/icon/party-member-5.svg", import.meta.url)
			.href,
	},
	{
		label: "发展对象",
		value: 0,
		dataName: "fazhanMember",
		iconUrl: new URL("@/assets/modules/PartyBuilding/icon/party-member-6.svg", import.meta.url)
			.href,
	},
	{
		label: "积极分子",
		value: 0,
		dataName: "jijiMember",
		iconUrl: new URL("@/assets/modules/PartyBuilding/icon/party-member-7.svg", import.meta.url)
			.href,
	},
	{
		label: "申请人",
		value: 0,
		dataName: "shenqingMember",
		iconUrl: new URL("@/assets/modules/PartyBuilding/icon/party-member-8.svg", import.meta.url)
			.href,
	},
	{
		label: "在岗职工数",
		value: 0,
		dataName: "zaigangMember",
		iconUrl: new URL("@/assets/modules/PartyBuilding/icon/party-member-9.svg", import.meta.url)
			.href,
	},
];

const dataList2 = [
	{
		label: "发展大专及以上学历党员",
		value: 0,
		dataName: "dazhuanMember",
		iconUrl: new URL("@/assets/modules/PartyBuilding/icon/party-member-10.svg", import.meta.url)
			.href,
	},
	{
		label: "发展文明党员",
		value: 0,
		dataName: "wenmingMember",
		iconUrl: new URL("@/assets/modules/PartyBuilding/icon/party-member-11.svg", import.meta.url)
			.href,
	},
	{
		label: "发展35岁及以下党员",
		value: 0,
		dataName: "sanwuMember",
		iconUrl: new URL("@/assets/modules/PartyBuilding/icon/party-member-12.svg", import.meta.url)
			.href,
	},
	{
		label: "发展女党员",
		value: 0,
		dataName: "nvMember",
		iconUrl: new URL("@/assets/modules/PartyBuilding/icon/party-member-13.svg", import.meta.url)
			.href,
	},
	{
		label: "发展产业工人党员",
		value: 0,
		dataName: "gongrenMember",
		iconUrl: new URL("@/assets/modules/PartyBuilding/icon/party-member-14.svg", import.meta.url)
			.href,
	},
	{
		label: "发展高知群体党员",
		value: 0,
		dataName: "gaozhiMember",
		iconUrl: new URL("@/assets/modules/PartyBuilding/icon/party-member-15.svg", import.meta.url)
			.href,
	},
];

const activeTab = ref("0");
const activeRadio = ref("全部");
const innerDataList = ref([]);
const total = ref(0);

const dataList = computed({
	get: () => innerDataList.value,
	set: (data) => {
		let _datalist = [];
		if (activeTab.value === "0") {
			_datalist = dataList1;
		} else if (activeTab.value === "1") {
			_datalist = dataList2;
		}
		innerDataList.value = _datalist.map((item) => ({ ...item, value: data[item.dataName] || 0 }));
	},
});

const formattedTotal = computed(() => {
	return new Intl.NumberFormat().format(unref(total));
});

onMounted(() => {
	getData();
});

const onClickTab = (tab) => {
	activeTab.value = tab.value;
	getData();
};

const onInfo = () => {
	proxy.$refs.PartyMemberModalRef.open();
};

const onChangeDepartment = () => {
	PartyBuildingStore.updateStore({ department: activeRadio.value });
	getData();
};

const getData = () => {
	if (activeTab.value === "0") {
		getStaffData();
	} else if (activeTab.value === "1") {
		getCompositionData();
	}
};

const getStaffData = () => {
	request.get("/api/screen/dangjian/stat/staff", { department: activeRadio.value }).then((res) => {
		if (res && res.code == 200) {
			dataList.value = res.data;
			const { zhengshiMember = 0, yubeiMember = 0 } = res.data;
			total.value = zhengshiMember + yubeiMember || 0;
		}
	});
};

const getCompositionData = () => {
	request
		.get("/api/screen/dangjian/stat/party/composition", { department: activeRadio.value })
		.then((res) => {
			if (res && res.code == 200) {
				dataList.value = res.data;
			}
		});
};
</script>

<style lang="scss" scoped>
.party-member {
	height: 498px;
	width: 100%;
}

.nav-tab {
	position: relative;
	margin-bottom: 12px;
	padding-bottom: 28px;

	&-list {
		display: flex;
		flex-direction: row;
		column-gap: 62px;
	}

	&-item {
		width: 181px;
		height: 64px;
		display: flex;
		align-items: end;
		justify-content: start;
		cursor: pointer;
		z-index: 1;
		background-image: url("@/assets/modules/PartyBuilding/bg/card-header-tab-bg.webp");
		background-size: 100% 100%;
		background-repeat: no-repeat;

		&.is-active {
			background-image: url("@/assets/modules/PartyBuilding/bg/card-header-tab-bg-active.webp");
		}

		&-label {
			font-family: Alibaba PuHuiTi;
			font-size: 20px;
			color: #ffe3cd;
			letter-spacing: 5px;
			text-shadow: 0px 15px 7px rgba(111, 24, 12, 0.5), 0px 2px 0px #e42a1c,
				0px 0px 30px rgba(255, 155, 95, 0.5);
			transform: skewX(-12deg);
			padding-bottom: 7px;
			padding-left: 42px;
		}
	}

	&-line {
		position: absolute;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 54px;
		background-image: url("@/assets/modules/PartyBuilding/bg/card-header-line-bg.webp");
		background-size: auto 100%;
		background-repeat: no-repeat;
	}
}

.radio-group {
	margin-bottom: 4px;
}

.card-content {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
	overflow: hidden;

	&-top {
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 10px 12px;
	}

	&-main {
		display: grid;
		row-gap: 40px;
		margin-top: auto;
		padding: 0px 32px;
		grid-template-rows: repeat(3, 1fr);

		&.col-2 {
			grid-template-columns: repeat(2, 1fr);

			.type {
				&-right {
					width: 190px;
				}
			}
		}

		&.col-3 {
			grid-template-columns: repeat(3, 1fr);

			.type {
				&:nth-child(3n + 1) {
					justify-content: start;
				}

				&:nth-child(3n + 2) {
					justify-content: center;
				}

				&:nth-child(3n + 3) {
					justify-content: end;
				}

				&-right {
					width: 110px;
				}
			}
		}
	}

	&-footer {
		margin-top: auto;
		padding-bottom: 16px;
	}

	&-label {
		font-family: Alibaba PuHuiTi;
		font-size: 18px;
		color: #e4f2ff;
		margin-right: 16px;
	}

	&-link {
		font-family: Alibaba PuHuiTi;
		font-size: 14px;
		color: #ffffff;
		cursor: pointer;
		display: flex;
		align-items: center;
	}
}

.type {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;

	&-pic {
		img {
			width: 48px;
			height: 48px;
		}
	}

	&-right {
		margin-left: 16px;
	}

	&-label {
		font-family: Alibaba PuHuiTi;
		font-size: 16px;
		color: #ffffff;
		letter-spacing: 1px;
	}

	&-value {
		font-family: D-DIN-PRO;
		font-weight: bold;
		font-size: 36px;
		letter-spacing: 2px;
		background-image: linear-gradient(180deg, #ffffff 24%, #fcffd9 48%, #ff4200 77%);
		background-clip: text;
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
	}
}

.all {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;

	&-label {
		font-family: Alibaba PuHuiTi;
		font-weight: bold;
		font-size: 36px;
		color: #ffffff;
		letter-spacing: 7px;
	}

	&-value {
		font-family: D-DIN-PRO;
		font-weight: bold;
		font-size: 96px;
		letter-spacing: 7px;
		display: flex;
		flex-direction: row;
		align-items: baseline;
		background-image: linear-gradient(180deg, #ffffff 24%, #fcffd9 48%, #ff4200 77%);
		background-clip: text;
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
	}

	&-unit {
		font-family: Alibaba PuHuiTi;
		font-weight: bold;
		font-size: 36px;
		color: #ffffff;
		margin-bottom: 16px;
		margin-left: 16px;
	}

	&-bottom {
		display: flex;
		flex-direction: row;
		align-items: end;
	}
}
</style>

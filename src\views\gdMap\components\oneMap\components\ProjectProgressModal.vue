<template>
  <Modal ref="ModalRef" title="项目进度明细">
    <template #title>
      <!-- <modelIconTitle class="icon-title" /> -->
      进度明细
    </template>

    <!-- Tab选项卡 -->
    <div class="progress-tabs">
      <div
        v-for="tab in tabs"
        :key="tab.key"
        class="tab-item"
        :class="{ active: activeTab === tab.key }"
        @click="handleTabClick(tab.key)"
      >
        {{ tab.label }}
      </div>
    </div>

    <!-- 类型选择器 -->
    <div class="type-selector">
      <div
        v-for="type in projectTypes"
        :key="type.key"
        class="type-item"
        :class="{ active: activeType === type.key }"
        @click="handleTypeClick(type.key)"
      >
        {{ type.label }}
      </div>
    </div>

    <!-- Tab内容区域 -->
    <div class="tab-content">
      <div v-if="activeTab === 'overview'" class="tab-panel">
        <!-- 施工概况内容 -->
        <!-- 图例区域 -->
        <div class="chart-legend">
          <div
            v-for="legend in chartLegends"
            :key="legend.key"
            class="legend-item"
            :class="{ active: legend.selected }"
            @click="toggleLegend(legend.key)"
          >
            <div class="legend-checkbox" :style="{ backgroundColor: legend.color }">
              <div v-if="legend.selected" class="checkbox-check">✓</div>
            </div>
            <div class="legend-label">{{ legend.label }}</div>
          </div>
        </div>

        <!-- 图表区域 -->
        <div class="chart-container">
          <MultiLineChart
            :data="chartData"
            :selected-legends="getSelectedLegendKeys()"
            height="270px"
            :show-data-zoom="false"
            :smooth="true"
            y-axis-unit="万元"
            completion-unit="%"
          />
        </div>

        <!-- 按钮区域 -->
        <div class="button-container">
          <button class="action-button" @click="openVideoModal">形象进度视频</button>
          <button class="action-button" @click="openDetailModal">明细台账</button>
        </div>
      </div>
      <div v-if="activeTab === 'detail'" class="tab-panel">
        <!-- 详细进度内容 -->
        <!-- 图例区域 -->
        <div class="chart-legend">
          <div
            v-for="legend in chartLegends"
            :key="legend.key"
            class="legend-item"
            :class="{ active: legend.selected }"
            @click="toggleLegend(legend.key)"
          >
            <div class="legend-checkbox" :style="{ backgroundColor: legend.color }">
              <div v-if="legend.selected" class="checkbox-check">✓</div>
            </div>
            <div class="legend-label">{{ legend.label }}</div>
          </div>
        </div>

        <!-- 图表区域 -->
        <div class="chart-container">
          <MultiLineChart
            :data="chartData"
            :selected-legends="getSelectedLegendKeys()"
            height="300px"
            :show-data-zoom="false"
            :smooth="true"
            y-axis-unit="万元"
            completion-unit="%"
          />
        </div>

        <!-- 按钮区域 -->
        <div class="button-container">
          <button class="action-button" @click="openVideoModal">形象进度视频</button>
          <button class="action-button" @click="openDetailModal">明细台账</button>
        </div>
      </div>
    </div>
  </Modal>

  <!-- 形象进度视频弹窗 -->
  <VideoProgressModal ref="videoModalRef" />

  <!-- 明细台账弹窗 -->
  <DetailLedgerModal ref="detailModalRef" />
</template>

<script setup>
import modelIconTitle from "@/assets/modules/traffic/icon/model-icon-title.svg?component";
import Modal from "../../traffic/components/Modal.vue";
import MultiLineChart from "@/components/MultiLineChart/index.vue";
import VideoProgressModal from "@/components/detailModal/VideoProgressModal.vue";
import DetailLedgerModal from "@/components/detailModal/DetailLedgerModal.vue";

const ModalRef = ref(null);
const videoModalRef = ref(null);
const detailModalRef = ref(null);

// Tab相关数据
const tabs = [
  { key: "overview", label: "施工情况" },
  { key: "detail", label: "计划进度" },
];

const activeTab = ref("overview");

// 项目类型数据
const projectTypes = [
  { key: "all", label: "所有" },
  { key: "bridge1", label: "格贡一" },
  { key: "test", label: "试验段" },
  { key: "bridge3", label: "格贡三" },
  { key: "bridge4", label: "格贡四" },
  { key: "jingna1", label: "贡那一" },
  { key: "jingna2", label: "贡那二" },
];

const activeType = ref("all");

// Tab切换处理
const handleTabClick = (tabKey) => {
  activeTab.value = tabKey;
};

// 类型切换处理
const handleTypeClick = (typeKey) => {
  activeType.value = typeKey;
};

// 获取当前类型名称
const getCurrentTypeName = () => {
  const currentType = projectTypes.find((type) => type.key === activeType.value);
  return currentType ? currentType.label : "所有";
};

// 施工概况图例数据
const overviewLegends = ref([
  { key: "total", label: "合产值", color: "#8B5CF6", selected: true },
  { key: "cumulative", label: "累计完成产值", color: "#EF4444", selected: true },
  { key: "bridge", label: "桥建设计总量", color: "#06B6D4", selected: true },
  { key: "equipment", label: "设备完成总值", color: "#F97316", selected: false },
  { key: "today", label: "今日完成产值", color: "#10B981", selected: false },
  { key: "road", label: "路面完成产值", color: "#3B82F6", selected: false },
  { key: "monthly", label: "本月完成产值", color: "#8B5CF6", selected: false },
  { key: "test", label: "测试工程量", color: "#84CC16", selected: false },
  { key: "bridge_design", label: "桥建设计总量", color: "#F59E0B", selected: false },
  { key: "village", label: "乡村设计总量", color: "#EF4444", selected: false },
  { key: "new_bridge", label: "新建设计总量", color: "#8B5CF6", selected: false },
  { key: "road_design", label: "路建设计总量", color: "#06B6D4", selected: false },
  { key: "new_road", label: "新路完成产值", color: "#10B981", selected: false },
  { key: "safety", label: "安全设施设计总量", color: "#3B82F6", selected: false },
  { key: "safety_complete", label: "安全设施完成产值", color: "#F97316", selected: false },
]);

// 详细进度图例数据
const detailLegends = ref([
  { key: "contract_amount", label: "暂定合同额", color: "#00D4AA", selected: true },
  { key: "monthly_plan", label: "月度产值计划", color: "#FF6B6B", selected: true },
]);

// 当前图例数据（根据activeTab动态切换）
const chartLegends = computed(() => {
  return activeTab.value === "overview" ? overviewLegends.value : detailLegends.value;
});

// 切换图例选中状态
const toggleLegend = (legendKey) => {
  // 根据当前Tab选择对应的图例数组
  const currentLegends =
    activeTab.value === "overview" ? overviewLegends.value : detailLegends.value;
  const legend = currentLegends.find((item) => item.key === legendKey);
  if (legend) {
    legend.selected = !legend.selected;
  }
};

// 获取已选择的图例名称
const getSelectedLegends = () => {
  return chartLegends.value.filter((legend) => legend.selected).map((legend) => legend.label);
};

// 获取已选择的图例键
const getSelectedLegendKeys = () => {
  return chartLegends.value.filter((legend) => legend.selected).map((legend) => legend.key);
};

// 施工概况图表数据
const overviewChartData = {
  categories: [
    "06-20",
    "06-21",
    "06-22",
    "06-23",
    "06-24",
    "06-25",
    "06-26",
    "06-27",
    "06-28",
    "06-29",
    "06-30",
    "07-01",
  ],
  series: [
    {
      key: "total",
      name: "总产值",
      color: "#8B5CF6",
      data: [50, 75, 100, 125, 150, 175, 200, 225, 250, 275, 300, 325],
    },
    {
      key: "cumulative",
      name: "累计完成产值",
      color: "#EF4444",
      data: [30, 45, 65, 85, 110, 140, 170, 200, 230, 260, 290, 320],
    },
    {
      key: "bridge",
      name: "临建设计总量",
      color: "#06B6D4",
      data: [20, 35, 50, 70, 90, 115, 145, 175, 205, 235, 265, 295],
    },
    {
      key: "equipment",
      name: "备料完成总值",
      color: "#F97316",
      data: [15, 25, 40, 55, 75, 95, 120, 145, 170, 195, 220, 245],
    },
    {
      key: "today",
      name: "今日完成产值",
      color: "#10B981",
      data: [5, 8, 12, 15, 20, 25, 30, 35, 40, 45, 50, 55],
    },
    {
      key: "road",
      name: "路面完成产值",
      color: "#3B82F6",
      data: [10, 18, 28, 38, 50, 65, 80, 95, 110, 125, 140, 155],
    },
    {
      key: "monthly",
      name: "本月完成产值",
      color: "#8B5CF6",
      data: [25, 40, 60, 80, 105, 130, 160, 190, 220, 250, 280, 310],
    },
    {
      key: "test",
      name: "累计完成比例",
      color: "#84CC16",
      data: [8, 15, 25, 35, 48, 62, 78, 95, 112, 130, 148, 166],
    },
    {
      key: "bridge_design",
      name: "桥涵设计总量",
      color: "#F59E0B",
      data: [12, 22, 35, 48, 65, 82, 102, 122, 142, 162, 182, 202],
    },
    {
      key: "village",
      name: "临建完成产值",
      color: "#EF4444",
      data: [18, 30, 45, 62, 80, 100, 122, 145, 168, 192, 216, 240],
    },
    {
      key: "new_bridge",
      name: "路基设计总量",
      color: "#8B5CF6",
      data: [22, 35, 52, 70, 90, 112, 136, 160, 185, 210, 235, 260],
    },
    {
      key: "road_design",
      name: "备料设计总量",
      color: "#06B6D4",
      data: [16, 28, 42, 58, 76, 96, 118, 140, 163, 186, 209, 232],
    },
    {
      key: "new_road",
      name: "路基完成产值",
      color: "#10B981",
      data: [14, 24, 38, 52, 68, 86, 105, 124, 144, 164, 184, 204],
    },
    {
      key: "safety",
      name: "路面设计总量",
      color: "#3B82F6",
      data: [11, 19, 30, 42, 56, 71, 88, 105, 123, 141, 159, 177],
    },
    {
      key: "safety_complete",
      name: "房建完成产值",
      color: "#F97316",
      data: [9, 16, 26, 37, 50, 64, 80, 96, 113, 130, 147, 164],
    },
  ],
};

// 详细进度图表数据
const detailChartData = {
  categories: [
    "06-20",
    "06-21",
    "06-22",
    "06-23",
    "06-24",
    "06-25",
    "06-26",
    "06-27",
    "06-28",
    "06-29",
    "06-30",
    "07-01",
  ],
  series: [
    {
      key: "contract_amount",
      name: "暂定合同额",
      color: "#00D4AA",
      data: [1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000],
    },
    {
      key: "monthly_plan",
      name: "月度产值计划",
      color: "#FF6B6B",
      data: [80, 120, 150, 180, 220, 250, 280, 320, 350, 380, 420, 450],
    },
  ],
};

// 当前图表数据（根据activeTab动态切换）
const chartData = computed(() => {
  return activeTab.value === "overview" ? overviewChartData : detailChartData;
});

const onOpen = () => {
  ModalRef.value?.open();
};

// 打开形象进度视频弹窗
const openVideoModal = () => {
  videoModalRef.value?.open();
};

// 打开明细台账弹窗
const openDetailModal = () => {
  detailModalRef.value?.open();
};

defineExpose({
  open: onOpen,
});
</script>

<style lang="scss" scoped>
.icon-title {
  width: 28px;
  height: 28px;
  margin-right: 12px;
  font-family: Alibaba PuHuiTi;
}

.progress-tabs {
  display: flex;
  margin-bottom: 10px;
  /* border-bottom: 1px solid rgba(7, 131, 250, 0.2); */
  background-color: #001220;
}

.tab-item {
  font-family: Alibaba PuHuiTi;
  padding: 10px 54px;
  font-size: 18px;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  color: #8a9ba8;
  background: transparent;
  border: none;

  &:hover {
    color: #c4e5ff;
  }

  &.active {
    color: #ffffff;
    background: linear-gradient(180deg, rgba(0, 138, 255, 0) 0%, #008aff 100%);
    border-radius: 0px 0px 0px 0px;

    &::before {
      content: "";
      position: absolute;
      top: -1px;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, #008aff 0%, #6dbcff 50%, #008aff 100%);
    }
  }
}

.tab-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 400px;
}

.tab-panel {
  flex: 1;
  padding: 20px 0;
}

.type-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  /* margin-bottom: 20px; */
  padding: 0 4px;
}

.type-item {
  padding: 8px 36px;
  font-size: 14px;
  font-family: Alibaba PuHuiTi;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s ease;
  color: #8a9ba8;
  background-color: transparent;
  background-image: url("@/assets/images/type-noselected.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  border: none;
  &.active {
    color: #ffffff !important;
    background-color: transparent !important;
    background-image: url("@/assets/images/type-selected.png") !important;
    background-repeat: no-repeat !important;
    background-size: 100% 100% !important;
    border: none !important;
  }

  &:hover:not(.active) {
    opacity: 0.8;
  }
}

.content-placeholder {
  padding: 40px 20px;
  text-align: center;
  color: #c4e5ff;
  font-size: 16px;
  font-family: Alibaba PuHuiTi;
}

/* 图例样式 */
.chart-legend {
  display: flex;
  flex-wrap: wrap;
  /* gap: 5px; */
}

.legend-item {
  width: 130px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: Alibaba PuHuiTi;
  opacity: 0.7;
}

.legend-item:hover {
  transform: translateY(-1px);
}

.legend-item.active {
  opacity: 1;
}

.legend-checkbox {
  width: 16px;
  height: 16px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.checkbox-check {
  color: white;
  font-size: 12px;
  font-weight: bold;
  line-height: 1;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
}

.legend-label {
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  white-space: nowrap;
}

/* 图表容器样式 */
.chart-container {
  flex: 1;
  /* background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1); */
  padding: 20px;
  min-height: 250px;
}

.chart-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 10px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.chart-info {
  text-align: center;
  line-height: 1.6;
  font-family: Alibaba PuHuiTi;
}

/* 按钮容器样式 */
.button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  /* margin-top: 20px; */
  /* padding: 20px 0; */
}

.action-button {
  padding: 10px 30px;
  font-size: 14px;
  font-family: Alibaba PuHuiTi;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s ease;
  color: #fff;
  background-color: transparent;
  background-image: url("@/assets/images/type-selected.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  border: none;
  min-width: 120px;

  &:hover {
    opacity: 0.8;
    transform: translateY(-1px);
  }

  &:active {
    color: #ffffff;
    background-image: url("@/assets/images/type-selected.png");
    transform: translateY(0);
  }
}

/* 弹窗内容样式 */
.modal-content {
  padding: 20px;
  min-height: 300px;
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 250px;
  text-align: center;
}

.placeholder-text {
  font-size: 18px;
  font-weight: bold;
  color: #c4e5ff;
  margin-bottom: 10px;
  font-family: Alibaba PuHuiTi;
}

.placeholder-desc {
  font-size: 14px;
  color: rgba(196, 229, 255, 0.7);
  font-family: Alibaba PuHuiTi;
}
</style>

import { AreaMarker } from "./types/AreaMarker";
import { FacilityMarker } from "./types/FacilityMarker";
import { PersonnelAlertMarker } from "./types/PersonnelAlertMarker";
import { FacilityWarnMarker } from "./types/FacilityWarnMarker";
import {
	TrafficAccidentMarker,
	TrafficDeviceMarker,
	TrafficAlarmMarker,
} from "./types/TrafficMarker";
import { StaffMarker } from "./types/StaffMarker";
import { EngineeringMarker } from "./types/EngineeringMarker";

/**
 * 标记工厂类
 * 根据标记类型创建对应的标记渲染器
 */
export class MarkerFactory {
	// 标记类型映射
	static markerTypes = {
		// 区域标记
		area: AreaMarker,
		// 工程设施标记
		facility: FacilityMarker,
		// 人员警情标记
		personnelAlert: PersonnelAlertMarker,
		// 设备预警
		facilityWarn: FacilityWarnMarker,
		// 交通事件
		trafficAccident: TrafficAccidentMarker,
		// 保通设备
		trafficDevice: TrafficDeviceMarker,
		// 拥堵预警点
		trafficAlarm: TrafficAlarmMarker,
		// 人员分布
		staff: StaffMarker,
		// 工程设别
		engineering: EngineeringMarker,
	};

	/**
	 * 创建标记渲染器
	 * @param {String} type - 标记类型
	 * @param {Object} map - MapLibre地图实例
	 * @param {String} sourceId - 数据源ID
	 * @param {Object} options - 渲染选项
	 * @returns {Object} 渲染结果
	 */
	static createMarker(type, map, sourceId, options = {}) {
		const MarkerClass = this.markerTypes[type];

		if (!MarkerClass) {
			console.warn(`未知的标记类型: ${type}，将使用默认标记`);
			// 可以返回一个默认标记或者抛出错误
			return null;
		}

		return MarkerClass.render(map, sourceId, options);
	}

	/**
	 * 移除标记
	 * @param {String} type - 标记类型
	 * @param {Object} map - MapLibre地图实例
	 * @param {String} sourceId - 数据源ID
	 * @param {Object} options
	 */
	static removeMarker(type, map, sourceId, options = {}) {
		const MarkerClass = this.markerTypes[type];

		if (MarkerClass) {
			MarkerClass.remove(map, sourceId, options);
		} else {
			console.warn(`未知的标记类型: ${type}，无法移除`);
		}
	}

	/**
	 * 更新标记数据
	 * @param {String} type - 标记类型
	 * @param {Object} map - MapLibre地图实例
	 * @param {String} sourceId - 数据源ID
	 * @param {Object} data - GeoJSON数据
	 */
	static updateMarkerData(type, map, sourceId, data) {
		const MarkerClass = this.markerTypes[type];

		if (MarkerClass) {
			MarkerClass.updateData(map, sourceId, data);
		} else {
			console.warn(`未知的标记类型: ${type}，无法更新数据`);
		}
	}

	/**
	 * 设置标记可见性
	 * @param {Object} map - MapLibre地图实例
	 * @param {String} type - 标记类型
	 * @param {Boolean} visible - 是否可见
	 * @param {Object} options
	 */
	static setMarkerVisibility(map, type, visible, options = {}) {
		const MarkerClass = this.markerTypes[type];

		if (MarkerClass) {
			MarkerClass.setVisibility(map, visible, options);
		} else {
			console.warn(`未知的标记类型: ${type}，无法设置可见性`);
		}
	}
}

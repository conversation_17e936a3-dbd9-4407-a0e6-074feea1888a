import gsap from "gsap";

/**
 * 模块动画系统
 * @param {Object} sidebarVisibility 侧边栏可见性状态对象
 * @returns {Object} 动画系统对象
 */
export function useModuleAnimation(sidebarVisibility) {
  // 基础配置
  const config = {
    duration: {
      fadeOut: 0.4, // 淡出动画时间
      fadeIn: 0.6, // 淡入动画时间
      transition: 0.2, // 过渡时间
      button: 0.3, // 按钮动画时间
    },
    ease: {
      out: "power3.out",
      in: "power2.in",
      inOut: "power2.inOut",
    },
    delay: {
      base: 0.05, // 基础延迟
      stagger: 0.08, // 错开延迟
      button: 0.1, // 按钮延迟
    },
  };

  /**
   * 创建动画时间线
   * @returns {Object} GSAP时间线对象
   */
  function createTimeline() {
    return gsap.timeline();
  }

  /**
   * 检查元素是否可见
   * @param {HTMLElement} element DOM元素
   * @returns {Boolean} 是否可见
   */
  function isElementVisible(element) {
    if (!element) return false;
    const style = window.getComputedStyle(element);
    return style.display !== "none" && style.visibility !== "hidden" && style.opacity !== "0";
  }

  /**
   * 获取模块的所有元素
   * @param {String} moduleKey 模块标识
   * @returns {Array} 元素数组
   */
  function getModuleElements(moduleKey) {
    const elements = [];

    // 获取特定模块的元素
    if (moduleKey === "oneMap") {
      // 一张图模块的元素
      const navigation = document.querySelector(".page-navigation-wrap");
      const legend = document.querySelector(".map-legend-wrap");

      if (navigation) elements.push(navigation);
      if (legend) elements.push(legend);
    } else if (moduleKey === "permafrost") {
      // 冻土-监测总览的元素
      const dongtuSwitch = document.querySelector(".dongtu-switch");
      const centerCard = document.querySelector(".center-card");
      if (dongtuSwitch) elements.push(dongtuSwitch);
      if (centerCard) elements.push(centerCard);
    } else if (moduleKey === "permafrost2") {
      // 冻土-实时监测的元素
      const dongtuSwitch = document.querySelector(".dongtu-switch");
      const selectPoint = document.querySelector(".select-point");
      if (selectPoint) elements.push(selectPoint);
      if (dongtuSwitch) elements.push(dongtuSwitch);
    } else if (moduleKey === "dashiji") {
      // 大事记模块的元素
      // 通过ScreenAdapter组件查找大事记的主要元素
      const screenAdapter = document.querySelector(".screen-adapter");
      if (screenAdapter) {
        const container = screenAdapter.querySelector(".container");
        if (container) {
          elements.push(container);
          // 获取大事记的主要子元素
          const side = container.querySelector(".side");
          const main = container.querySelector(".main");
          if (side) elements.push(side);
          if (main) elements.push(main);
        }
      }
    } else {
      // 其他模块的元素
      // const moduleItems = document.querySelectorAll(`.${moduleKey}-module`);
      // moduleItems.forEach(item => elements.push(item));
    }
    // 通用元素
    const leftWrap = document.querySelector(".left-wrap");
    const rightWrap = document.querySelector(".right-wrap");

    if (leftWrap) elements.push(leftWrap);
    if (rightWrap) elements.push(rightWrap);

    // 确保所有元素都是有效的DOM元素
    return elements.filter((el) => el && el instanceof Element);
  }

  /**
   * 根据元素在页面中的位置确定动画方向
   * @param {HTMLElement} element DOM元素
   * @returns {Object} 方向对象 {x, y}
   */
  function getElementDirection(element) {
    if (!element) return { x: 0, y: 0 };

    const rect = element.getBoundingClientRect();
    const centerX = window.innerWidth / 2;
    const centerY = window.innerHeight / 2;

    // 计算元素中心点
    const elementCenterX = rect.left + rect.width / 2;
    const elementCenterY = rect.top + rect.height / 2;

    // 确定方向
    const x = elementCenterX < centerX ? -1 : 1;
    const y = elementCenterY < centerY ? -1 : 1;

    return { x, y };
  }

  /**
   * 隐藏侧边栏按钮
   */
  function hideSidebarButtons() {
    // 获取所有侧边栏按钮
    const leftToggleBtn = document.querySelector(
      ".main-layout-sidebar-left .main-layout-sidebar-toggle"
    );
    const rightToggleBtn = document.querySelector(
      ".main-layout-sidebar-right .main-layout-sidebar-toggle"
    );
    const leftEdgeToggleBtn = document.querySelector(".main-layout-edge-toggle-left");
    const rightEdgeToggleBtn = document.querySelector(".main-layout-edge-toggle-right");

    // 使用GSAP淡出所有按钮
    const buttons = [leftToggleBtn, rightToggleBtn, leftEdgeToggleBtn, rightEdgeToggleBtn];
    buttons.forEach((btn) => {
      if (btn && isElementVisible(btn)) {
        gsap.to(btn, {
          opacity: 0,
          duration: config.duration.button,
          ease: config.ease.out,
          onComplete: () => {
            // 完全隐藏按钮
            if (btn) btn.style.visibility = "hidden";
          },
        });
      }
    });
  }

  /**
   * 显示侧边栏按钮
   */
  function showSidebarButtons() {
    // 获取左侧边栏的折叠状态
    const leftSidebar = document.querySelector(".main-layout-sidebar-left");
    const leftCollapsed = leftSidebar ? leftSidebar.classList.contains("is-collapsed") : false;

    // 获取右侧边栏的折叠状态
    const rightSidebar = document.querySelector(".main-layout-sidebar-right");
    const rightCollapsed = rightSidebar ? rightSidebar.classList.contains("is-collapsed") : false;

    // 获取所有按钮
    const leftToggleBtn = document.querySelector(
      ".main-layout-sidebar-left .main-layout-sidebar-toggle"
    );
    const rightToggleBtn = document.querySelector(
      ".main-layout-sidebar-right .main-layout-sidebar-toggle"
    );
    const leftEdgeToggleBtn = document.querySelector(".main-layout-edge-toggle-left");
    const rightEdgeToggleBtn = document.querySelector(".main-layout-edge-toggle-right");

    // 显示左侧按钮
    if (sidebarVisibility.left) {
      if (leftCollapsed) {
        // 如果左侧边栏已折叠，显示边缘按钮
        if (leftEdgeToggleBtn) {
          leftEdgeToggleBtn.style.visibility = "visible";
          gsap.fromTo(
            leftEdgeToggleBtn,
            { opacity: 0 },
            {
              opacity: 1,
              duration: config.duration.button,
              delay: config.delay.button,
              ease: config.ease.inOut,
            }
          );
        }
      } else {
        // 如果左侧边栏未折叠，显示折叠按钮
        if (leftToggleBtn) {
          leftToggleBtn.style.visibility = "visible";
          gsap.fromTo(
            leftToggleBtn,
            { opacity: 0 },
            {
              opacity: 1,
              duration: config.duration.button,
              delay: config.delay.button,
              ease: config.ease.inOut,
            }
          );
        }
      }
    }

    // 显示右侧按钮
    if (sidebarVisibility.right) {
      if (rightCollapsed) {
        // 如果右侧边栏已折叠，显示边缘按钮
        if (rightEdgeToggleBtn) {
          rightEdgeToggleBtn.style.visibility = "visible";
          gsap.fromTo(
            rightEdgeToggleBtn,
            { opacity: 0 },
            {
              opacity: 1,
              duration: config.duration.button,
              delay: config.delay.button,
              ease: config.ease.inOut,
            }
          );
        }
      } else {
        // 如果右侧边栏未折叠，显示折叠按钮
        if (rightToggleBtn) {
          rightToggleBtn.style.visibility = "visible";
          gsap.fromTo(
            rightToggleBtn,
            { opacity: 0 },
            {
              opacity: 1,
              duration: config.duration.button,
              delay: config.delay.button,
              ease: config.ease.inOut,
            }
          );
        }
      }
    }
  }

  /**
   * 模块淡出动画
   * @param {Object} timeline GSAP时间线
   * @param {String} moduleKey 模块标识
   * @param {Function} onComplete 完成回调
   * @returns {Object} 时间线对象
   */
  function fadeOutModule(timeline, moduleKey, onComplete) {
    // 获取模块元素
    const moduleElements = getModuleElements(moduleKey);

    // 如果没有找到元素，直接执行回调并返回
    if (moduleElements.length === 0) {
      console.warn(`没有找到模块 ${moduleKey} 的元素`);
      if (onComplete) {
        setTimeout(onComplete, 10);
      }
      return timeline;
    }

    // 处理左侧边栏整体淡出
    const leftSidebar = document.querySelector(".left-wrap");
    if (leftSidebar && sidebarVisibility.left && isElementVisible(leftSidebar)) {
      timeline.to(
        leftSidebar,
        {
          opacity: 0,
          x: -100,
          duration: config.duration.fadeOut,
          ease: config.ease.out,
        },
        0
      );
      // 处理左侧边栏边线隐藏
      const leftLayout = document.querySelector(".main-layout-sidebar-left");
      leftLayout && gsap.to(leftLayout, { "--after-opacity": 0 }, 0);
    }

    // 处理右侧边栏整体淡出
    const rightSidebar = document.querySelector(".right-wrap");
    if (rightSidebar && sidebarVisibility.right && isElementVisible(rightSidebar)) {
      timeline.to(
        rightSidebar,
        {
          opacity: 0,
          x: 100,
          duration: config.duration.fadeOut,
          ease: config.ease.out,
        },
        0
      );
      // 处理右侧边栏边线隐藏
      const rightLayout = document.querySelector(".main-layout-sidebar-right");
      rightLayout && gsap.to(rightLayout, { "--after-opacity": 0 }, 0);
    }

    // 处理其他元素淡出
    const otherElements = moduleElements.filter(
      (el) => !el.classList.contains("left-wrap") && !el.classList.contains("right-wrap")
    );

    otherElements.forEach((el, index) => {
      // 根据元素位置决定动画方向
      const direction = getElementDirection(el);

      timeline.to(
        el,
        {
          opacity: 0,
          y: direction.y * 30,
          duration: config.duration.fadeOut,
          ease: config.ease.out,
          delay: config.delay.base + index * config.delay.stagger,
        },
        0
      );
    });

    // 确保动画完成回调被执行
    if (onComplete) {
      timeline.to(
        {},
        {
          duration: 0.01,
          delay: config.duration.fadeOut + config.delay.base + 0.1,
          onComplete: onComplete,
        },
        0
      );
    }

    return timeline;
  }

  /**
   * 模块淡入动画
   * @param {Object} timeline GSAP时间线
   * @param {String} moduleKey 模块标识
   * @param {Function} onComplete 完成回调
   * @returns {Object} 时间线对象
   */
  function fadeInModule(timeline, moduleKey, onComplete) {
    // 获取模块元素
    const moduleElements = getModuleElements(moduleKey);

    if (moduleElements.length === 0) return timeline;

    // 在动画开始前先设置所有元素的初始状态
    // 处理左侧边栏整体淡入
    const leftSidebar = document.querySelector(".left-wrap");
    if (leftSidebar && sidebarVisibility.left) {
      // 设置初始状态
      gsap.set(leftSidebar, {
        opacity: 0,
        x: -100,
        visibility: "visible", // 确保元素可见但透明
      });

      // 处理左侧边栏边线显示
      const leftLayout = document.querySelector(".main-layout-sidebar-left");
      leftLayout && gsap.to(leftLayout, { "--after-opacity": 0.7 }, 0);
    }

    // 处理右侧边栏整体淡入
    const rightSidebar = document.querySelector(".right-wrap");
    if (rightSidebar && sidebarVisibility.right) {
      // 设置初始状态
      gsap.set(rightSidebar, {
        opacity: 0,
        x: 100,
        visibility: "visible", // 确保元素可见但透明
      });

      // 处理右侧边栏边线显示
      const rightLayout = document.querySelector(".main-layout-sidebar-right");
      rightLayout && gsap.to(rightLayout, { "--after-opacity": 0.7 }, 0);
    }

    // 处理其他元素淡入 - 先设置初始状态
    const otherElements = moduleElements.filter(
      (el) => !el.classList.contains("left-wrap") && !el.classList.contains("right-wrap")
    );

    otherElements.forEach((el) => {
      // 根据元素位置决定动画方向
      const direction = getElementDirection(el);

      // 设置初始状态
      gsap.set(el, {
        opacity: 0,
        y: direction.y * 40,
        visibility: "visible", // 确保元素可见但透明
      });
    });

    // 添加延迟以确保初始状态已应用
    timeline.to({}, { duration: 0.05 });

    // 然后开始动画
    if (leftSidebar && sidebarVisibility.left) {
      timeline.to(
        leftSidebar,
        {
          opacity: 1,
          x: 0,
          duration: config.duration.fadeIn,
          ease: config.ease.out,
        },
        config.duration.transition
      );
    }

    if (rightSidebar && sidebarVisibility.right) {
      timeline.to(
        rightSidebar,
        {
          opacity: 1,
          x: 0,
          duration: config.duration.fadeIn,
          ease: config.ease.out,
        },
        config.duration.transition
      );
    }

    otherElements.forEach((el, index) => {
      // 添加淡入动画
      timeline.to(
        el,
        {
          opacity: 1,
          y: 0,
          duration: config.duration.fadeIn,
          ease: config.ease.out,
          delay: config.delay.base + index * config.delay.stagger,
        },
        config.duration.transition
      );
    });

    // 动画完成回调
    if (onComplete) {
      timeline.call(onComplete, null, "+=0.1");
    }

    return timeline;
  }

  /**
   * 切换模块
   * @param {String} currentModuleKey 当前模块标识
   * @param {String} targetModuleKey 目标模块标识
   * @param {Function} onStateUpdate 状态更新回调
   * @param {Function} onComplete 完成回调
   * @param {Function} onError 错误回调
   */
  function switchModule(currentModuleKey, targetModuleKey, onStateUpdate, onComplete, onError) {
    try {
      // 先隐藏侧边栏按钮
      hideSidebarButtons();

      // 创建动画时间线
      const tl = createTimeline();

      // 执行当前模块淡出动画
      fadeOutModule(tl, currentModuleKey, () => {
        try {
          // 更新状态
          if (onStateUpdate) onStateUpdate();

          // 等待DOM更新
          setTimeout(() => {
            try {
              // 执行目标模块淡入动画
              const inTl = createTimeline();
              fadeInModule(inTl, targetModuleKey, () => {
                try {
                  // 动画完成后执行回调
                  if (onComplete) onComplete();

                  // 显示侧边栏按钮
                  setTimeout(() => {
                    showSidebarButtons();
                  }, 200);
                } catch (error) {
                  console.error("动画完成回调执行错误:", error);
                  if (onError) onError();
                }
              });
            } catch (error) {
              console.error("淡入动画执行错误:", error);
              if (onError) onError();
            }
          }, 50);
        } catch (error) {
          console.error("状态更新错误:", error);
          if (onError) onError();
        }
      });
    } catch (error) {
      console.error("模块切换错误:", error);
      if (onError) onError();
    }
  }

  // 返回动画系统对象
  return {
    createTimeline,
    fadeOutModule,
    fadeInModule,
    hideSidebarButtons,
    showSidebarButtons,
    switchModule,
    isElementVisible,
    getModuleElements,
    getElementDirection,
    config,
  };
}

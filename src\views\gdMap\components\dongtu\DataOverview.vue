<template>
  <div class="center-card">
    <div class="center-card-hd">
      <div class="center-card-hd-bg"></div>
      <div class="saoguang">
        <img src="@/assets/images/m-card/saoguang.svg" alt="" />
      </div>
      <div class="center-card-hd-title">数据总览</div>
      <div class="card-btn">
        <div class="btn" :class="active == 'monitor' ? 'active' : ''" @click="active = 'monitor'">
          监测统计
        </div>
        <div class="btn" :class="active == 'history' ? 'active' : ''" @click="active = 'history'">
          历史数据
        </div>
      </div>
    </div>
    <div class="center-card-bd">
      <div class="center-card-bd-bg"></div>
      <div class="center-card-bd-content">
        <div class="data-overview">
          <div class="select">
            <span>测点选择：</span>
            <a-dropdown trigger="click">
              <div class="point">
                <span class="point-text">{{ selectedPonit }}</span>
                <span class="arrow-down">
                  <icon-caret-down />
                </span>
              </div>
              <template #content>
                <a-doption
                  v-for="point in pointOptions"
                  :key="point"
                  @click="selectedPonit = point"
                >
                  {{ point }}
                </a-doption>
              </template>
            </a-dropdown>
          </div>
          <div class="context-table">
            <ScreenTable :columns="tableColumns">
              <div ref="containerRef" class="data-table">
                <div class="table-body">
                  <div class="table-row" v-for="(item, index) in dataList" :key="index">
                    <div class="col name" :title="item.name">{{ item.name }}</div>
                    <div class="col type">
                      <span :class="['dot', setBg(item.type)]"></span>{{ item.type }}
                    </div>
                    <div class="col value" :title="item.value">{{ item.value }}</div>
                    <div class="col unit" :title="item.unit">{{ item.unit }}</div>
                    <div class="col time">{{ item.time }}</div>
                    <div class="col opt">报警规则</div>
                  </div>
                </div>
              </div>
            </ScreenTable>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { IconCaretDown } from '@arco-design/web-vue/es/icon';
import ScreenTable from '../ScreenTable.vue';
import { useMarquee } from '@/hooks/useMarquee';
import { getDataOverview, getPointSelect } from '@/views/gdMap/services/dongtu.mock.js';

const tableColumns = [
  { title: '测点名称', width: '18%' },
  { title: '报警状态', width: '18%' },
  { title: '计算值', width: '18%' },
  { title: '单位', width: '18%' },
  { title: '时间', width: '18%' },
  { title: '操作', width: '10%' },
];
const active = ref('monitor');
const dataList = ref();

const selectedPonit = ref('');
const pointOptions = ref([]);

const { containerRef, resetScroll } = useMarquee({
  speed: 10, // 滚动速度
  delay: 2000, // 滚动到底部后停顿时间
  step: 1, // 每次滚动的像素
});

// 当数据变化时，重置滚动
watch(
  dataList,
  () => {
    setTimeout(resetScroll, 300); // 延迟一点时间确保DOM已更新
  },
  { deep: true }
);

onMounted(async () => {
  const defaultData = await getDataOverview();
  const pointList = await getPointSelect();
  pointOptions.value = pointList.list;
  selectedPonit.value = pointList.list?.[0] || '';
  dataList.value = defaultData.list;
});

const setBg = type => {
  if (type === '正常') {
    return 'green';
  } else if (type === '异常') {
    return 'red';
  } else {
    return 'orange';
  }
};
</script>

<style lang="scss" scoped>
.center-card {
  bottom: 164px;
  position: absolute;
  left: 423px;
  right: 423px;
  height: 328px;
  z-index: 3;
  .card-btn {
    box-sizing: border-box;
    height: 29px;
    line-height: 29px;
    position: absolute;
    right: 12px;
    font-size: 12px;
    top: 5px;
    color: #fff;
    display: flex;
    box-shadow: 0px 0px 4px 0px rgba(70, 188, 255, 0.37);
    border: 1px solid rgba(70, 188, 255, 0.37);
    background: rgba(43, 87, 183, 0.6);
    pointer-events: all;
    .btn {
      cursor: pointer;
      padding: 0 10px;
      &.active {
        border: 1px solid rgba(73, 128, 188, 0.8);
        background: #1e53ab;
      }
    }
  }
  &-hd {
    position: absolute;
    height: 39px;
    width: 100%;
    z-index: 2;
    &-bg {
      position: absolute;
      height: 39px;
      width: 100%;
      background-size: 100% 100%;
      background-image: url('~@/assets/images/dongtu/card-header-center.png');
    }
    &-title {
      position: absolute;
      left: 33px;
      color: #fff;
      font-size: 20px;
      letter-spacing: 1.6px;
      height: 39px;
      line-height: 39px;
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-weight: 400;
      text-shadow: 0px 0px 9px #158eff;
    }
    .saoguang {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 39px;
      overflow: hidden;
      pointer-events: none;
      img {
        width: 89px;
        height: 39px;
        animation: saoguangMove 6s linear infinite;
      }
    }
  }
  &-bd {
    position: absolute;
    left: 0;
    top: 43px;
    width: 100%;
    height: 285px;
    z-index: 1;
    &-bg {
      position: absolute;
      width: 100%;
      height: 285px;
      background-color: rgba(10, 35, 75, 0.6);
      // background-image: url('~@/assets/images/dongtu/card-bg-center.png');
      // background-repeat: no-repeat;
      // background-size: 100% 100%;
    }
    &-content {
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      pointer-events: all;
      overflow: hidden;
    }
  }
}

.data-overview {
  color: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
  .select {
    display: flex;
    align-items: center;
    padding: 12px 18px;
    font-size: 14px;
  }
  .point {
    height: 30px;
    background: rgba(13, 58, 102, 0.6);
    line-height: 30px;
    padding: 0 12px;
    display: flex;
    align-items: center;
    cursor: pointer;
  }
  .arrow-down {
    color: #ffffff;
    width: 16px;
    height: 16px;
    margin-left: 6px;
    font-size: 16px;
    display: flex;
    align-items: center;
  }
  .context-table {
    flex: 1;
    overflow: hidden;
  }
  .data-table {
    height: 100%;
    overflow-y: hidden;
    .table-body {
      .table-row {
        display: flex;
        height: 28px;
        align-items: center;

        // 单行背景色
        &:nth-child(odd) {
          background-color: #091e3f;
        }

        // 双行背景色
        &:nth-child(even) {
          background-color: #142b50;
        }

        &:last-child {
          border-bottom: none;
        }

        .col {
          padding: 0 6px;
          font-size: 12px;
          color: #fff;
          font-family: Source Han Sans CN, Source Han Sans CN;
          box-sizing: border-box;
          width: 18%;
          &.name {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          &.opt {
            width: 10%;
            color: #48d6ff;
            cursor: pointer;
          }
        }
      }
    }
  }
  .dot {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 1px;
    margin-right: 6px;
    &.green {
      background: #06d239;
    }
    &.red {
      background: #ff4042;
    }
    &.orange {
      background: #ff9e49;
    }
  }
}
</style>

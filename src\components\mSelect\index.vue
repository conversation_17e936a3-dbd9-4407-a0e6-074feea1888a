<template>
  <div class="m-select">
    <a-dropdown trigger="click">
      <div class="filter-btn">
        <span class="filter-btn-text">{{ formate(selectedLable) }}</span>
        <span class="arrow-down">
          <icon-caret-down />
        </span>
      </div>
      <template #content>
        <a-doption v-for="item in options" :key="item" @click="onClick(item)">
          {{ formate(item) }}
        </a-doption>
      </template>
    </a-dropdown>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { IconCaretDown } from '@arco-design/web-vue/es/icon';

const props = defineProps({
  options: {
    type: Array,
    default() {
      return [];
    },
  },
  selected: {
    type: String,
    default: '',
  },
  formate: {
    type: Function,
    default: val => {
      return val;
    },
  },
});
const selectedLable = ref(props.formate(props.selected));

watch(
  () => props.selected,
  val => {
    selectedLable.value = props.formate(val);
  }
);

const onClick = val => {
  selectedLable.value = val;
};
</script>

<style lang="scss">
.m-select {
  position: absolute;
  right: 9px;
  top: 7px;
  .filter-btn {
    padding: 0 5px;
    display: flex;
    height: 26px;
    line-height: 26px;
    background: #0b3567;
    box-shadow: inset 0px 0px 4px 0px #0e9aff;
    border-radius: 2px;
    border: 1px solid #1299ff;
    box-sizing: border-box;
    cursor: pointer;
    color: #ffffff;
    &-text {
      font-size: 12px;
    }
    .arrow-down {
      width: 12px;
      height: 26px;
      font-size: 12px;
      margin-left: 4px;
    }
  }
}
</style>

import mapConfig from '@/config/engine/maplibre/map.config.js';

// 桩号图层
// 图层缩放级别常量
const LAYER_ZOOM_LEVELS = {
  POINT: {
    MIN: 11,
    MAX: mapConfig.maxzoom,
  },
  LABEL: {
    MIN: 11,
    MAX: mapConfig.maxzoom,
  },
};

export class PileLayer {
  constructor(map) {
    this.map = map;
    this.sourceId = 'pile-markers';
    this.pointLayerId = `${this.sourceId}-points`;
    this.labelLayerId = `${this.sourceId}-labels`;
    this.commandCenterLayerId = `${this.sourceId}-command-centers`;
    this.subCommandCenterLayerId = `${this.sourceId}-sub-command-centers`;

    // 图标加载状态
    this.iconsLoaded = false;
  }

  /**
   * 处理GeoJSON数据，去除重复坐标的点
   * @param {Object} geoJsonData 原始GeoJSON数据
   * @returns {Object} 处理后的GeoJSON数据
   */
  processGeoJsonData(geoJsonData) {
    if (!geoJsonData || !geoJsonData.features || !Array.isArray(geoJsonData.features)) {
      return geoJsonData;
    }

    const uniqueCoordinates = new Set();
    const filteredFeatures = [];

    for (const feature of geoJsonData.features) {
      if (feature.geometry && feature.geometry.coordinates) {
        // 将坐标转换为字符串用于比较
        const coordStr = JSON.stringify(feature.geometry.coordinates);

        // 如果坐标已存在，跳过此特征
        if (uniqueCoordinates.has(coordStr)) {
          continue;
        }

        // 添加到已处理集合
        uniqueCoordinates.add(coordStr);
        filteredFeatures.push(feature);
      } else {
        // 没有坐标的特征仍然保留
        filteredFeatures.push(feature);
      }
    }

    return {
      ...geoJsonData,
      features: filteredFeatures,
    };
  }

  /**
   * 使用默认数据URL初始化桩号图层
   * @returns {Promise<this>} 当前实例
   */
  async init() {
    const url = '/config/engine/map/json/pile-mark.json';
    try {
      const response = await fetch(url);
      const data = await response.json();
      return this.loadData(data);
    } catch (error) {
      console.error('加载桩号数据失败:', error);
      throw error;
    }
  }

  /**
   * 初始化桩号图层
   * @param {Object} geoJsonData GeoJSON数据
   */
  async loadData(geoJsonData) {
    // 处理重复坐标的点，只保留第一个出现的点
    const processedData = this.processGeoJsonData(geoJsonData);

    // 添加数据源
    this.map.addSource(this.sourceId, {
      type: 'geojson',
      data: processedData,
    });

    // 添加桩号点图层
    this.map.addLayer({
      id: this.pointLayerId,
      type: 'symbol',
      source: this.sourceId,
      filter: ['!', ['has', 'point_type']], // 只显示没有point_type的点（桩号点）
      layout: {
        'icon-image': 'pile-point',
        'icon-size': 0.8,
        'icon-allow-overlap': true,
        'icon-anchor': 'center',
      },
      minzoom: LAYER_ZOOM_LEVELS.POINT.MIN,
      maxzoom: LAYER_ZOOM_LEVELS.POINT.MAX,
    });

    // 添加桩号文本图层
    this.map.addLayer({
      id: this.labelLayerId,
      type: 'symbol',
      source: this.sourceId,
      filter: ['!', ['has', 'point_type']], // 只显示没有point_type的点（桩号点）

      layout: {
        'text-field': ['get', 'stake_number'], // 显示文本
        'text-size': 18,
        'text-offset': [1, 0], // 文本偏移（右侧显示）
        'text-anchor': 'left',
        'text-allow-overlap': true, // 允许文本重叠
      },
      paint: {
        'text-color': '#FFFFFF',
      },
      minzoom: LAYER_ZOOM_LEVELS.LABEL.MIN,
      maxzoom: LAYER_ZOOM_LEVELS.LABEL.MAX,
    });

    return this;
  }

  /**
   * 销毁图层
   */
  destroy() {
    // 移除所有图层
    const layers = [
      this.labelLayerId,
      this.pointLayerId,
      this.commandCenterLayerId,
      this.subCommandCenterLayerId,
    ];

    for (const layerId of layers) {
      if (this.map.getLayer(layerId)) {
        this.map.removeLayer(layerId);
      }
    }

    // 移除图标
    if (this.iconsLoaded) {
      try {
        this.map.removeImage('command-center-icon');
        this.map.removeImage('sub-command-center-icon');
      } catch (e) {
        console.warn('移除图标失败:', e);
      }
    }

    // 移除数据源
    if (this.map.getSource(this.sourceId)) {
      this.map.removeSource(this.sourceId);
    }
  }

  /**
   * 清空所有桩号标记
   */
  clear() {
    const source = this.map.getSource(this.sourceId);
    if (source) {
      source.setData({
        type: 'FeatureCollection',
        features: [],
      });
    }
  }
}

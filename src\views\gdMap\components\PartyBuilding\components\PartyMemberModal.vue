<template>
	<Modal ref="ModalRef" title="党组织人员构成-明细查询" size="large">
		<DepartmentRadioGroup v-model="activeRadio" @change="getStaffData" />
		<a-table
			class="table"
			:columns="columns"
			:data="tableData"
			:scroll="{ x: '100%', y: '100%' }"
			:scrollbar="false"
			:pagination="false"
		/>
	</Modal>
</template>

<script setup>
import request from "@/utils/request";
import Modal from "./Modal.vue";
import DepartmentRadioGroup from "./DepartmentRadioGroup.vue";

const columns = [
	{
		title: "党组织名称",
		render: () => "党组织名称",
		width: 110,
	},
	{
		title: "全部党员",
		render: ({ record }) => {
			const { zhengshiMember = 0, yubeiMember = 0 } = record;
			const total = zhengshiMember + yubeiMember;
			return new Intl.NumberFormat().format(total);
		},
	},
	{
		title: "正式党员",
		dataIndex: "zhengshiMember",
	},
	{
		title: "预备党员",
		dataIndex: "yubeiMember",
	},
	{
		title: "少数名族党员",
		dataIndex: "shaoshuMember",
		width: 120,
	},
	{
		title: "女党员",
		dataIndex: "nvMember",
		width: 80,
	},
	{
		title: "党务干部",
		dataIndex: "dangwuMember",
	},
	{
		title: "发展对象",
		dataIndex: "fazhanMember",
	},
	{
		title: "积极分子",
		dataIndex: "jijiMember",
	},
	{
		title: "申请人",
		dataIndex: "shenqingMember",
		width: 80,
	},
	{
		title: "在岗职工数",
		dataIndex: "zaigangMember",
		width: 110,
	},
];
const activeRadio = ref("全部");
const tableData = ref([]);
const ModalRef = ref(null);

const onOpen = () => {
	ModalRef.value?.open();
	getStaffData();
};

const getStaffData = () => {
	request.get("/api/screen/dangjian/stat/staff", { department: activeRadio.value }).then((res) => {
		if (res && res.code == 200) {
			tableData.value = [res.data];
		}
	});
};

defineExpose({
	open: onOpen,
});
</script>

<style lang="scss" scoped>
.table {
	margin-top: 25px;

	:deep(.arco-table-td-content) {
		height: 2em;
		display: flex;
		align-items: center;
	}

	:deep(.arco-table-cell) {
		padding-left: 4px;
		padding-right: 4px;
	}
}
</style>

<svg width="23" height="23" viewBox="0 0 23 23" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#232;&#167;&#130;&#230;&#153;&#175;&#229;&#143;&#176;">
<g id="Ellipse 15" filter="url(#filter0_d_342_3741)">
<circle cx="11.469" cy="11.5518" r="8" fill="url(#paint0_radial_342_3741)" fill-opacity="0.7"/>
<circle cx="11.469" cy="11.5518" r="7.55492" stroke="url(#paint1_linear_342_3741)" stroke-width="0.890162"/>
</g>
<circle id="Ellipse 16" cx="11.5427" cy="11.6252" r="7.07681" fill="url(#paint2_radial_342_3741)" stroke="url(#paint3_linear_342_3741)" stroke-width="0.200286"/>
<g id="&#229;&#176;&#143;&#230;&#161;&#165;" filter="url(#filter1_d_342_3741)">
<g id="Frame" clip-path="url(#clip0_342_3741)">
<path id="Vector" d="M13.5812 8.81622C14.4507 9.32885 15.473 9.51882 16.4686 9.35278C16.4304 9.80655 16.2488 10.2364 15.9502 10.5802C15.6516 10.924 15.2514 11.1639 14.8074 11.2653V14.6573L15.7613 14.6578V15.6117H7.17641V14.6578H8.13029V11.2653C7.68632 11.1639 7.28609 10.9239 6.9874 10.5802C6.68872 10.2364 6.50705 9.80656 6.46863 9.35278C7.46434 9.51919 8.48686 9.32948 9.35661 8.81698C10.2264 8.30447 10.8878 7.50193 11.2247 6.55028H11.7135V6.5498C12.0504 7.5013 12.7117 8.30372 13.5812 8.81622ZM9.08417 11.3187V14.6573H13.8536V11.3187H9.08417Z" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_342_3741" x="0.798508" y="0.881272" width="21.341" height="21.341" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.33524"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.388235 0 0 0 0 0.640523 0 0 0 0 0.807843 0 0 0 0.51 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_342_3741"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3741" result="shape"/>
</filter>
<filter id="filter1_d_342_3741" x="6.40442" y="5.9668" width="10" height="10.4451" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.445081"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.150327 0 0 0 0 0.247059 0 0 0 0 0.145098 0 0 0 0.75 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_342_3741"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3741" result="shape"/>
</filter>
<radialGradient id="paint0_radial_342_3741" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(11.469 11.5518) scale(8)">
<stop stop-color="#D3D3D3"/>
<stop offset="0.03" stop-color="#D3D3D3" stop-opacity="0.9216"/>
<stop offset="0.13" stop-color="#D3D3D3" stop-opacity="0.7056"/>
<stop offset="0.23" stop-color="#D3D3D3" stop-opacity="0.5329"/>
<stop offset="0.35" stop-color="#D3D3D3" stop-opacity="0.4225"/>
<stop offset="0.47" stop-color="#D3D3D3" stop-opacity="0.3364"/>
<stop offset="0.6" stop-color="#D3D3D3" stop-opacity="0.2916"/>
<stop offset="0.76" stop-color="#D3D3D3" stop-opacity="0.2601"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.5"/>
</radialGradient>
<linearGradient id="paint1_linear_342_3741" x1="11.469" y1="19.5518" x2="11.469" y2="3.55176" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<radialGradient id="paint2_radial_342_3741" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(11.5427 11.6252) scale(7.17695)">
<stop stop-color="#D3D3D3"/>
<stop offset="1" stop-color="#0B103B" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint3_linear_342_3741" x1="11.5427" y1="18.8022" x2="11.5427" y2="4.44824" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<clipPath id="clip0_342_3741">
<rect width="10" height="10" fill="white" transform="translate(6.40442 5.9668)"/>
</clipPath>
</defs>
</svg>

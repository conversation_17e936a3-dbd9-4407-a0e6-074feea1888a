{"areas": [{"areaName": "全览", "key": "all", "totalTarget": 37133, "targetProgress": 0.5, "actualProgress": 0.48, "items": [{"type": "生态降碳", "current": 0, "target": 2270, "progress": 0}, {"type": "减污降碳", "current": 30, "target": 14700, "progress": 0.2}, {"type": "循环利用降碳", "current": 145, "target": 12950, "progress": 1.1}, {"type": "气候韧性降碳", "current": 0, "target": 2426, "progress": 0}, {"type": "源头降碳", "current": 0, "target": 24, "progress": 0}, {"type": "过程降碳", "current": 5, "target": 165, "progress": 3}, {"type": "末端降碳", "current": 0, "target": 4598, "progress": 0}]}, {"areaName": "格贡一", "key": "gegong1", "totalTarget": 4194, "targetProgress": 0.6, "actualProgress": 0.57, "items": [{"type": "生态降碳", "current": 0, "target": 2270, "progress": 0}, {"type": "减污降碳", "current": 30, "target": 14700, "progress": 0.2}, {"type": "循环利用降碳", "current": 145, "target": 12950, "progress": 1.1}, {"type": "气候韧性降碳", "current": 0, "target": 2426, "progress": 0}, {"type": "源头降碳", "current": 0, "target": 24, "progress": 0}, {"type": "过程降碳", "current": 5, "target": 165, "progress": 3}, {"type": "末端降碳", "current": 0, "target": 4598, "progress": 0}]}, {"areaName": "格贡二", "key": "gegong2", "totalTarget": 6669, "targetProgress": 0.2, "actualProgress": 0.3, "items": [{"type": "生态降碳", "current": 0, "target": 250, "progress": 0}, {"type": "减污降碳", "current": 4, "target": 2817, "progress": 0.14}, {"type": "循环利用降碳", "current": 15, "target": 2600, "progress": 0.58}, {"type": "气候韧性降碳", "current": 0, "target": 190, "progress": 0}, {"type": "源头降碳", "current": 0, "target": 3, "progress": 0}, {"type": "过程降碳", "current": 1, "target": 29, "progress": 3.45}, {"type": "末端降碳", "current": 0, "target": 780, "progress": 0}]}, {"areaName": "格贡三", "key": "gegong3", "totalTarget": 2347, "targetProgress": 1.5, "actualProgress": 1.96, "items": [{"type": "生态降碳", "current": 0, "target": 500, "progress": 0}, {"type": "减污降碳", "current": 8, "target": 203, "progress": 3.94}, {"type": "循环利用降碳", "current": 36, "target": 304, "progress": 11.84}, {"type": "气候韧性降碳", "current": 0, "target": 800, "progress": 0}, {"type": "源头降碳", "current": 0, "target": 6, "progress": 0}, {"type": "过程降碳", "current": 2, "target": 32, "progress": 6.25}, {"type": "末端降碳", "current": 0, "target": 502, "progress": 0}]}, {"areaName": "格贡四", "key": "gegong4", "totalTarget": 12772, "targetProgress": 2, "actualProgress": 0.25, "items": [{"type": "生态降碳", "current": 0, "target": 478, "progress": 0}, {"type": "减污降碳", "current": 6, "target": 6460, "progress": 0.09}, {"type": "循环利用降碳", "current": 25, "target": 4700, "progress": 0.53}, {"type": "气候韧性降碳", "current": 0, "target": 423, "progress": 0}, {"type": "源头降碳", "current": 0, "target": 8, "progress": 0}, {"type": "过程降碳", "current": 1, "target": 49, "progress": 2.04}, {"type": "末端降碳", "current": 0, "target": 654, "progress": 0}]}, {"areaName": "贡那一", "key": "gongna1", "totalTarget": 7609, "targetProgress": 1.2, "actualProgress": 0.2, "items": [{"type": "生态降碳", "current": 0, "target": 587, "progress": 0}, {"type": "减污降碳", "current": 4, "target": 2650, "progress": 0.15}, {"type": "循环利用降碳", "current": 11, "target": 2800, "progress": 0.39}, {"type": "气候韧性降碳", "current": 0, "target": 586, "progress": 0}, {"type": "源头降碳", "current": 0, "target": 2, "progress": 0}, {"type": "过程降碳", "current": 0, "target": 21, "progress": 0}, {"type": "末端降碳", "current": 0, "target": 963, "progress": 0}]}, {"areaName": "贡那二", "key": "gongna2", "totalTarget": 3545, "targetProgress": 2, "actualProgress": 1.21, "items": [{"type": "生态降碳", "current": 0, "target": 303, "progress": 0}, {"type": "减污降碳", "current": 6, "target": 800, "progress": 0.75}, {"type": "循环利用降碳", "current": 36, "target": 1046, "progress": 3.44}, {"type": "气候韧性降碳", "current": 0, "target": 327, "progress": 0}, {"type": "源头降碳", "current": 0, "target": 3, "progress": 0}, {"type": "过程降碳", "current": 1, "target": 17, "progress": 5.88}, {"type": "末端降碳", "current": 0, "target": 1049, "progress": 0}]}]}
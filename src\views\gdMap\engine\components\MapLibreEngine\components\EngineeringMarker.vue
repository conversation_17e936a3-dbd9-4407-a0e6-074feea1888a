<template>
	<div ref="markerRef" style="width: 100%; height: 100%">
		<div class="common-marker" :class="markerClass">
			<div class="icon" @click.stop="onClickIcon">
				<component :is="icon" />
			</div>
		</div>
		<MarkerPanel v-if="isExpanded" :title="title" anchor="right-bottom">
			<template #content>
				<div class="data-list">
					<template v-if="props.type === 'culvert'">
						<div class="data-item">
							<span class="data-key">涵洞总数:</span>
							<span class="data-value"> {{ props.properties["culvertNumber"] }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">建设中涵洞数:</span>
							<span class="data-value"> {{ props.properties["constructionNumber"] }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">建设完成涵洞数:</span>
							<span class="data-value"> {{ props.properties["completeNumber"] }} </span>
						</div>
					</template>
					<template v-else-if="props.type === 'bridge'">
						<div class="data-item">
							<span class="data-key">桥梁总数:</span>
							<span class="data-value"> {{ props.properties["bridgeNumber"] }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">建设中桥梁数:</span>
							<span class="data-value"> {{ props.properties["constructionNumber"] }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">建设完成桥梁数:</span>
							<span class="data-value"> {{ props.properties["completeNumber"] }} </span>
						</div>
					</template>
					<template v-else-if="props.type === 'culvert-actual'">
						<div class="data-item">
							<span class="data-key">涵洞名称:</span>
							<span class="data-value"> {{ culvertData.culvertName }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">涵洞中心桩号:</span>
							<span class="data-value"> {{ culvertData.centerStakeNumber }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">结构类型:</span>
							<span class="data-value"> {{ culvertData.structureType }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">孔数-孔径:</span>
							<span class="data-value"> {{ culvertData.holeAperture }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">涵长:</span>
							<span class="data-value"> {{ culvertData.culvertLength }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">填土高度:</span>
							<span class="data-value"> {{ culvertData.fillHeight }} m</span>
						</div>
						<div class="data-item">
							<span class="data-key">功能:</span>
							<span class="data-value"> {{ culvertData.functionDesc }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">水流方向:</span>
							<span class="data-value"> {{ culvertData.waterFlowDirection }} </span>
						</div>
					</template>
					<template v-else-if="props.type === 'bridge-actual'">
						<div class="data-item">
							<span class="data-key">桥梁名称:</span>
							<span class="data-value"> {{ bridgeData.bridgeName }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">中心桩号:</span>
							<span class="data-value"> {{ bridgeData.centerStakeNumber }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">孔径*跨径:</span>
							<span class="data-value"> {{ bridgeData.apertureSpan }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">桥梁全长:</span>
							<span class="data-value"> {{ bridgeData.totalLength }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">桥梁宽度:</span>
							<span class="data-value"> {{ bridgeData.width }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">最大桥高:</span>
							<span class="data-value"> {{ bridgeData.maxHeight }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">设计流量:</span>
							<span class="data-value"> {{ bridgeData.designFlow }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">设计水位:</span>
							<span class="data-value"> {{ bridgeData.designWaterLevel }} </span>
						</div>
						<div class="data-item">
							<span class="data-key">冲刷深度:</span>
							<span class="data-value"> {{ bridgeData.scourDepth }} </span>
						</div>
					</template>
				</div>
			</template>
		</MarkerPanel>
	</div>
</template>

<script setup>
import { Dict, departmentDict } from "@/utils/dict";
import request from "@/utils/request";
import emitter from "@/utils/emitter";
import { v4 } from "uuid";
import Culvert from "@/assets/modules/oneMap/icon/culvert.svg?component";
import Bridge from "@/assets/modules/oneMap/icon/bridge.svg?component";
import MarkerPanel from "./MarkerPanel.vue";

const props = defineProps({
	properties: {
		type: Object,
		default: {},
	},
	type: String,
});

const typeList = new Dict([
	{
		value: "culvert",
		label: "涵洞",
		class: "is-cyan",
		icon: Culvert,
	},
	{
		value: "bridge",
		label: "桥梁",
		icon: Bridge,
	},
	{
		value: "culvert-actual",
		label: "涵洞基本信息",
		class: "is-cyan",
		icon: Culvert,
	},
	{
		value: "bridge-actual",
		label: "桥梁基本信息",
		icon: Bridge,
	},
]);

const markerClass = computed(() => {
	const item = typeList.getItem(props.type);
	return item?.class || "is-blue";
});

const icon = computed(() => {
	const item = typeList.getItem(props.type);
	if (item) {
		return item.icon;
	}
	return null;
});

const title = computed(() => {
	return `【${typeList.getLabel(props.type)}】 ${departmentDict.getLabel(
		props.properties["departmentId"]
	)}`;
});

let inited = false;
const markerId = ref(v4());
const markerRef = ref(null);
const isExpanded = ref(false);
const culvertData = ref({});
const bridgeData = ref({});

onMounted(() => {
	document.addEventListener("click", handleClickOutside);
	emitter.$on("marker-expand", handleOtherMarkerExpand);
});

onUnmounted(() => {
	document.removeEventListener("click", handleClickOutside);
	emitter.$off("marker-expand", handleOtherMarkerExpand);
});

watch(
	() => isExpanded.value,
	async (val) => {
		if (val && !inited) {
			await getData();
			inited = true;
		}

		const parent = markerRef.value.parentElement;
		parent.style["z-index"] = val ? 1 : 0;
	}
);

const getData = async () => {
	if (props.type === "culvert-actual") {
		await getCulvertDetail();
	}
	if (props.type === "bridge-actual") {
		await getBridgeDetail();
	}
};

const onClickIcon = () => {
	if (!isExpanded.value) {
		emitter.$emit("marker-expand", markerId.value);
	}
	isExpanded.value = !isExpanded.value;
};

const getCulvertDetail = async () => {
	await request
		.get("/api/screen/engineering/center/culvert/detail", {
			culvertId: props.properties["culvertId"],
		})
		.then((res) => {
			if (res.code == 200) {
				culvertData.value = res.data;
			}
		});
};

const getBridgeDetail = async () => {
	await request
		.get("/api/screen/engineering/center/bridge/detail", {
			bridgeId: props.properties["bridgeId"],
		})
		.then((res) => {
			if (res.code == 200) {
				bridgeData.value = res.data;
			}
		});
};

const handleClickOutside = (event) => {
	if (isExpanded.value && markerRef.value && !markerRef.value.contains(event.target)) {
		isExpanded.value = false;
	}
};

const handleOtherMarkerExpand = (id) => {
	if (id !== markerId.value && isExpanded.value) {
		isExpanded.value = false;
	}
};
</script>

<style lang="scss" scoped>
@import "./common.scss";

.data-key {
	width: 100px;
}
</style>

<svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#232;&#135;&#170;&#230;&#156;&#137;&#232;&#189;&#166;&#232;&#190;&#134;">
<g id="Ellipse 15" filter="url(#filter0_d_342_3972)">
<circle cx="10.9956" cy="10.8789" r="8" fill="url(#paint0_radial_342_3972)" fill-opacity="0.7"/>
<circle cx="10.9956" cy="10.8789" r="7.55492" stroke="url(#paint1_linear_342_3972)" stroke-width="0.890162"/>
</g>
<circle id="Ellipse 16" cx="10.9956" cy="10.8787" r="7.01097" fill="url(#paint2_radial_342_3972)" stroke="url(#paint3_linear_342_3972)" stroke-width="0.200286"/>
<g id="Group 1321315401" filter="url(#filter1_d_342_3972)">
<g id="Frame" clip-path="url(#clip0_342_3972)">
<path id="Vector" d="M7.88781 12.0545C8.10289 12.0545 8.30915 12.1399 8.46123 12.292C8.61331 12.4441 8.69874 12.6504 8.69874 12.8654C8.69874 13.0805 8.61331 13.2868 8.46123 13.4388C8.30915 13.5909 8.10289 13.6764 7.88781 13.6764C7.67274 13.6764 7.46648 13.5909 7.3144 13.4388C7.16232 13.2868 7.07689 13.0805 7.07689 12.8654C7.07689 12.6504 7.16232 12.4441 7.3144 12.292C7.46648 12.1399 7.67274 12.0545 7.88781 12.0545ZM11.7803 12.0545C11.9953 12.0545 12.2016 12.1399 12.3537 12.292C12.5058 12.4441 12.5912 12.6504 12.5912 12.8654C12.5912 13.0805 12.5058 13.2868 12.3537 13.4388C12.2016 13.5909 11.9953 13.6764 11.7803 13.6764C11.5652 13.6764 11.3589 13.5909 11.2069 13.4388C11.0548 13.2868 10.9693 13.0805 10.9693 12.8654C10.9693 12.6504 11.0548 12.4441 11.2069 12.292C11.3589 12.1399 11.5652 12.0545 11.7803 12.0545ZM13.6183 12.0545C13.8333 12.0545 14.0396 12.1399 14.1917 12.292C14.3438 12.4441 14.4292 12.6504 14.4292 12.8654C14.4292 13.0805 14.3438 13.2868 14.1917 13.4388C14.0396 13.5909 13.8333 13.6764 13.6183 13.6764C13.4032 13.6764 13.1969 13.5909 13.0449 13.4388C12.8928 13.2868 12.8073 13.0805 12.8073 12.8654C12.8073 12.6504 12.8928 12.4441 13.0449 12.292C13.1969 12.1399 13.4032 12.0545 13.6183 12.0545ZM8.70774 7.9459C8.7371 7.94612 8.76536 7.95715 8.7871 7.9769C8.80884 7.99664 8.82253 8.02372 8.82555 8.05293L8.82645 8.06612V11.4909H15.1754C15.2048 11.4912 15.233 11.5022 15.2548 11.5219C15.2766 11.5417 15.2903 11.5687 15.2935 11.5979L15.2941 11.6108V12.9077C15.2941 12.9371 15.2834 12.9655 15.2639 12.9875C15.2444 13.0095 15.2175 13.0236 15.1883 13.027L15.1754 13.0276H14.6444C14.654 12.9703 14.6588 12.9122 14.6588 12.854C14.6588 12.2814 14.1993 11.8168 13.6327 11.8168C13.4381 11.8172 13.2477 11.873 13.0837 11.9776C12.9196 12.0823 12.7887 12.2314 12.7063 12.4077C12.6239 12.2315 12.4931 12.0824 12.3291 11.9777C12.1651 11.8731 11.9748 11.8173 11.7803 11.8168C11.2137 11.8168 10.7544 12.2814 10.7544 12.854C10.7544 12.8984 10.7574 12.9422 10.7625 12.985L10.7685 13.0276H8.92688C8.93645 12.9703 8.94126 12.9122 8.94127 12.854C8.94127 12.2814 8.482 11.8168 7.9154 11.8168C7.3488 11.8168 6.88952 12.2814 6.88952 12.854C6.88952 12.8984 6.89252 12.9422 6.89761 12.985L6.90361 13.0276H6.11457C6.08519 13.0273 6.05693 13.0163 6.03515 12.9966C6.01338 12.9768 5.99961 12.9498 5.99645 12.9206L5.99585 12.9077V9.61183C5.99585 8.70916 6.53277 7.96809 6.93299 7.9465L6.95068 7.9459H8.70744H8.70774ZM15.2941 9.67568V11.1551C15.2941 11.2637 15.2692 11.2945 15.1727 11.2975H12.1358H12.1625L12.2045 11.2951C12.4296 11.2757 12.6293 11.1647 12.8358 11.0844C13.3682 10.8757 13.9001 10.6647 14.4304 10.4506C14.476 10.4319 14.518 10.4052 14.5542 10.3718C14.7982 10.1455 15.0399 9.91611 15.2941 9.67598V9.67568ZM9.35708 9.35161C9.39005 9.35071 9.40924 9.3669 9.42003 9.40677C9.55434 9.90112 9.90869 10.8892 9.90869 10.8892C9.90869 10.8892 9.96265 11.085 9.98903 11.1833C10.0088 11.2589 9.98873 11.2966 9.92608 11.2972C9.68625 11.2978 9.44671 11.2978 9.20718 11.2972C9.14573 11.2972 9.13224 11.279 9.13164 11.1992L9.13134 10.7609V9.46433C9.13134 9.3651 9.14213 9.35221 9.22038 9.35161H9.25575C9.29292 9.35161 9.29772 9.35221 9.35708 9.35131V9.35161ZM15.3801 7.17575C15.5765 7.72975 15.7746 8.28346 15.974 8.83657C15.998 8.89803 16.0109 8.94839 15.9659 8.98107L15.9554 8.98767L15.5399 9.19302L15.3394 9.23979C15.3394 9.23979 14.6241 9.91072 14.2643 10.2435C14.2328 10.2723 14.1897 10.2896 14.1498 10.3052C13.376 10.6113 12.6029 10.9186 11.8273 11.2184C11.7476 11.2493 11.652 11.2418 11.5635 11.2454C11.166 11.2628 10.7682 11.2778 10.371 11.2945C10.3296 11.2963 10.3089 11.2837 10.2948 11.2418C10.0739 10.5906 9.85143 9.94009 9.62779 9.29015C9.6134 9.24938 9.6149 9.223 9.64517 9.19542L9.65477 9.18703L10.3443 8.60334C10.5835 8.3695 10.8641 8.21721 11.1759 8.10149C11.7977 7.86706 12.414 7.61703 13.0295 7.36701C13.1668 7.31185 13.3032 7.31755 13.442 7.31845C13.7412 7.32084 14.0404 7.32744 14.339 7.33194C14.4175 7.33313 14.4961 7.33313 14.5746 7.33463L14.6921 7.33763L14.8675 7.2417C14.9838 7.19703 15.0989 7.14817 15.2149 7.1017C15.313 7.06213 15.3445 7.07562 15.3801 7.17575ZM8.49459 8.28946H7.0646C6.82177 8.28946 6.36309 8.90103 6.3481 9.6565L6.3475 9.69517V10.4554L8.32251 10.0498C8.36895 10.0376 8.41041 10.0111 8.44114 9.97423C8.47187 9.93732 8.49035 9.89175 8.49399 9.84386L8.49459 9.82558V8.28916V8.28946ZM9.61789 7.83798C9.74481 7.84113 9.86546 7.89376 9.95411 7.98463C10.0428 8.07551 10.0924 8.19743 10.0924 8.32439C10.0924 8.45134 10.0428 8.57326 9.95411 8.66414C9.86546 8.75501 9.74481 8.80764 9.61789 8.81079C9.49098 8.80764 9.37033 8.75501 9.28168 8.66414C9.19303 8.57326 9.14341 8.45134 9.14341 8.32439C9.14341 8.19743 9.19303 8.07551 9.28168 7.98463C9.37033 7.89376 9.49098 7.84113 9.61789 7.83798Z" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_342_3972" x="0.325119" y="0.20842" width="21.341" height="21.341" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.33524"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.388235 0 0 0 0 0.640523 0 0 0 0 0.807843 0 0 0 0.51 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_342_3972"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3972" result="shape"/>
</filter>
<filter id="filter1_d_342_3972" x="5.99597" y="5.87891" width="9.9082" height="10.3533" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.445081"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.150327 0 0 0 0 0.247059 0 0 0 0 0.145098 0 0 0 0.75 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_342_3972"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3972" result="shape"/>
</filter>
<radialGradient id="paint0_radial_342_3972" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(10.9956 10.8789) scale(8)">
<stop stop-color="#D3D3D3"/>
<stop offset="0.03" stop-color="#D3D3D3" stop-opacity="0.9216"/>
<stop offset="0.13" stop-color="#D3D3D3" stop-opacity="0.7056"/>
<stop offset="0.23" stop-color="#D3D3D3" stop-opacity="0.5329"/>
<stop offset="0.35" stop-color="#D3D3D3" stop-opacity="0.4225"/>
<stop offset="0.47" stop-color="#D3D3D3" stop-opacity="0.3364"/>
<stop offset="0.6" stop-color="#D3D3D3" stop-opacity="0.2916"/>
<stop offset="0.76" stop-color="#D3D3D3" stop-opacity="0.2601"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.5"/>
</radialGradient>
<linearGradient id="paint1_linear_342_3972" x1="10.9956" y1="18.8789" x2="10.9956" y2="2.87891" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<radialGradient id="paint2_radial_342_3972" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(10.9956 10.8787) scale(7.11111)">
<stop stop-color="#D3D3D3"/>
<stop offset="1" stop-color="#0B103B" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint3_linear_342_3972" x1="10.9956" y1="17.9898" x2="10.9956" y2="3.76758" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<clipPath id="clip0_342_3972">
<rect width="9.90826" height="9.90826" fill="white" transform="translate(5.99597 5.87891)"/>
</clipPath>
</defs>
</svg>

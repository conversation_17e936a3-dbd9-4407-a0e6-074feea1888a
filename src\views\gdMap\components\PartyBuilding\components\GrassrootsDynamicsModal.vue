<template>
	<Modal ref="ModalRef" title="基层动态">
		<div class="news">
			<template v-for="item in data" :key="item.id">
				<div class="news-item">
					<div class="news-item-time">「{{ item.publishDate }}」</div>
					<div class="news-item-desc">
						{{ item.title }}
					</div>
					<div class="news-item-time"></div>
					<div class="news-item-link" @click="onInfo(item)">查看详情 <icon-double-right /></div>
				</div>
			</template>
		</div>
	</Modal>
	<ParagraphModal ref="ParagraphModalRef" :mask="false" />
</template>

<script setup>
import { usePartyBuildingStore } from "@/store";
import request from "@/utils/request";
import Modal from "./Modal.vue";
import ParagraphModal from "./ParagraphModal.vue";

const PartyBuildingStore = usePartyBuildingStore();
const ModalRef = ref(null);
const ParagraphModalRef = ref(null);
const data = ref([]);

const onOpen = () => {
	ModalRef.value?.open();
	getData();
};

const getData = () => {
	const params = {
		type: "基层动态",
		department: PartyBuildingStore.getDepartment,
	};
	request.get("/api/screen/dangjian/article/list", params).then((res) => {
		if (res.code == 200) {
			data.value = res.data;
		}
	});
};

const onInfo = (item) => {
	ParagraphModalRef.value.open(item.id);
};

defineExpose({
	open: onOpen,
});
</script>

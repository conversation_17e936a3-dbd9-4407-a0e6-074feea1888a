/**
 * 将 Esri JSON 转换为 GeoJSON
 * @param {*} esriJson
 * @returns
 */
export function esriToGeoJSON(esriJson) {
  const features = esriJson.features.map(feature => {
    return {
      type: 'Feature',
      geometry: {
        type: 'MultiLineString', // 根据 paths 数组的嵌套层级决定
        coordinates: feature.geometry.paths,
      },
      properties: feature.attributes,
    };
  });
  return {
    type: 'FeatureCollection',
    features,
  };
}

// import { divide } from "@/utils/number";

export const getPie3d = (pieData: any[], internalDiameterRatio?: any, colorList?: string[]) => {
	const series = [];
	// 总和
	let sumValue = 0;
	let startValue = 0;
	let endValue = 0;
	const legendData = [];
	const k =
		typeof internalDiameterRatio !== "undefined"
			? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
			: 1 / 3;
	// 为每一个饼图数据，生成一个 series-surface 配置
	for (let i = 0; i < pieData.length; i += 1) {
		sumValue += pieData[i].value;
	}

	for (let i = 0; i < pieData.length; i += 1) {
		const seriesItem: any = {
			name:
				typeof pieData[i].name === "undefined"
					? `series${i}`
					: pieData[i].name + "   " + pieData[i].percent + "%",

			type: "surface",
			// showEmptyCircle: false,
			parametric: true,
			wireframe: {
				show: false,
			},
			pieData: pieData[i],
			pieStatus: {
				selected: false,
				hovered: false,
				k,
			},
		};
		if (typeof pieData[i].itemStyle !== "undefined") {
			// const { itemStyle } = pieData[i];
			const itemStyle = pieData[i].itemStyle;
			// typeof pieData[i].itemStyle.color !== "undefined"
			// 	? (itemStyle.color = pieData[i].itemStyle.color)
			// 	: null;
			typeof pieData[i].itemStyle.opacity !== "undefined"
				? (itemStyle.opacity = pieData[i].itemStyle.opacity)
				: (itemStyle.opacity = 1); // 默认透明度 1
			seriesItem.itemStyle = itemStyle;
		} else {
			// 如果未传入 itemStyle，默认设置 opacity
			seriesItem.itemStyle = { opacity: 1 };
		}

		// console.log(seriesItem, "seriesItem");

		series.push(seriesItem);
	}
	// 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
	// 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
	for (let i = 0; i < series.length; i += 1) {
		endValue = startValue + series[i].pieData.value;
		series[i].pieData.startRatio = startValue / sumValue;
		series[i].pieData.endRatio = endValue / sumValue;
		series[i].parametricEquation = getParametricEquation(
			series[i].pieData.startRatio,
			series[i].pieData.endRatio,
			false,
			false,
			k,
			series[i].pieData.value
			// ，使除了第一个之外的值都是10
			// series[i].pieData.value === series[0].pieData.value ? 35 : 10
		);
		startValue = endValue;
		legendData.push(series[i].name);
	}
	// 准备待返回的配置项，把准备好的 legendData、series 传入。
	const option = {
		color: colorList,
		title: {
			text: "",
			textStyle: {
				rich: {
					a: {
						fontSize: 24,
						color: "#BBF9FF",
						fontFamily: "AlibabaPuHuiTi",
						fontWeight: 500,
						lineHeight: 40,
					},
				},
			},
			textAlign: "center",
			x: "45%",
			y: "0%",
		},

		legend: {
			show: false,
			data: legendData,
			orient: "vertical",
			icon: "roundRect",
			left: "center",
			top: "bottom",
			padding: 5,
			itemGap: 10,
			bottom: 0,
			itemWidth: 12, // 设置宽度
			itemHeight: 12, // 设置高度
			selectedMode: true,
			textStyle: {
				color: "#BBF9FF",
				fontSize: 24,
				fontFamily: "D-DIN-PRO",
				lineHeight: 28,
				rich: {
					a: {
						verticalAlign: "middle",
					},
				},
				padding: [0, 0, 5, 0],
			},
		},
		tooltip: {
			formatter: (params: any) => {
				if (params.seriesName !== "mouseoutSeries") {
					return `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params.color};"></span>${params.seriesName}`;
				}
				return "";
			},
			backgroundColor: "rgba(50, 50, 50, 0.7)",
			borderColor: "rgb(51, 51, 51)",
			textStyle: {
				color: "#fff",
			},
		},
		xAxis3D: {
			min: -1,
			max: 1,
		},
		yAxis3D: {
			min: -1,
			max: 1,
		},
		zAxis3D: {
			min: -1,
			max: "dataMax",
		},
		grid3D: {
			show: false,
			left: "center",
			top: "-19%",
			boxHeight: 19,
			// boxWidth: 100,
			// top: "-18%",
			viewControl: {
				// 3d效果可以放大、旋转等，请自己去查看官方配置
				alpha: 23,
				beta: 0, //旋转角度
				rotateSensitivity: 1,
				zoomSensitivity: 0,
				panSensitivity: 0,
				// autoRotate: true,
				distance: 141,
			},
			// 后处理特效可以为画面添加高光、景深、环境光遮蔽（SSAO）、调色等效果。可以让整个画面更富有质感。
			postEffect: {
				// 配置这项会出现锯齿，请自己去查看官方配置有办法解决
				enable: false,
				bloom: {
					enable: true,
					bloomIntensity: 0.1,
				},
				SSAO: {
					enable: true,
					quality: "medium",
					radius: 2,
				},
			},
		},
		series,
	};
	return option;
};

export function getParametricEquation(
	startRatio: any,
	endRatio: any,
	isSelected: any,
	isHovered: any,
	k: any,
	h: any
) {
	// 计算
	const midRatio = (startRatio + endRatio) / 2;
	const startRadian = startRatio * Math.PI * 2;
	const endRadian = endRatio * Math.PI * 2;
	const midRadian = midRatio * Math.PI * 2;

	// 如果只有一个扇形，则不实现选中效果。
	if (startRatio === 0 && endRatio === 1) {
		// eslint-disable-next-line no-param-reassign
		isSelected = false;
	}

	// 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
	// eslint-disable-next-line no-param-reassign
	k = typeof k !== "undefined" ? k : 1 / 3;

	// 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
	const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
	const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;

	// 计算高亮效果的放大比例（未高亮，则比例为 1）
	const hoverRate = isHovered ? 1.05 : 1;

	// 返回曲面参数方程
	return {
		u: {
			min: -Math.PI,
			max: Math.PI * 3,
			step: Math.PI / 32,
		},

		v: {
			min: 0,
			max: Math.PI * 2,
			step: Math.PI / 20,
		},

		x(u: any, v: any) {
			if (u < startRadian) {
				return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
			}
			if (u > endRadian) {
				return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
			}
			return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
		},

		y(u: any, v: any) {
			if (u < startRadian) {
				return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
			}
			if (u > endRadian) {
				return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
			}
			return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
		},

		z(u: any, v: any) {
			if (u < -Math.PI * 0.5) {
				return Math.sin(u);
			}
			if (u > Math.PI * 2.5) {
				return Math.sin(u) * h * 0.5;
			}
			// 当前图形的高度是Z根据h（每个value的值决定的）
			return Math.sin(v) > 0 ? 1 * h * 0.5 : -1;
		},
	};
}

<template>
  <div class="left-card">
    <m-card title="监测总览" :height="230" isRight>
      <div class="monitor-overview">
        <div class="item" v-for="(item, i) in listData" :key="i">
          <img class="icon" :src="images[item.image]" alt="" />
          <div class="content">
            <div class="number">
              <span class="count">{{ item.value }}</span>
              <span class="unit">{{ item.unit }}</span>
            </div>
            <div class="item-name">{{ item.name }}</div>
          </div>
        </div>
      </div>
    </m-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import mCard from '@/components/mCard/index.vue';
import proStat1 from '@/assets/images/dongtu/point-total.png';
import proStat2 from '@/assets/images/dongtu/warning-num.png';
import proStat3 from '@/assets/images/dongtu/processed.png';
import proStat4 from '@/assets/images/dongtu/process-percent.png';
import { getMonitorOverview } from '@/views/gdMap/services/dongtu.mock.js';

const images = {
  proStat1,
  proStat2,
  proStat3,
  proStat4,
};
const listData = ref();
onMounted(async () => {
  const defaultData = await getMonitorOverview();
  listData.value = defaultData.listData;
});
</script>

<style lang="scss" scoped>
.monitor-overview {
  color: #fff;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  height: 100%;
  padding: 25px 30px 0px;
  box-sizing: border-box;
  .item {
    width: 50%;
    height: 51px;
    display: flex;
    align-items: center;
    .icon {
      width: 41px;
      height: 41px;
    }
  }
  .content {
    display: flex;
    flex-direction: column;
    margin-left: 12px;
  }
  .number {
    font-size: 12px;
    .count {
      font-size: 20px;
      font-family: 'DDINPRO-Bold';
      margin-right: 4px;
      color: #0783fa;
    }
    .unit {
      font-size: 10px;
      line-height: 14px;
      vertical-align: text-bottom;
    }
  }
  .item-name {
    font-size: 12px;
    line-height: 16px;
  }
}
</style>

import { BaseMarker } from "../BaseMarker";
import maplibregl from "maplibre-gl";
import request from "@/utils/request";
import EngineeringMarkerVue from "@/views/gdMap/engine/components/MapLibreEngine/components/EngineeringMarker.vue";
import mapConfig from "@/config/engine/maplibre/map.config.js";

export class EngineeringMarker extends BaseMarker {
	static MAP_KEY = `${EngineeringMarker.name}Map`;
	static HANDLER_KEY = `_${EngineeringMarker.name}ZoomHandler`;

	static render(map, sourceId, options = {}) {
		const { data } = options;

		const markersData = this.preprocessData(data);

		this.renderMarkers(map, markersData, options);

		this.setupZoomHandler(map, options);
	}

	static preprocessData(data) {
		return data.features
			.map((item) => item)
			.filter((item) => {
				const {
					geometry: { coordinates },
				} = item;
				return coordinates && coordinates.length === 2;
			});
	}

	static renderMarkers(map, markersData, options) {
		markersData.forEach((markerData) => {
			this.createMarker(map, markerData, options);
		});
	}

	static createMarker(map, markerData, options) {
		const {
			geometry: { coordinates },
			properties,
		} = markerData;
		const { type } = options;

		const el = document.createElement("div");

		const app = createApp(EngineeringMarkerVue, {
			properties,
			type,
		});
		app.mount(el);

		const marker = new maplibregl.Marker({
			element: el,
		})
			.setLngLat(coordinates)
			.addTo(map);
		marker._data = markerData;

		if (!map[this.MAP_KEY]) {
			map[this.MAP_KEY] = new Map();
		}
		const id = `${EngineeringMarker.name}-${type}-${
			markerData.properties.departmentId
		}-${Date.now()}`;
		map[this.MAP_KEY].set(id, marker);

		this.updateElementSize(marker, mapConfig.minzoom * 7);

		map.on("zoom", () => {
			const zoom = map.getZoom();
			// 根据 circle-radius 的插值规则设置 size
			let size;
			if (zoom <= 5) {
				size = 6;
			} else if (zoom <= 9) {
				// 5~9: 6~14
				size = 6 + ((zoom - 5) / (9 - 5)) * (14 - 6);
			} else if (zoom <= 13) {
				// 9~13: 14~44
				size = 14 + ((zoom - 9) / (13 - 9)) * (44 - 14);
			} else {
				size = 44;
			}
			this.updateElementSize(marker, size * 7);
		});
	}

	static removeMarkers(map) {
		const markers = this.getMarkers(map);
		markers.forEach((marker) => {
			marker.remove();
		});
		if (map[this.MAP_KEY]) {
			map[this.MAP_KEY].clear();
		}
	}

	static getMarkers(map) {
		if (map[this.MAP_KEY]) {
			const markers = Array.from(map[this.MAP_KEY].values());
			return markers;
		}
		return [];
	}

	static setVisibility(map, isVisible) {
		const markers = this.getMarkers(map);
		markers.forEach((marker) => {
			marker._element.style.visibility = isVisible ? "visible" : "hidden";
		});
	}

	static setupZoomHandler(map) {
		if (!map[this.HANDLER_KEY]) {
			let lastZoom = map.getZoom();

			map[this.HANDLER_KEY] = () => {
				const zoom = map.getZoom();
				this.setVisibility(map, zoom < 9);

				if (lastZoom < 9 && zoom >= 9) {
					[EngineeringCulvertMarker, EngineeringBridgetMarker].forEach((MarkerClass) => {
						MarkerClass.render(map, {
							type: MarkerClass.markerType,
						});
					});
				}

				lastZoom = zoom;
			};

			map.on("zoomend", map[this.HANDLER_KEY]);
		}
	}

	static remove(map) {
		map.off("zoomend", map[this.HANDLER_KEY]);
		this.removeMarkers(map);
	}

	static updateElementSize(marker, size) {
		const element = marker.getElement();
		element.style.width = size + "px";
		element.style.height = size + "px";
		element.style.fontSize = size + "px";
	}
}

class EngineeringActualMarker extends EngineeringMarker {
	static async render(map, options) {
		const geojson = await this.loadData();

		const markersData = this.preprocessData(geojson);

		this.renderMarkers(map, markersData, options);

		this.setupZoomHandler(map);
	}

	static createMarker(map, markerData, options) {
		const {
			geometry: { coordinates },
			properties,
		} = markerData;

		const { type } = options;

		const el = document.createElement("div");

		const app = createApp(EngineeringMarkerVue, {
			properties,
			type,
		});
		app.mount(el);

		const marker = new maplibregl.Marker({
			element: el,
		})
			.setLngLat(coordinates)
			.addTo(map);
		marker._data = markerData;

		if (!map[this.MAP_KEY]) {
			map[this.MAP_KEY] = new Map();
		}
		const id = `${type}-${
			markerData.properties.bridgeId || markerData.properties.culvertId
		}-${Date.now()}`;
		map[this.MAP_KEY].set(id, marker);

		this.updateElementSize(marker, 32 * 3);
	}

	static setupZoomHandler(map) {
		if (!map[this.HANDLER_KEY]) {
			let lastZoom = map.getZoom();

			map[this.HANDLER_KEY] = () => {
				const zoom = map.getZoom();
				this.setVisibility(map, zoom >= 9);

				if (lastZoom < 9 && zoom >= 9) {
					this.removeMarkers(map);
					this.render();
				}

				lastZoom = zoom;
			};

			map.on("zoomend", map[this.HANDLER_KEY]);
		}
	}

	static remove(map) {
		map.off("zoomend", map[handlerKey]);
		this.removeMarkers(map);
	}
}

class EngineeringCulvertMarker extends EngineeringActualMarker {
	static MAP_KEY = `${EngineeringCulvertMarker.name}Map`;
	static HANDLER_KEY = `_${EngineeringCulvertMarker.name}ZoomHandler`;
	static markerType = `culvert-actual`;

	static async loadData() {
		const { data = [] } = await request.get("/api/screen/engineering/center/culvert/list");

		return {
			type: "FeatureCollection",
			features: data.map((item) => ({
				type: "Feature",
				geometry: {
					type: "Point",
					coordinates: item.coordinates,
				},
				properties: item,
			})),
		};
	}
}

class EngineeringBridgetMarker extends EngineeringActualMarker {
	static MAP_KEY = `${EngineeringBridgetMarker.name}Map`;
	static HANDLER_KEY = `_${EngineeringBridgetMarker.name}ZoomHandler`;
	static markerType = `bridge-actual`;

	static async loadData() {
		const { data = [] } = await request.get("/api/screen/engineering/center/bridge/list");

		return {
			type: "FeatureCollection",
			features: data.map((item) => ({
				type: "Feature",
				geometry: {
					type: "Point",
					coordinates: item.coordinates,
				},
				properties: item,
			})),
		};
	}
}

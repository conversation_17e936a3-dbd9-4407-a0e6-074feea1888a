<svg width="23" height="22" viewBox="0 0 23 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#230;&#182;&#181;&#230;&#180;&#158;">
<g id="Ellipse 15" filter="url(#filter0_d_342_3766)">
<circle cx="11.4918" cy="10.7393" r="8" fill="url(#paint0_radial_342_3766)" fill-opacity="0.7"/>
<circle cx="11.4918" cy="10.7393" r="7.55492" stroke="url(#paint1_linear_342_3766)" stroke-width="0.890162"/>
</g>
<circle id="Ellipse 16" cx="11.5655" cy="10.8137" r="7.07681" fill="url(#paint2_radial_342_3766)" stroke="url(#paint3_linear_342_3766)" stroke-width="0.200286"/>
<g id="&#230;&#182;&#181;&#230;&#180;&#158;_2" filter="url(#filter1_d_342_3766)">
<g id="Frame" clip-path="url(#clip0_342_3766)">
<path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M8.46228 9.30408C8.56694 9.26292 8.66519 9.21888 8.75805 9.17283C8.80949 9.14735 8.84212 9.09503 8.84212 9.03768V8.20999C8.84212 8.1007 8.72944 8.02779 8.62993 8.07272C8.54322 8.11174 8.45212 8.14888 8.3555 8.18364C8.29602 8.2051 8.25649 8.26182 8.25649 8.32506V9.16392C8.25649 9.27008 8.36353 9.34298 8.46228 9.30408ZM11.0139 6.73946C10.9053 6.7579 10.8039 6.783 10.7082 6.81374C10.6461 6.8337 10.6043 6.89179 10.6043 6.95692V7.80343C10.6043 7.90331 10.6996 7.97471 10.796 7.94824C10.8801 7.92515 10.9684 7.90632 11.0622 7.89252C11.1357 7.8816 11.1899 7.81811 11.1899 7.7437V6.8879C11.1899 6.79467 11.1058 6.7239 11.0139 6.73946ZM12.2564 6.8091C12.1618 6.78137 12.061 6.75853 11.9527 6.74134C11.8611 6.72678 11.778 6.7973 11.778 6.89004V7.74621C11.778 7.821 11.8329 7.88474 11.9069 7.89516C12.0018 7.90858 12.0909 7.92628 12.1751 7.94761C12.2707 7.97183 12.3637 7.90055 12.3637 7.80192V6.95328C12.3638 6.88677 12.3203 6.82767 12.2564 6.8091ZM9.77521 7.3414C9.74107 7.36684 9.7068 7.39259 9.67225 7.41855L9.67224 7.41855C9.61307 7.463 9.5531 7.50806 9.49162 7.55322C9.47263 7.56721 9.4572 7.58547 9.44656 7.60653C9.43592 7.62758 9.43038 7.65084 9.43038 7.67443V8.45958C9.43038 8.58368 9.57218 8.65458 9.67144 8.57992L9.67968 8.57373L9.6797 8.57371C9.77157 8.50471 9.86164 8.43707 9.95265 8.37275C9.99242 8.34451 10.016 8.29884 10.016 8.25002V7.46186C10.016 7.33801 9.87459 7.26724 9.77521 7.3414ZM15.2997 8.54993V9.4061C15.2997 9.48039 15.3538 9.54363 15.4272 9.55492C15.694 9.59621 15.9944 9.62206 16.3363 9.62808C16.4203 9.62958 16.4893 9.56182 16.4894 9.47788L16.4917 8.61844C16.4919 8.53612 16.4258 8.46887 16.3435 8.46749C16.0176 8.46196 15.7293 8.4385 15.4719 8.40085C15.381 8.38755 15.2997 8.4582 15.2997 8.54993ZM13.3504 7.44137L13.3504 7.44134L13.3503 7.44133C13.2977 7.40036 13.2457 7.35981 13.1935 7.32007C13.0945 7.24478 12.9521 7.3158 12.9521 7.44015V8.22719C12.9521 8.276 12.9758 8.32192 13.0157 8.35003C13.1101 8.41654 13.2019 8.48668 13.2946 8.55871C13.3936 8.63551 13.5377 8.56511 13.5377 8.43988V7.66038C13.5376 7.6137 13.516 7.56953 13.479 7.54104C13.4355 7.50768 13.3928 7.47439 13.3504 7.44137ZM7.49569 8.39558C7.24083 8.43386 6.95761 8.45782 6.63989 8.46347C6.5577 8.46498 6.49182 8.53186 6.49182 8.61392V9.47348C6.49182 9.55768 6.56084 9.62569 6.64504 9.62406C6.9797 9.61791 7.27622 9.59131 7.54149 9.54915C7.61464 9.53748 7.66835 9.47449 7.66835 9.40033V8.54428C7.66823 8.45255 7.58654 8.38191 7.49569 8.39558ZM14.6127 8.19142C14.5158 8.15641 14.425 8.11902 14.339 8.07974C14.2393 8.03407 14.1258 8.10685 14.1258 8.21664V9.04295C14.1258 9.09979 14.158 9.15162 14.2085 9.17747C14.3011 9.22452 14.3995 9.26945 14.5052 9.31148C14.6041 9.35076 14.7113 9.27786 14.7113 9.17145V8.33284C14.7115 8.2696 14.6722 8.21288 14.6127 8.19142ZM11.4551 8.48911C10.6227 8.48911 10.1762 8.82474 9.67314 9.20293C9.0723 9.65462 8.39071 10.167 6.87442 10.2409C6.66022 10.2513 6.4917 10.4274 6.4917 10.6419V12.1995C6.4917 12.4212 6.67152 12.6011 6.89325 12.6011H9.74849C9.77421 12.6011 9.7899 12.5726 9.77597 12.5509C9.56905 12.2281 9.44883 11.8434 9.44883 11.4303C9.44883 10.2903 10.3636 9.36624 11.492 9.36624C12.6203 9.36624 13.5351 10.2904 13.5351 11.4303C13.5351 11.8435 13.4149 12.2283 13.2078 12.551C13.1939 12.5727 13.2094 12.6011 13.2352 12.6011H16.0817C16.3028 12.6011 16.4823 12.4224 16.4833 12.2013L16.4902 10.648C16.4912 10.4327 16.322 10.255 16.107 10.2451C14.5675 10.1742 13.9279 9.67614 13.353 9.2285C12.8537 8.83979 12.4033 8.48911 11.4551 8.48911ZM10.6829 12.2497C10.4683 12.0328 10.3477 11.7388 10.3477 11.4321C10.3477 11.1255 10.4683 10.8314 10.6829 10.6146C10.8975 10.3977 11.1886 10.2759 11.4921 10.2759C11.7957 10.2759 12.0867 10.3977 12.3013 10.6146C12.516 10.8314 12.6365 11.1255 12.6365 11.4321C12.6365 11.7388 12.516 12.0328 12.3013 12.2497C12.0867 12.4665 11.7957 12.5883 11.4921 12.5883C11.1886 12.5883 10.8975 12.4665 10.6829 12.2497Z" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_342_3766" x="0.821335" y="0.0687718" width="21.341" height="21.341" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.33524"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.388235 0 0 0 0 0.640523 0 0 0 0 0.807843 0 0 0 0.51 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_342_3766"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3766" result="shape"/>
</filter>
<filter id="filter1_d_342_3766" x="6.42725" y="5.15332" width="10" height="10.4451" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.445081"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.150327 0 0 0 0 0.247059 0 0 0 0 0.145098 0 0 0 0.75 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_342_3766"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3766" result="shape"/>
</filter>
<radialGradient id="paint0_radial_342_3766" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(11.4918 10.7393) scale(8)">
<stop stop-color="#D3D3D3"/>
<stop offset="0.03" stop-color="#D3D3D3" stop-opacity="0.9216"/>
<stop offset="0.13" stop-color="#D3D3D3" stop-opacity="0.7056"/>
<stop offset="0.23" stop-color="#D3D3D3" stop-opacity="0.5329"/>
<stop offset="0.35" stop-color="#D3D3D3" stop-opacity="0.4225"/>
<stop offset="0.47" stop-color="#D3D3D3" stop-opacity="0.3364"/>
<stop offset="0.6" stop-color="#D3D3D3" stop-opacity="0.2916"/>
<stop offset="0.76" stop-color="#D3D3D3" stop-opacity="0.2601"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.5"/>
</radialGradient>
<linearGradient id="paint1_linear_342_3766" x1="11.4918" y1="18.7393" x2="11.4918" y2="2.73926" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<radialGradient id="paint2_radial_342_3766" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(11.5655 10.8137) scale(7.17695)">
<stop stop-color="#D3D3D3"/>
<stop offset="1" stop-color="#0B103B" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint3_linear_342_3766" x1="11.5655" y1="17.9906" x2="11.5655" y2="3.63672" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<clipPath id="clip0_342_3766">
<rect width="10" height="10" fill="white" transform="translate(6.42725 5.15332)"/>
</clipPath>
</defs>
</svg>

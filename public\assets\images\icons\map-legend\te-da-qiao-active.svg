<svg width="23" height="22" viewBox="0 0 23 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#231;&#137;&#185;&#229;&#164;&#167;&#230;&#161;&#165;">
<g id="Ellipse 15" filter="url(#filter0_d_505_3018)">
<ellipse cx="11.4648" cy="11.3003" rx="8" ry="7.93796" fill="url(#paint0_radial_505_3018)" fill-opacity="0.7"/>
<path d="M19.0198 11.3003C19.0198 15.4352 15.6406 18.7931 11.4648 18.7931C7.2891 18.7931 3.90992 15.4352 3.90992 11.3003C3.90992 7.16532 7.2891 3.80739 11.4648 3.80739C15.6406 3.80739 19.0198 7.16532 19.0198 11.3003Z" stroke="url(#paint1_linear_505_3018)" stroke-width="0.890162"/>
</g>
<path id="Ellipse 16" d="M18.4756 11.3011C18.4756 15.1419 15.3374 18.2569 11.4646 18.2569C7.59184 18.2569 4.45366 15.1419 4.45366 11.3011C4.45366 7.46022 7.59184 4.34526 11.4646 4.34526C15.3374 4.34526 18.4756 7.46022 18.4756 11.3011Z" fill="url(#paint2_radial_505_3018)" stroke="url(#paint3_linear_505_3018)" stroke-width="0.200286"/>
<g id="Group 1321315401" filter="url(#filter1_d_505_3018)">
<g id="Frame" clip-path="url(#clip0_505_3018)">
<path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M12.7167 7.46916V7.02832H12.9973V7.46577L12.9976 7.46573C13.1398 8.6186 15.0721 11.3193 16.6006 11.9122H16.6777C16.7472 11.9122 16.8036 11.9681 16.8036 12.0371V12.466C16.8036 12.535 16.7473 12.5909 16.6778 12.5909H6.52578C6.45631 12.5909 6.3999 12.5351 6.3999 12.466V12.0371C6.3999 11.9682 6.45619 11.9122 6.52578 11.9122H6.57323C6.96573 11.7805 7.76074 11.0785 8.54006 10.1708C9.38079 9.19156 10.0096 8.21128 10.2031 7.58277V7.02832H10.4837V7.46916H10.9269V7.35631C10.9269 7.34208 10.9326 7.32844 10.9427 7.31838C10.9529 7.30833 10.9666 7.30268 10.9809 7.30268C10.9953 7.30268 11.009 7.30833 11.0192 7.31838C11.0293 7.32844 11.035 7.34208 11.035 7.35631V7.46916H11.5462V7.35853C11.5462 7.3443 11.5519 7.33066 11.562 7.3206C11.5721 7.31055 11.5859 7.3049 11.6002 7.3049C11.6145 7.3049 11.6283 7.31055 11.6384 7.3206C11.6486 7.33066 11.6543 7.3443 11.6543 7.35853V7.46916H12.1654V7.35853C12.1654 7.3443 12.1711 7.33066 12.1812 7.3206C12.1914 7.31055 12.2051 7.3049 12.2195 7.3049C12.2338 7.3049 12.2475 7.31055 12.2577 7.3206C12.2678 7.33066 12.2735 7.3443 12.2735 7.35853V7.46916H12.7167ZM7.57267 11.9122H10.2031V8.33181C9.89038 8.92536 9.38952 9.63759 8.76702 10.3627C8.40623 10.7828 7.98112 11.2302 7.58038 11.5815C8.15359 11.3068 8.90179 10.8791 9.70969 10.3313C9.81143 10.2623 9.91128 10.1933 10.0091 10.1243L10.0093 10.4857C9.96274 10.5179 9.91882 10.5479 9.87796 10.5756C9.12917 11.0833 8.25969 11.596 7.57267 11.9122ZM12.9973 10.3581V8.41015C13.2411 8.93542 13.6167 9.54029 14.0711 10.1232C14.6647 10.8849 15.2974 11.4904 15.8864 11.871C15.0639 11.6472 14.0244 11.1077 12.9973 10.3581ZM12.29 10.1875L12.2899 11.9122H10.8695V9.22946C10.8695 9.10862 10.9001 8.99487 10.9542 8.89547C11.3313 9.34726 11.808 9.79512 12.29 10.1875ZM12.29 9.80299C11.8362 9.41904 11.449 9.03164 11.1512 8.66738C11.2703 8.57782 11.4188 8.52468 11.5798 8.52468C11.972 8.52468 12.2901 8.84015 12.2901 9.22946L12.29 9.80299ZM12.9973 10.7227C13.6308 11.1681 14.3822 11.6126 15.1061 11.9122H12.9973V10.7227ZM10.3983 14.908L12.8696 14.5326C12.9057 14.525 12.9382 14.5052 12.9614 14.4767C12.9847 14.4482 12.9974 14.4126 12.9975 14.376V12.9214C12.9975 12.8625 12.9494 12.8148 12.8901 12.8148H10.3106C10.2512 12.8148 10.2032 12.8625 10.2032 12.9214V14.7514C10.2032 14.8534 10.2979 14.9293 10.3983 14.908Z" fill="url(#paint4_linear_505_3018)"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_505_3018" x="0.794358" y="0.691819" width="21.341" height="21.2169" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.33524"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.388235 0 0 0 0 0.640523 0 0 0 0 0.807843 0 0 0 0.51 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_505_3018"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_505_3018" result="shape"/>
</filter>
<filter id="filter1_d_505_3018" x="6.46484" y="5.84668" width="10" height="10.3679" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.445081"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.150327 0 0 0 0 0.247059 0 0 0 0 0.145098 0 0 0 0.75 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_505_3018"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_505_3018" result="shape"/>
</filter>
<radialGradient id="paint0_radial_505_3018" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(11.4648 11.3003) scale(8 7.93796)">
<stop stop-color="#000101" stop-opacity="0"/>
<stop offset="1" stop-color="#07B5FA"/>
</radialGradient>
<linearGradient id="paint1_linear_505_3018" x1="11.4648" y1="19.2382" x2="11.4648" y2="3.3623" gradientUnits="userSpaceOnUse">
<stop stop-color="#07B5FA"/>
<stop offset="0.49" stop-color="#0783FA" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#0783FA" stop-opacity="0.13"/>
</linearGradient>
<radialGradient id="paint2_radial_505_3018" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(11.4646 11.3011) scale(7.11111 7.05596)">
<stop stop-color="#07B5FA"/>
<stop offset="1" stop-color="#0B103B" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint3_linear_505_3018" x1="11.4646" y1="18.357" x2="11.4646" y2="4.24512" gradientUnits="userSpaceOnUse">
<stop stop-color="#07B5FA"/>
<stop offset="0.49" stop-color="#07B5FA" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#07B5FA" stop-opacity="0.13"/>
</linearGradient>
<linearGradient id="paint4_linear_505_3018" x1="8.8326" y1="14.4561" x2="10.4761" y2="6.77518" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#07B5FA"/>
</linearGradient>
<clipPath id="clip0_505_3018">
<rect width="10" height="9.92245" fill="white" transform="translate(6.46484 5.84668)"/>
</clipPath>
</defs>
</svg>

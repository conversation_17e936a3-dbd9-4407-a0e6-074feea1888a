<template>
	<div class="tree-item-box">
		<!-- 第一层：分组 -->
		<div class="tree-item level1" @click.stop="toggle(item)">
			<span class="arrow" :class="{ expanded: item.expanded }"></span>
			<span class="tree-item-label">
				{{ item.departmentName }}
				<template v-if="item.totalDeviceCount">({{ item.totalDeviceCount }})</template>
			</span>
		</div>
		<!-- 第二层：子分组 -->
		<div v-if="item.expanded && item.children && item.children.length">
			<div v-for="sub in item.children" :key="sub.type">
				<div class="tree-item level2" @click.stop="toggle(sub)">
					<span class="arrow" :class="{ expanded: sub.expanded }"></span>
					<span class="tree-item-label-three">
						{{ sub.typeName }}
						<template v-if="sub.deviceCount">{{ sub.deviceCount }}</template></span
					>
				</div>
				<!-- 第三层：成员 -->
				<div v-if="sub.expanded && sub.devices && sub.devices.length">
					<div
						v-for="member in sub.devices"
						:key="member.id"
						class="tree-item level3"
						:class="{ selected: selectedMember === member }"
						:style="{ paddingLeft: `${(item.level + 1) * 16}px` }"
						@click="handleMemberClick(member)"
					>
						<span
							:class="[
								'status-dot',
								{
									offline: member.state === 0,
									online: member.state === 1,
									warning: member.state === 2,
								},
							]"
						></span>
						<span class="tree-item-label-three">{{ member.name }}</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import emitter from "@/utils/emitter";
const props = defineProps({
	item: {
		type: Object,
		required: true,
	},
});
const selectedMember = ref(null);
function toggle(node) {
	node.expanded = !node.expanded;
}
function handleMemberClick(member) {
	selectedMember.value = member;
	emitter.$emit("staff-select", member.coordinates);
}
</script>

<style scoped lang="scss">
.tree-item-box {
	margin-bottom: 16px;
	background: rgba(0, 0, 0, 0.3);
}
.tree-item {
	display: flex;
	align-items: center;
	font-size: 12px;
	color: #fff;
	line-height: 33px;
	cursor: pointer;

	&.level1 {
		padding-left: 16px;
	}

	&.level2 {
		padding-left: 32px;
	}

	&.level3 {
		padding-left: 48px;
	}
}
.tree-item.selected {
	background: rgba(142, 184, 255, 0.1);
	border-radius: 999px;
}
.arrow {
	display: inline-block;
	width: 0;
	height: 0;
	margin-right: 8px;
	border-top: 5px solid transparent;
	border-bottom: 5px solid transparent;
	border-left: 5px solid #8dbfff;
	cursor: pointer;
	transition: transform 0.5s;
}
.arrow.expanded {
	border-left: 5px solid transparent;
	border-right: 5px solid transparent;
	border-top: 5px solid #8dbfff;
	border-bottom: 0;
}
.status-dot {
	width: 6px;
	height: 6px;
	border-radius: 50%;
	display: inline-block;
	margin-right: 8px;
}
.status-dot.online {
	background: #27f3bd;
	box-shadow: 0px 0px 6px 0px rgba(41, 255, 198, 0.6);
}
.status-dot.offline {
	background: #f3272e;
	box-shadow: 0px 0px 6px 0px rgba(255, 41, 48, 0.6);
}
.status-dot.warning {
	background: #ffb327;
	box-shadow: 0px 0px 6px 0px rgba(255, 179, 39, 0.6);
}
.tree-item-label {
	color: #fff;
}
.tree-item-label-three {
	color: rgba(255, 255, 255, 0.7);
}
</style>

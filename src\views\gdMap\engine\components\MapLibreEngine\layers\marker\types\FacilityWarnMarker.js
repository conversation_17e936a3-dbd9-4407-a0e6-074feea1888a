import { BaseMarker } from '../BaseMarker';
import maplibregl from 'maplibre-gl';
import { createApp } from 'vue';
import FacilityWarnInfo from '../../../components/FacilityWarnInfo.vue';
import mapConfig from '@/config/engine/maplibre/map.config.js';

// 图层缩放级别常量
const LAYER_ZOOM_LEVELS = {
  ICON: {
    MIN: 5,
    MAX: mapConfig.maxzoom,
    CLUSTER: {
      MAX_ZOOM: 14,
      RADIUS: 100,
    },
    SIZE: {
      DEFAULT: 0.6,
      CLUSTER: {
        DEFAULT: 0.8,
        MEDIUM: 1.0,
        LARGE: 1.2,
      },
    },
  },
  BOARD: {
    MIN: 5,
    MAX: mapConfig.maxzoom,
  },
};

/**
 * 设备预警标记渲染器
 */
export class FacilityWarnMarker extends BaseMarker {
  /**
   * 渲染设备预警标记
   * @param {Object} map - MapLibre地图实例
   * @param {String} sourceId - 数据源ID
   * @param {Object} options - 渲染选项
   * @returns {Object} 渲染结果
   */
  static render(map, sourceId, options = {}) {
    const { data, iconImage = 'facility-warn-icon' } = options;

    // 如果已存在源，先移除
    this.remove(map, sourceId);

    // 处理数据，分离图标标记和弹窗标记
    const { iconMarkers, popupMarkers } = this.processMarkerData(data);

    // 添加数据源
    map.addSource(sourceId, {
      type: 'geojson',
      data: iconMarkers,
      cluster: true, // 启用聚合
      clusterMaxZoom: LAYER_ZOOM_LEVELS.ICON.CLUSTER.MAX_ZOOM, // 最大聚合缩放级别
      clusterRadius: LAYER_ZOOM_LEVELS.ICON.CLUSTER.RADIUS, // 聚合半径
      generateId: true,
    });

    // 添加图层
    const layerId = `${sourceId}-layer`;

    // 添加聚合图层
    const clusterLayerId = `${sourceId}-clusters`;
    map.addLayer({
      id: clusterLayerId,
      type: 'symbol',
      source: sourceId,
      filter: ['has', 'point_count'],
      layout: {
        'icon-image': iconImage,
        'icon-allow-overlap': true,
        'icon-ignore-placement': true,
        'icon-size': [
          'interpolate',
          ['linear'],
          ['zoom'],
          5,
          LAYER_ZOOM_LEVELS.ICON.SIZE.DEFAULT, // 缩放级别5时使用默认大小
          10,
          [
            'step',
            ['get', 'point_count'],
            LAYER_ZOOM_LEVELS.ICON.SIZE.CLUSTER.DEFAULT, // 默认聚合大小
            5,
            LAYER_ZOOM_LEVELS.ICON.SIZE.CLUSTER.MEDIUM, // 5个点以上大小
            15,
            LAYER_ZOOM_LEVELS.ICON.SIZE.CLUSTER.LARGE, // 10个点以上大小
          ],
        ],
      },
      // 设置图层的可见性范围
      minzoom: LAYER_ZOOM_LEVELS.ICON.MIN,
      maxzoom: LAYER_ZOOM_LEVELS.ICON.MAX,
    });

    // 添加非聚合点图层
    map.addLayer({
      id: layerId,
      type: 'symbol',
      source: sourceId,
      filter: ['!', ['has', 'point_count']], // 只显示非聚合点
      layout: {
        'icon-image': iconImage,
        'icon-allow-overlap': true,
        'icon-ignore-placement': true,
        'icon-size': LAYER_ZOOM_LEVELS.ICON.SIZE.DEFAULT, // 图标大小
      },
      // 设置图层的可见性范围
      minzoom: LAYER_ZOOM_LEVELS.ICON.MIN,
      maxzoom: LAYER_ZOOM_LEVELS.ICON.MAX,
    });

    // 添加点击事件
    this.setupClickHandler(map, layerId, options);

    return {
      layerId,
      sourceId,
    };
  }

  /**
   * 处理标记数据，分离图标标记和弹窗标记
   * @param {Object} data - GeoJSON数据
   * @returns {Object} 分离后的图标标记和弹窗标记
   */
  static processMarkerData(data) {
    if (!data || data.type !== 'FeatureCollection' || !Array.isArray(data.features)) {
      return { iconMarkers: data, popupMarkers: [] };
    }

    const iconMarkers = {
      type: 'FeatureCollection',
      features: [],
    };

    const popupMarkers = [];

    // 为每个特征添加样式和位置信息
    data.features.forEach((feature, index) => {
      const id = feature.properties.id || `facility-warn-${index}`;
      const coordinates = feature.geometry.coordinates;
      const properties = feature.properties || {};

      // 添加到图标标记集合
      iconMarkers.features.push({
        ...feature,
        id,
        properties,
      });

      // 同时添加到弹窗标记集合，用于显示详细信息
      popupMarkers.push({
        id,
        coordinates,
        title: properties.formData?.title || '设备预警',
        properties,
      });
    });

    return { iconMarkers, popupMarkers };
  }

  /**
   * 设置点击事件处理
   * @param {Object} map - MapLibre地图实例
   * @param {String} layerId - 图层ID
   * @param {Object} options - 选项
   */
  static setupClickHandler(map, layerId, options = {}) {
    const handlerKey = `_${this.name}ClickHandler`;

    // 移除已有的点击事件
    if (map[handlerKey]) {
      map.off('click', layerId, map[handlerKey]);
    }

    // 创建新的点击事件处理函数
    map[handlerKey] = e => {
      const features = map.queryRenderedFeatures(e.point, {
        layers: [layerId],
      });

      if (features.length > 0) {
        const feature = features[0];
        const coordinates = feature.geometry.coordinates.slice();
        const properties = feature.properties;
        // 尝试从多个可能的来源获取ID
        const id =
          feature.id || (properties && properties.id) || `facility-warn-marker-${Date.now()}`;

        // 防止地图缩放时弹出窗口位置偏移
        while (Math.abs(e.lngLat.lng - coordinates[0]) > 180) {
          coordinates[0] += e.lngLat.lng > coordinates[0] ? 360 : -360;
        }

        // 优化：检查当前点击的标记是否已存在，实现切换效果
        const isCurrentMarkerExist =
          map._facilityWarnBoardMarkers && map._facilityWarnBoardMarkers.has(id);

        // 清除所有标记
        this.clearBoardMarkers(map);

        // 只有当前标记不存在时才创建新标记，实现切换效果
        if (!isCurrentMarkerExist) {
          this.createBoardMarker(
            map,
            id,
            coordinates,
            properties.formData?.title || '设备预警',
            properties
          );
        }

        // 标记此事件已被处理，防止全局点击事件再次处理
        e.originalEvent._handledByLayerClick = true;

        // 阻止事件继续传播
        e.originalEvent.stopPropagation();
      }
    };

    // 添加点击事件
    map.on('click', layerId, map[handlerKey]);
  }

  /**
   * 创建立牌标记
   * @param {Object} map - MapLibre地图实例
   * @param {String} id - 标记ID
   * @param {Array} lngLat - 经纬度坐标
   * @param {String} title - 标记标题
   * @param {Object} properties - 标记属性
   * @returns {Object} 标记对象
   */
  static createBoardMarker(map, id, lngLat, title, properties = {}) {
    const el = document.createElement('div');
    el.className = 'facility-warn-marker-container';
    // 添加数据属性，用于标识标记ID
    el.dataset.markerId = id;

    // 创建Vue应用实例
    const app = createApp(FacilityWarnInfo, {
      properties,
    });

    // 获取组件实例
    const componentInstance = app.mount(el);

    // 设置关闭回调（如果组件支持）
    if (componentInstance.setCloseCallback) {
      componentInstance.setCloseCallback(() => {
        this.removeBoardMarker(map, id);
      });
    }

    // 创建 Marker
    const marker = new maplibregl.Marker({
      element: el,
      anchor: 'bottom', // 使弹窗底部对齐图标位置
      offset: [0, -30], // 添加垂直偏移，使弹窗在图标上方有一定距离
      draggable: false,
    })
      .setLngLat(lngLat)
      .addTo(map);

    // 保存标记信息
    if (!map._facilityWarnBoardMarkers) {
      map._facilityWarnBoardMarkers = new Map();

      // 只在第一次创建标记时添加全局点击事件
      this.setupGlobalClickHandler(map);
    }

    map._facilityWarnBoardMarkers.set(id, {
      marker,
      app,
      el,
      properties,
      componentInstance,
    });

    return marker;
  }

  /**
   * 设置全局点击事件处理器
   * @param {Object} map - MapLibre地图实例
   */
  static setupGlobalClickHandler(map) {
    // 如果已经设置了全局点击事件，则不重复设置
    if (map._facilityWarnGlobalClickHandler) {
      return;
    }

    // 创建全局点击事件处理函数，使用防抖处理
    const handleGlobalClick = e => {
      // 检查是否是从图层点击事件触发的
      if (e.originalEvent._handledByLayerClick) {
        return;
      }

      // 优化：使用更简洁的方式检查点击是否在标记元素内
      const isMarkerClick = e.originalEvent
        .composedPath()
        .some(
          element =>
            element.className &&
            typeof element.className === 'string' &&
            element.className.includes('facility-warn-marker-container')
        );

      // 如果点击不在任何标记元素内，则关闭所有标记
      if (!isMarkerClick) {
        this.clearBoardMarkers(map);
      }
    };

    // 添加防抖处理，避免频繁触发
    map._facilityWarnGlobalClickHandler = handleGlobalClick;

    // 添加点击事件到地图
    map.on('click', map._facilityWarnGlobalClickHandler);
  }

  /**
   * 移除立牌标记
   * @param {Object} map - MapLibre地图实例
   * @param {String} id - 标记ID
   */
  static removeBoardMarker(map, id) {
    if (map._facilityWarnBoardMarkers && map._facilityWarnBoardMarkers.has(id)) {
      const { marker, app } = map._facilityWarnBoardMarkers.get(id);
      marker.remove();
      app.unmount();
      map._facilityWarnBoardMarkers.delete(id);
    }
  }

  /**
   * 清除所有立牌标记
   * @param {Object} map - MapLibre地图实例
   */
  static clearBoardMarkers(map) {
    if (map._facilityWarnBoardMarkers) {
      map._facilityWarnBoardMarkers.forEach(({ marker, app }) => {
        marker.remove();
        app.unmount();
      });
      map._facilityWarnBoardMarkers.clear();
    }
  }

  /**
   * 移除图层和数据源
   * @param {Object} map - MapLibre地图实例
   * @param {String} sourceId - 数据源ID
   */
  static remove(map, sourceId) {
    const layerId = `${sourceId}-layer`;
    const clusterLayerId = `${sourceId}-clusters`;

    // 移除图层
    if (map.getLayer(layerId)) {
      map.removeLayer(layerId);
    }

    // 移除聚合图层
    if (map.getLayer(clusterLayerId)) {
      map.removeLayer(clusterLayerId);
    }

    // 移除数据源
    if (map.getSource(sourceId)) {
      map.removeSource(sourceId);
    }

    // 清除立牌标记
    this.clearBoardMarkers(map);

    // 移除点击事件
    const handlerKey = `_${this.name}ClickHandler`;
    if (map[handlerKey]) {
      map.off('click', layerId, map[handlerKey]);
      delete map[handlerKey];
    }

    // 移除全局点击事件
    if (map._facilityWarnGlobalClickHandler) {
      map.off('click', map._facilityWarnGlobalClickHandler);
      delete map._facilityWarnGlobalClickHandler;
    }
  }
}

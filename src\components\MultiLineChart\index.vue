<template>
  <div class="multi-line-chart">
    <v-chart class="chart" :option="chartOption" autoresize />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
import VChart from "vue-echarts";
import { use } from "echarts/core";
import { CanvasRenderer } from "echarts/renderers";
import { LineChart } from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  MarkPointComponent,
  MarkLineComponent,
} from "echarts/components";

// 注册必要的组件
use([
  CanvasRenderer,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  MarkPointComponent,
  MarkLineComponent,
]);

const props = defineProps({
  // 图表数据
  data: {
    type: Object,
    default: () => ({
      categories: [], // x轴数据
      series: [], // 系列数据
    }),
  },
  // 选中的图例
  selectedLegends: {
    type: Array,
    default: () => [],
  },
  // 图表高度
  height: {
    type: String,
    default: "400px",
  },
  // 是否显示数据缩放
  showDataZoom: {
    type: Boolean,
    default: false,
  },
  // 是否平滑曲线
  smooth: {
    type: Boolean,
    default: true,
  },
  // Y轴单位
  yAxisUnit: {
    type: String,
    default: "万元",
  },
  // 完成比例单位
  completionUnit: {
    type: String,
    default: "%",
  },
});

const chartOption = computed(() => {
  // 过滤选中的系列数据
  const filteredSeries =
    props.data.series?.filter((series) => props.selectedLegends.includes(series.key)) || [];

  return {
    backgroundColor: "transparent",
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0, 18, 32, 0.5)",
      // borderColor: "#00d4aa",
      borderWidth: 0,
      textStyle: {
        color: "#ffffff",
        fontSize: 12,
        fontFamily: "Alibaba PuHuiTi",
      },
      formatter: function (params) {
        let result = `<div style="margin-bottom: 4px; font-weight: bold;">${params[0].axisValue}</div>`;
        params.forEach((param) => {
          const unit = param.seriesName.includes("完成总值")
            ? props.completionUnit
            : props.yAxisUnit;
          result += `<div style="display: flex; align-items: center; margin: 2px 0;">
            <span style="display: inline-block; width: 10px; height: 10px; background: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
            <span style="flex: 1;">${param.seriesName}：</span>
            <span style="font-weight: bold;">${param.value} ${unit}</span>
          </div>`;
        });
        return result;
      },
    },
    legend: {
      show: false, // 使用自定义图例
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: props.showDataZoom ? "15%" : "3%",
      top: "10%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: props.data.categories || [],
      axisLine: {
        lineStyle: {
          color: "rgba(255, 255, 255, 0.3)",
        },
      },
      axisLabel: {
        color: "rgba(255, 255, 255, 0.7)",
        fontSize: 11,
        fontFamily: "Alibaba PuHuiTi",
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: [
      {
        type: "value",
        name: `单位: ${props.yAxisUnit}`,
        nameTextStyle: {
          color: "rgba(255, 255, 255, 0.7)",
          fontSize: 11,
          fontFamily: "Alibaba PuHuiTi",
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: "rgba(255, 255, 255, 0.7)",
          fontSize: 11,
          fontFamily: "Alibaba PuHuiTi",
        },
        splitLine: {
          lineStyle: {
            color: "rgba(255, 255, 255, 0.1)",
            type: "dashed",
          },
        },
      },
      {
        type: "value",
        name: `完成比例`,
        nameTextStyle: {
          color: "rgba(255, 255, 255, 0.7)",
          fontSize: 11,
          fontFamily: "Alibaba PuHuiTi",
        },
        position: "right",
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: "rgba(255, 255, 255, 0.7)",
          fontSize: 11,
          fontFamily: "Alibaba PuHuiTi",
          formatter: "{value}%",
        },
        splitLine: {
          show: false,
        },
        max: 100,
      },
    ],
    dataZoom: props.showDataZoom
      ? [
          {
            type: "slider",
            show: true,
            xAxisIndex: [0],
            start: 0,
            end: 100,
            height: 20,
            bottom: 10,
            backgroundColor: "rgba(255, 255, 255, 0.1)",
            fillerColor: "rgba(0, 212, 170, 0.3)",
            borderColor: "rgba(255, 255, 255, 0.2)",
            handleStyle: {
              color: "#00d4aa",
            },
            textStyle: {
              color: "rgba(255, 255, 255, 0.7)",
            },
          },
        ]
      : [],
    series: filteredSeries.map((series) => ({
      name: series.name,
      type: "line",
      yAxisIndex: series.name.includes("完成总值") ? 1 : 0,
      data: series.data || [],
      smooth: props.smooth,
      symbol: "circle", // 节点样式为圆形
      symbolSize: 6, // 节点大小
      showSymbol: false, // 默认不显示节点，只在tooltip时显示
      lineStyle: {
        width: 3,
        color: series.color,
      },
      itemStyle: {
        color: series.color,
        borderColor: series.color,
        borderWidth: 2,
      },
      // 不使用区域阴影
      areaStyle: null,
      emphasis: {
        focus: "series",
        lineStyle: {
          width: 4,
        },
      },
    })),
  };
});
</script>

<style lang="scss" scoped>
.multi-line-chart {
  width: 100%;
  height: v-bind(height);

  .chart {
    width: 100%;
    height: 100%;
  }
}
</style>

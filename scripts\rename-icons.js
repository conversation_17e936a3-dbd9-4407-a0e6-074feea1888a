#!/usr/bin/env node
import fs from 'fs-extra';
import path from 'path';
import { globby } from 'globby';
import chalk from 'chalk';
// 修改 pinyin 导入方式
import pinyin from 'pinyin';

// 自动加载配置文件
import config from './rename.config.js';

// 核心重命名方法
async function startRename() {
  console.log(chalk.cyan('🚀 开始处理图标重命名...'));

  // 初始化输出目录
  if (config.outputDir) {
    await fs.ensureDir(config.outputDir);
  }

  // 扫描图标文件
  const files = await globby([`${config.inputDir}/**/*`]);

  for (const filePath of files) {
    const filename = path.basename(filePath);

    // 跳过排除项
    if (config.excludeReg.test(filename)) {
      console.log(chalk.gray(`⏩ 跳过排除文件: ${filename}`));
      continue;
    }

    // 中文文件名转换
    if (isContainChinese(filename)) {
      const newName = generateNewName(filename);
      const dirPath = path.dirname(filePath);
      const outputPath = config.outputDir 
        ? path.join(config.outputDir, newName) 
        : path.join(dirPath, newName);

      // 显示操作日志
      const actionType = config.outputDir ? '导出' : '重命名';
      console.log(chalk.yellow(`${actionType}: ${filename} → ${newName}`));

      // 实际执行文件操作
      if (!config.dryRun) {
        if (config.outputDir) {
          // 导出到指定目录
          await fs.copy(filePath, outputPath);
        } else {
          // 原地重命名
          await fs.rename(filePath, outputPath);
        }
      }
    }
  }
}

// 检测中文字符
function isContainChinese(text) {
  return /[\u4E00-\u9FFF]/.test(text);
}

// 生成规范名称
function generateNewName(originName) {
  const ext = path.extname(originName);
  const baseName = originName.replace(ext, '');

  // 修改 pinyin 调用，使用 style: pinyin.STYLE_NORMAL 确保没有音标
  const pyArray = pinyin(baseName, {
    style: pinyin.STYLE_NORMAL, // 使用普通风格，不带音标
    heteronym: false // 禁用多音字模式
  }).flat();

  let formatted;
  switch (config.nameStyle) {
    case 'camel':
      formatted = pyArray
        .map((word, i) => (i === 0 ? word.toLowerCase() : word[0].toUpperCase() + word.slice(1)))
        .join('');
      break;
    case 'pascal':
      formatted = pyArray.map(word => word[0].toUpperCase() + word.slice(1)).join('');
      break;
    case 'snake':
      formatted = pyArray.join('_');
      break;
    default: // kebab
      formatted = pyArray.join('-').toLowerCase();
  }

  // 哈希处理
  if (config.hashLength > 0) {
    const hash = Math.random()
      .toString(36)
      .slice(2, 2 + config.hashLength);
    formatted += `-${hash}`;
  }

  return `${formatted}${ext}`;
}

// 执行入口
startRename();

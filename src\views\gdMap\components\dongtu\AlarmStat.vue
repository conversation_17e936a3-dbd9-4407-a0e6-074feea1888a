<template>
  <div class="left-card">
    <m-card title="报警统计" :height="271">
      <template #extra>
        <img class="title-img" src="@/assets/images/dongtu/alarm-title.png" alt="" />
      </template>
      <div class="alarm-stat">
        <div
          class="item"
          v-for="(item, i) in listData"
          :key="i"
          :style="{ 'background-image': `url(${images[item.image]})` }"
        >
          <div class="number">
            <span class="count">{{ item.value }}</span>
            <span class="unit">{{ item.unit }}</span>
          </div>
          <div class="item-name">{{ item.name }}</div>
        </div>
      </div>
    </m-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import mCard from '@/components/mCard/index.vue';
import proStat1 from '@/assets/images/dongtu/project-stat-1.png';
import proStat2 from '@/assets/images/dongtu/project-stat-2.png';
import proStat3 from '@/assets/images/dongtu/project-stat-3.png';
import proStat4 from '@/assets/images/dongtu/project-stat-4.png';
import { getAlarmStat } from '@/views/gdMap/services/dongtu.mock.js';

const images = {
  proStat1,
  proStat2,
  proStat3,
  proStat4,
};
const listData = ref();
onMounted(async () => {
  const defaultData = await getAlarmStat();
  listData.value = defaultData.listData;
});
</script>

<style lang="scss" scoped>
.title-img {
  width: 14px;
  height: 14px;
  position: absolute;
  left: 118px;
  top: 13px;
}
.alarm-stat {
  color: #fff;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  height: 100%;
  padding: 40px 15px 0px;
  box-sizing: border-box;
  .item {
    width: 154px;
    height: 54px;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .number {
    font-size: 11px;
    padding-left: 66px;
    .count {
      font-size: 20px;
      margin-right: 4px;
      font-family: 'DDINPRO-Bold';
    }
    .unit {
      font-size: 11px;
      line-height: 14px;
      vertical-align: text-bottom;
    }
  }
  .item-name {
    font-size: 12px;
    line-height: 16px;
    padding-left: 66px;
  }
}
</style>

<template>
  <div class="left-card">
    <m-card title="平均通行速度" :height="165">
      <div class="average-speed">
        <div class="gauge-box">
          <v-chart ref="vChart" :option="option" :autoresize="true" />
        </div>
        <div class="gauge-box">
          <v-chart ref="vChart2" :option="option2" :autoresize="true" />
        </div>
      </div>
    </m-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import * as echarts from 'echarts';
import mCard from '@/components/mCard/index.vue';
import VChart from 'vue-echarts';
import { getAverageSpeed } from '@/views/gdMap/services/baotong.mock.js';

const option = ref({});
const option2 = ref({});

onMounted(async () => {
  const defaultData = await getAverageSpeed();
  const golmudSpeed = defaultData?.golmud || 0;
  const naquSpeed = defaultData?.naqu || 0;
  const golmudRate = (golmudSpeed - 20) / 80;
  const naquRate = (naquSpeed - 20) / 80;
  setOptions(golmudSpeed, golmudRate);
  setOptions2(naquSpeed, naquRate);
});

const setOptions = (speed, rate) => {
  option.value = {
    series: [
      {
        name: '外层圈',
        type: 'gauge',
        z: 2,
        radius: '100%',
        startAngle: 210,
        endAngle: -30,
        center: ['50%', '50%'], //整体的位置设置
        axisLine: {
          // 坐标轴线
          lineStyle: {
            color: [
              [
                1,
                new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'transparent' },
                  { offset: 0.21, color: 'rgba(232,232,232,0.25)' },
                  { offset: 0.49, color: 'rgba(224,224,224,0.6)' },
                  { offset: 0.74, color: 'rgba(222,222,222,0.25)' },
                  { offset: 1, color: 'transparent' },
                ]),
              ],
            ],
            width: 1,
          },
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        pointer: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        detail: {
          show: false,
        },
      },
      {
        name: '内层圈',
        type: 'gauge',
        z: 2,
        min: 20,
        max: 100,
        splitNumber: 4,
        radius: '80%',
        startAngle: 180,
        endAngle: 0,
        center: ['50%', '50%'],
        axisLine: {
          lineStyle: {
            color: [[1, '#E5E5E5']],
            width: 1,
          },
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          show: true,
          color: '#fff',
          distance: -25,
          rotate: 'tangential',
          fontSize: 9,
        },
        pointer: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        detail: {
          show: false,
        },
      },
      {
        name: '刻度',
        type: 'gauge',
        radius: '80%',
        startAngle: 180,
        endAngle: 0,
        splitNumber: 4,
        z: 10,
        min: 20,
        max: 100,
        progress: {
          show: false,
          width: 0,
        },
        axisLine: {
          show: false,
          lineStyle: {
            width: 0,
            color: [
              [
                rate, // 由数据值的大小决定
                new echarts.graphic.LinearGradient(0, 0, 1, 1, [
                  {
                    offset: 0,
                    color: 'rgba(35, 226, 255, 1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(7, 255, 156, 1)',
                  },
                ]),
              ],
              [1, 'rgba(255, 255, 255, 0.3)'],
            ],
            opacity: 0,
          },
        },
        axisTick: {
          length: 9,
          splitNumber: 10,
          lineStyle: {
            width: 2,
            color: 'auto',
          },
          distance: 4,
        },
        anchor: {
          show: true,
          showAbove: true,
          size: 8,
          itemStyle: {
            color: '#fff',
            borderWidth: 1,
            borderColor: '#0AFCA6',
          },
        },
        pointer: {
          length: '90%',
          width: 2,
          itemStyle: {
            color: '#fff',
          },
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        title: {
          show: true,
          fontSize: 12,
          color: '#fff',
        },
        detail: {
          valueAnimation: true,
          fontSize: 20,
          offsetCenter: [0, '50%'],
          color: '#0AFCA6',
          fontFamily: 'DDINPRO-Medium',
        },
        data: [
          {
            value: speed,
            name: '格尔木方向',
            title: {
              offsetCenter: ['0%', '100%'],
            },
          },
        ],
      },
      {
        name: '中心灰色圆',
        type: 'gauge',
        radius: '50%',
        z: 4,
        startAngle: 180,
        endAngle: 0,
        axisLine: {
          lineStyle: {
            color: [
              [
                1,
                new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 0,
                    color: 'rgba(76, 128, 165, 1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(231, 245, 255, 1)',
                  },
                ]),
              ],
            ],
            width: 1,
          },
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        detail: {
          show: false,
        },
        pointer: {
          show: false,
        },
        progress: {
          show: true,
          width: 11,
          itemStyle: {
            color: {
              type: 'radial',
              x: 0.5,
              y: 0.5,
              r: 36,
              colorStops: [
                {
                  offset: 1,
                  color: 'rgba(76, 128, 165, 0.4)',
                },
              ],
            },
          },
        },
        data: [
          {
            value: 100,
          },
        ],
      },
      {
        name: '中心绿色圆',
        type: 'gauge',
        radius: '32%',
        z: 5,
        startAngle: 180,
        endAngle: 0,
        axisLine: {
          lineStyle: {
            color: [
              [
                1,
                new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 0,
                    color: 'rgba(76, 128, 165, 1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(231, 245, 255, 1)',
                  },
                ]),
              ],
            ],
            width: 1,
          },
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        detail: {
          show: false,
        },
        pointer: {
          show: false,
        },
        progress: {
          show: true,
          width: 18,
          itemStyle: {
            color: {
              type: 'radial',
              x: 0.5,
              y: 0.5,
              r: 18,
              colorStops: [
                {
                  offset: 1,
                  color: 'rgba(87,210,171,0.4)',
                },
              ],
            },
          },
        },
        data: [
          {
            value: 100,
          },
        ],
      },
    ],
  };
};

const setOptions2 = (speed, rate) => {
  option2.value = {
    series: [
      {
        name: '外层圈',
        type: 'gauge',
        z: 2,
        radius: '100%',
        startAngle: 210,
        endAngle: -30,
        center: ['50%', '50%'], //整体的位置设置
        axisLine: {
          // 坐标轴线
          lineStyle: {
            color: [
              [
                1,
                new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'transparent' },
                  { offset: 0.21, color: 'rgba(232,232,232,0.25)' },
                  { offset: 0.49, color: 'rgba(224,224,224,0.6)' },
                  { offset: 0.74, color: 'rgba(222,222,222,0.25)' },
                  { offset: 1, color: 'transparent' },
                ]),
              ],
            ],
            width: 1,
          },
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        pointer: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        detail: {
          show: false,
        },
      },
      {
        name: '内层圈',
        type: 'gauge',
        z: 2,
        min: 20,
        max: 100,
        splitNumber: 4,
        radius: '80%',
        startAngle: 180,
        endAngle: 0,
        center: ['50%', '50%'],
        axisLine: {
          lineStyle: {
            color: [[1, '#E5E5E5']],
            width: 1,
          },
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          show: true,
          color: '#fff',
          distance: -25,
          rotate: 'tangential',
          fontSize: 9,
        },
        pointer: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        detail: {
          show: false,
        },
      },
      {
        name: '刻度',
        type: 'gauge',
        radius: '80%',
        startAngle: 180,
        endAngle: 0,
        splitNumber: 4,
        z: 10,
        min: 20,
        max: 100,
        progress: {
          show: false,
          width: 0,
        },
        axisLine: {
          show: false,
          lineStyle: {
            width: 0,
            color: [
              [
                rate, // 由数据值的大小决定
                new echarts.graphic.LinearGradient(0, 0, 1, 1, [
                  {
                    offset: 0,
                    color: 'rgba(35, 105, 255, 1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(15, 211, 255, 1)',
                  },
                ]),
              ],
              [1, 'rgba(255, 255, 255, 0.3)'],
            ],
            opacity: 0,
          },
        },
        axisTick: {
          length: 9,
          splitNumber: 10,
          lineStyle: {
            width: 2,
            color: 'auto',
          },
          distance: 4,
        },
        anchor: {
          show: true,
          showAbove: true,
          size: 8,
          itemStyle: {
            color: '#fff',
            borderWidth: 1,
            borderColor: '#178FE5',
          },
        },
        pointer: {
          length: '90%',
          width: 2,
          itemStyle: {
            color: '#fff',
          },
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        title: {
          show: true,
          fontSize: 12,
          color: '#fff',
        },
        detail: {
          valueAnimation: true,
          fontSize: 20,
          offsetCenter: [0, '50%'],
          color: '#18A7FF',
          fontFamily: 'DDINPRO-Medium',
        },
        data: [
          {
            value: speed,
            name: '那曲方向',
            title: {
              offsetCenter: ['0%', '100%'],
            },
          },
        ],
      },
      {
        name: '中心灰色圆',
        type: 'gauge',
        radius: '50%',
        z: 4,
        startAngle: 180,
        endAngle: 0,
        axisLine: {
          lineStyle: {
            color: [
              [
                1,
                new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 0,
                    color: 'rgba(76, 128, 165, 1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(231, 245, 255, 1)',
                  },
                ]),
              ],
            ],
            width: 1,
          },
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        detail: {
          show: false,
        },
        pointer: {
          show: false,
        },
        progress: {
          show: true,
          width: 11,
          itemStyle: {
            color: {
              type: 'radial',
              x: 0.5,
              y: 0.5,
              r: 36,
              colorStops: [
                {
                  offset: 1,
                  color: 'rgba(76, 128, 165, 0.4)',
                },
              ],
            },
          },
        },
        data: [
          {
            value: 100,
          },
        ],
      },
      {
        name: '中心蓝色圆',
        type: 'gauge',
        radius: '32%',
        z: 5,
        startAngle: 180,
        endAngle: 0,
        axisLine: {
          lineStyle: {
            color: [
              [
                1,
                new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 0,
                    color: 'rgba(76, 128, 165, 1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(231, 245, 255, 1)',
                  },
                ]),
              ],
            ],
            width: 1,
          },
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        detail: {
          show: false,
        },
        pointer: {
          show: false,
        },
        progress: {
          show: true,
          width: 18,
          itemStyle: {
            color: {
              type: 'radial',
              x: 0.5,
              y: 0.5,
              r: 18,
              colorStops: [
                {
                  offset: 1,
                  color: 'rgba(36, 112, 224, 0.4)',
                },
              ],
            },
          },
        },
        data: [
          {
            value: 100,
          },
        ],
      },
    ],
  };
};
</script>

<style lang="scss" scoped>
.average-speed {
  color: #fff;
  display: flex;
  align-items: center;
  height: 100%;
  .gauge-box {
    width: 50%;
    height: 100%;
  }
  .gauge-container {
    height: 100%;
    width: 100%;
  }
  .title {
    font-size: 12px;
    line-height: 14px;
    margin-bottom: 10px;
    text-align: center;
  }
}
</style>

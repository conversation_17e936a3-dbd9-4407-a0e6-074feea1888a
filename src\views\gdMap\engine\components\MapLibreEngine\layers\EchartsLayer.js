import maplibregl from "maplibre-gl";
import * as echarts from "echarts";
import "echarts-gl";

export class EchartsLayer {
	constructor() {
		this.id = "echarts-layer";
		this.type = "custom";
		this.renderingMode = "2d";
		this.chart = null;
	}

	onAdd(map, gl) {
		const canvas = map.getCanvas();
		this.chart = echarts.init(canvas);

		map.on("move", this.update.bind(this));
	}

	update() {
		const center = [];
		// 根据地图状态更新图表
		const option = {
			mapbox: {
				style: "https://demotiles.maplibre.org/style.json",
				center: [116.4, 39.9],
				zoom: 11,
				pitch: 50,
			},
			series: [
				{
					type: "lines3D",
					coordinateSystem: "mapbox",
					effect: {
						show: true,
						trailWidth: 2, // 光效宽度
						trailLength: 0.2, // 光效长度
						trailOpacity: 0.8, // 透明度
						trailColor: "#ffa022", // 光效颜色
						constantSpeed: 20, // 动画速度
					},
					blendMode: "lighter", // 混合模式增强光效
					lineStyle: {
						width: 1, // 实际线条宽度
						color: "#ff7e00", // 线条颜色
					},
					data: linesData.map((item) => ({
						coords: item.coords,
						lineStyle: {
							// 可以根据属性设置不同样式
							color: item.properties?.type === "highway" ? "#ff4500" : "#ff7e00",
						},
					})),
				},
			],
			// 图表配置
		};
		this.chart.setOption(option);
	}

	render(gl, matrix) {
		this.chart.resize();
		this.update();
	}
}

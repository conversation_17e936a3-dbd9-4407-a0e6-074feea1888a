<template>
	<div class="weather-widget">
		<div class="weather-icon">
			<img :src="weatherIcon" alt="天气图标" />
		</div>
		<div class="weather-info">
			<div class="weather-desc">{{ weatherDesc }}</div>
			<div class="temperature">{{ temperature }}°C</div>
			<!-- <div class="city-name">{{ cityName }}</div> -->
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import {
	WEATHER_API_KEY,
	WEATHER_API,
	WEATHER_ICON_MAP,
	DEFAULT_CITY,
	DEFAULT_CITY_ID,
} from "./config.ts";
import CloudyIcon from "@/assets/modules/common/icons/cloudy.svg";

const props = defineProps({
	city: {
		type: String,
		default: "",
	},
});

const temperature = ref("--");
const weatherDesc = ref("晴");
const weatherIcon = ref(CloudyIcon);
const cityName = ref(props.city || DEFAULT_CITY);

// 获取当前位置
const getCurrentPosition = () => {
	return new Promise((resolve, reject) => {
		if (!navigator.geolocation) {
			reject(new Error("浏览器不支持地理位置"));
			return;
		}

		navigator.geolocation.getCurrentPosition(
			(position) => {
				const { latitude, longitude } = position.coords;
				resolve({ latitude, longitude });
			},
			(error) => {
				console.warn("获取位置失败:", error.message);
				reject(error);
			},
			{ timeout: 10000 }
		);
	});
};

// 通过IP获取城市
const getCityByIP = async () => {
	try {
		const response = await fetch(
			`${WEATHER_API.CITY_LOOKUP}?location=auto_ip&key=${WEATHER_API_KEY}`
		);
		const data = await response.json();

		console.log("getCityByIP", data);

		if (data.code === "200" && data.location && data.location.length > 0) {
			return data.location[0];
		} else {
			throw new Error("IP定位失败");
		}
	} catch (error) {
		console.error("IP定位失败:", error);
		return null;
	}
};

// 通过经纬度获取城市
const getCityByLocation = async (latitude, longitude) => {
	try {
		const response = await fetch(
			`${WEATHER_API.CITY_LOOKUP}?location=${longitude},${latitude}&key=${WEATHER_API_KEY}`
		);
		const data = await response.json();
		console.log("getCityByLocation", data);
		if (data.code === "200" && data.location && data.location.length > 0) {
			return data.location[0];
		} else {
			throw new Error("经纬度定位失败");
		}
	} catch (error) {
		console.error("经纬度定位失败:", error);
		return null;
	}
};

// 获取天气信息
const getWeather = async (locationId) => {
	// 本地开发环境使用模拟数据
	// if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
	//   console.log('本地开发环境，使用模拟天气数据');
	//   // 模拟数据
	//   temperature.value = '14';
	//   weatherDesc.value = '晴';
	//   weatherIcon.value = WEATHER_ICON_MAP['101'];
	//   return {
	//     temp: '14°C',
	//     text: '多云',
	//     icon: '101',
	//   };
	// }

	try {
		const response = await fetch(
			`${WEATHER_API.REAL_TIME}?location=${locationId}&key=${WEATHER_API_KEY}&lang=zh&unit=m`
		);
		const data = await response.json();

		if (data.code === "200" && data.now) {
			temperature.value = data.now.temp;
			weatherDesc.value = data.now.text;

			// 使用图标映射表获取对应的本地图标
			weatherIcon.value = WEATHER_ICON_MAP[data.now.icon] || "/assets/images/icons/weather/101.png";

			return data.now;
		} else {
			throw new Error("获取天气失败");
		}
	} catch (error) {
		console.error("获取天气失败:", error);
		return null;
	}
};

// 初始化天气数据
const initWeather = async () => {
	try {
		// let cityData;

		// // 如果有指定城市，直接使用
		// if (props.city) {
		//   // 查询指定城市
		//   const response = await fetch(
		//     `${WEATHER_API.CITY_LOOKUP}?location=${props.city}&key=${WEATHER_API_KEY}`
		//   );
		//   const data = await response.json();

		//   if (data.code === '200' && data.location && data.location.length > 0) {
		//     cityData = data.location[0];
		//   }
		// } else {
		//   // 尝试使用浏览器定位
		//   try {
		//     const { latitude, longitude } = await getCurrentPosition();
		//     cityData = await getCityByLocation(latitude, longitude);
		//   } catch (error) {
		//     // 浏览器定位失败，尝试IP定位
		//     cityData = await getCityByIP();
		//   }
		// }

		// // 如果获取到城市信息，更新城市名称并获取天气
		// if (cityData) {
		//   cityName.value = cityData.name;
		//   await getWeather(cityData.id);
		// }

		await getWeather(DEFAULT_CITY_ID);
	} catch (error) {
		console.error("初始化天气失败:", error);
	}
};

onMounted(async () => {
	// 获取天气信息
	initWeather();

	// 每30分钟更新一次天气
	setInterval(initWeather, 30 * 60 * 1000);
});
</script>

<style scoped lang="scss">
.weather-widget {
	display: flex;
	align-items: center;
	cursor: pointer;
	.weather-icon {
		width: 36px;
		height: 36px;

		img {
			width: 100%;
			height: 100%;
		}
	}

	.weather-info {
		display: flex;
		align-items: center;
		color: var(--header-right-text-color);
		.temperature {
			margin-left: 8px;
			font-family: D-DIN-PRO;
			font-size: 14px;
		}

		.weather-desc {
			margin-left: 8px;
			font-family: Alibaba PuHuiTi;
			font-size: 12px;
		}

		// .city-name {
		// }
	}
}
</style>

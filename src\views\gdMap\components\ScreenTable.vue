<template>
  <div class="screen-table">
    <!-- 表头 -->
    <div class="screen-table-header">
      <div
        v-for="(column, index) in columns"
        :key="index"
        class="screen-table-header-col"
        :style="getColumnStyle(column)"
      >
        {{ column.title }}
      </div>
    </div>

    <!-- 数据列表区域（插槽） -->
    <div class="screen-table-body">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";

const props = defineProps({
  // 列配置
  columns: {
    type: Array,
    default: () => [],
    // 列配置格式：[{ title: '列标题', width: '宽度值', flex: 数值 }]
    // width: 固定宽度，如 '100px'
    // flex: flex 比例，不设置width时生效
  },
  // 表格高度
  height: {
    type: String,
    default: null,
  },
  // 表格背景色
  headerBgColor: {
    type: String,
    default: "#105297",
  },
  //
  tableBgColor: {
    type: String,
    default: "#091e3f",
  },
});

// 计算列样式
const getColumnStyle = (column) => {
  const style = {};

  if (column.width) {
    // 如果设置了宽度，使用固定宽度
    style.width = column.width;
    style.flex = "none";
  } else if (column.flex) {
    // 如果设置了flex比例，使用flex布局
    style.flex = column.flex;
  } else {
    // 默认flex为1
    style.flex = "1";
  }

  // 设置文本对齐方向，默认为left
  style.textAlign = column.align || "left";

  return style;
};
</script>

<style lang="scss" scoped>
.screen-table {
  padding: 6px;
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: v-bind(tableBgColor);

  box-sizing: border-box;
  &-header {
    display: flex;
    height: 28px;
    line-height: 28px;
    background-color: v-bind(headerBgColor);

    &-col {
      padding: 0 6px;
      font-size: 12px;
      font-family: Source Han Sans CN-Bold;
      color: #fff;
      box-sizing: border-box;
    }
  }

  &-body {
    flex: 1;
    overflow-y: auto;
  }
}
</style>

<template>
	<BasicCard title="党组织分析">
		<div class="organization-analysis-card">
			<div class="card-content">
				<div class="organization-tabs">
					<template v-for="tab in tabList" :key="tab.value">
						<div
							class="organization-tabs-item"
							:class="{ 'is-active': tab.value === activeTab }"
							@click="onClickTab(tab)"
						>
							{{ tab.label }}
						</div>
					</template>
				</div>
				<div class="card-content-main">
					<div class="organization-num">
						<div v-for="item in data" :key="item" class="organization-num-item">
							<div class="organization-num-item-value">{{ item.count || 0 }}</div>
							<div class="organization-num-item-label">{{ item.name || "" }}</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<template #extra>
			<div class="link" @click="onInfo">查看详情 <icon-double-right /></div>
		</template>
	</BasicCard>

	<OrganizationAnalysisModal ref="OrganizationAnalysisModalRef" />
</template>

<script setup>
import { usePartyBuildingStore } from "@/store";
import request from "@/utils/request";
import OrganizationAnalysisModal from "./components/OrganizationAnalysisModal.vue";

const { proxy } = getCurrentInstance();
const PartyBuildingStore = usePartyBuildingStore();

const tabList = [
	{ label: "全系统", value: "全系统" },
	{ label: "党委", value: "党委" },
	{ label: "党总支", value: "党总支" },
	{ label: "党支部", value: "党支部" },
];

const activeTab = ref("全系统");
const data = ref([]);

watch(
	() => PartyBuildingStore.getDepartment,
	() => getData()
);

onMounted(() => {
	getData();
});

const onClickTab = (tab) => {
	activeTab.value = tab.value;
	getData();
};

const onInfo = () => {
	proxy.$refs.OrganizationAnalysisModalRef.open();
};

const getData = () => {
	const department = PartyBuildingStore.getDepartment;
	const deptType = unref(activeTab);
	request.get("/api/screen/dangjian/stat/party", { department, deptType }).then((res) => {
		if (res.code == 200) {
			data.value = res.data;
		}
	});
};
</script>

<style lang="scss" scoped>
.organization-analysis-card {
	height: 188px;
	width: 100%;
}

.card-content {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
	overflow: hidden;
	&-main {
		flex: 1;
		overflow: hidden;
	}
}

.organization-tabs {
	display: flex;
	flex-direction: row;
	justify-content: end;
	column-gap: 8px;
	padding-right: 12px;

	&-item {
		font-family: Alibaba PuHuiTi;
		font-size: 14px;
		line-height: 21px;
		color: #f8cac5;
		text-align: center;
		padding: 2px 3px 1px 3px;
		background: linear-gradient(0deg, rgba(200, 54, 35, 0.15) 0%, rgba(200, 54, 35, 0) 100%);
		position: relative;
		cursor: pointer;

		&::after {
			content: "";
			display: block;
			position: absolute;
			left: 0;
			bottom: -2px;
			width: 100%;
			height: 2px;
			background: #ff3e0d;
		}

		&.is-active {
			color: #ffffff;
			background: linear-gradient(0deg, rgba(250, 236, 1, 0.35) 0%, rgba(250, 236, 1, 0) 100%);
			&::after {
				background: #edcb04;
			}
		}
	}
}

.organization-num {
	height: 100%;
	display: flex;
	flex-direction: row;
	&-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: start;
		background-image: url("@/assets/modules/PartyBuilding/icon/organization-num.svg");
		background-size: auto auto;
		background-repeat: no-repeat;
		background-position: center bottom 16px;

		&-value {
			font-family: D-DIN-PRO;
			font-weight: bold;
			font-size: 36px;
			letter-spacing: 2px;
			padding: 24px 0;
			background: var(--card-number-text-color);
			background-clip: text;
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}

		&-label {
			font-family: Alibaba PuHuiTi;
			font-size: 16px;
			color: #ffffff;
		}
	}
}

.link {
	font-family: Alibaba PuHuiTi;
	font-size: 14px;
	color: #ffffff;
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-top: auto;
	margin-bottom: 13px;
	cursor: pointer;
}
</style>

<template>
  <Modal ref="modalRef" title="明细台账" :icon="modelIconTitle">
    <div class="detail-modal-content">
      <!-- 筛选区域 -->
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-item">
            <div class="label">项目分类:</div>
            <a-select v-model:value="selectedProjectType" placeholder="请选择" allow-clear>
              <a-option value="全部">全部</a-option>
              <a-option value="格贡一">格贡一</a-option>
              <a-option value="格贡二">格贡二</a-option>
              <a-option value="格贡三">格贡三</a-option>
              <a-option value="格贡四">格贡四</a-option>
            </a-select>
          </div>
          <div class="filter-item">
            <div class="label">日期:</div>
            <a-date-picker v-model:value="selectedDate" placeholder="请选择" />
          </div>
          <a-button class="search" @click="handleSearch">搜索</a-button>
          <a-button class="reset" @click="handleReset">重置</a-button>
        </div>
      </div>

      <!-- 表格区域 -->
      <div class="table-container">
        <div class="table-section">
          <a-table
            :columns="columns"
            :data="paginatedData"
            :pagination="false"
            :scroll="{ x: 1200 }"
            row-key="id"
            size="small"
          >
            <template #index="{ rowIndex }">
              {{ (currentPage - 1) * pageSize + rowIndex + 1 }}
            </template>
            <template #totalValue="{ record }">
              {{ formatNumber(record.totalValue) }}
            </template>
            <template #todayValue="{ record }">
              {{ formatNumber(record.todayValue) }}
            </template>
            <template #monthValue="{ record }">
              {{ formatNumber(record.monthValue) }}
            </template>
            <template #cumulativeCompletion="{ record }">
              {{ record.cumulativeCompletion }}%
            </template>
            <template #action="{ record }">
              <div class="active-button" @click="handleViewDetail(record)">查看详情</div>
            </template>
          </a-table>
        </div>

        <!-- 分页组件 -->
        <div class="pagination-container">
          <div class="pagination-info">
            共<span class="pagination-number">{{ totalPages }}</span
            >页/<span class="pagination-number">{{ filteredTableData.length }}</span
            >条数据
          </div>
          <div class="pagination">
            <div class="pagination-info">
              共<span class="pagination-number">{{ totalPages }}</span
              >页
            </div>
            <button
              class="pagination-btn prev"
              :disabled="currentPage === 1"
              @click="handlePageChange(currentPage - 1)"
            >
              <IconLeft />
            </button>
            <button
              v-for="page in visiblePages"
              :key="page"
              class="pagination-btn"
              :class="{
                active: page === currentPage,
                ellipsis: page === '...',
              }"
              :disabled="page === '...'"
              @click="page !== '...' && handlePageChange(page)"
            >
              {{ page }}
            </button>
            <button
              class="pagination-btn next"
              :disabled="currentPage === totalPages"
              @click="handlePageChange(currentPage + 1)"
            >
              <IconRight />
            </button>
          </div>
        </div>
      </div>
    </div>
  </Modal>
</template>

<script setup>
import modelIconTitle from "@/assets/modules/traffic/icon/model-icon-title.svg?component";
import Modal from "@/views/gdMap/components/traffic/components/Modal.vue";
import { IconLeft, IconRight } from "@arco-design/web-vue/es/icon";

const modalRef = ref(null);

// 筛选条件
const selectedProjectType = ref("");
const selectedDate = ref("");

// 分页数据
const currentPage = ref(1);
const pageSize = ref(10);

// 表格列配置
const columns = [
  {
    title: "序号",
    slotName: "index",
    width: 60,
    align: "center",
  },
  {
    title: "分部名称",
    dataIndex: "departmentName",
    width: 120,
  },
  {
    title: "日报日期",
    dataIndex: "reportDate",
    width: 100,
  },
  {
    title: "总产值(万)",
    slotName: "totalValue",
    width: 120,
    align: "right",
  },
  {
    title: "今日完成产值(万)",
    slotName: "todayValue",
    width: 140,
    align: "right",
  },
  {
    title: "本月完成产值(万)",
    slotName: "monthValue",
    width: 140,
    align: "right",
  },
  {
    title: "累计完成",
    slotName: "cumulativeCompletion",
    width: 100,
    align: "center",
  },
  {
    title: "日报明细",
    slotName: "action",
    width: 100,
    align: "center",
  },
];

// 表格数据
const tableData = ref([
  {
    id: 1,
    departmentName: "全部",
    reportDate: "2025-06-19",
    totalValue: 999999999,
    todayValue: 999999999,
    monthValue: 999999999,
    cumulativeCompletion: 99.99,
  },
  {
    id: 2,
    departmentName: "格贡一",
    reportDate: "2025-06-19",
    totalValue: 999999999,
    todayValue: 999999999,
    monthValue: 999999999,
    cumulativeCompletion: 99.99,
  },
  {
    id: 3,
    departmentName: "试验段",
    reportDate: "2025-06-19",
    totalValue: 999999999,
    todayValue: 999999999,
    monthValue: 999999999,
    cumulativeCompletion: 99.99,
  },
  {
    id: 4,
    departmentName: "格贡三",
    reportDate: "2025-06-19",
    totalValue: 999999999,
    todayValue: 999999999,
    monthValue: 999999999,
    cumulativeCompletion: 99.99,
  },
  {
    id: 5,
    departmentName: "格贡四",
    reportDate: "2025-06-19",
    totalValue: 999999999,
    todayValue: 999999999,
    monthValue: 999999999,
    cumulativeCompletion: 99.99,
  },
  {
    id: 6,
    departmentName: "贡那一",
    reportDate: "2025-06-19",
    totalValue: 999999999,
    todayValue: 999999999,
    monthValue: 999999999,
    cumulativeCompletion: 99.99,
  },
  {
    id: 7,
    departmentName: "贡那二",
    reportDate: "2025-06-19",
    totalValue: 999999999,
    todayValue: 999999999,
    monthValue: 999999999,
    cumulativeCompletion: 99.99,
  },
  {
    id: 8,
    departmentName: "贡那三",
    reportDate: "2025-06-19",
    totalValue: 999999999,
    todayValue: 999999999,
    monthValue: 999999999,
    cumulativeCompletion: 99.99,
  },
  {
    id: 9,
    departmentName: "贡那四",
    reportDate: "2025-06-19",
    totalValue: 999999999,
    todayValue: 999999999,
    monthValue: 999999999,
    cumulativeCompletion: 99.99,
  },
  {
    id: 10,
    departmentName: "贡那五",
    reportDate: "2025-06-19",
    totalValue: 999999999,
    todayValue: 999999999,
    monthValue: 999999999,
    cumulativeCompletion: 99.99,
  },
]);

// 过滤后的表格数据
const filteredTableData = computed(() => {
  return tableData.value.filter((item) => {
    const typeMatch =
      !selectedProjectType.value ||
      selectedProjectType.value === "全部" ||
      item.departmentName === selectedProjectType.value;
    const dateMatch = !selectedDate.value || item.reportDate === selectedDate.value;
    return typeMatch && dateMatch;
  });
});

// 分页后的数据
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredTableData.value.slice(start, end);
});

// 总页数
const totalPages = computed(() => {
  return Math.ceil(filteredTableData.value.length / pageSize.value);
});

// 可见页码计算
const visiblePages = computed(() => {
  const total = totalPages.value;
  const current = currentPage.value;
  const pages = [];

  if (total <= 7) {
    // 如果总页数小于等于7，显示所有页码
    for (let i = 1; i <= total; i++) {
      pages.push(i);
    }
  } else {
    // 总页数大于7时的逻辑
    if (current <= 4) {
      // 当前页在前4页
      for (let i = 1; i <= 5; i++) {
        pages.push(i);
      }
      pages.push("...");
      pages.push(total);
    } else if (current >= total - 3) {
      // 当前页在后4页
      pages.push(1);
      pages.push("...");
      for (let i = total - 4; i <= total; i++) {
        pages.push(i);
      }
    } else {
      // 当前页在中间
      pages.push(1);
      pages.push("...");
      for (let i = current - 1; i <= current + 1; i++) {
        pages.push(i);
      }
      pages.push("...");
      pages.push(total);
    }
  }

  return pages;
});

// 格式化数字
const formatNumber = (value) => {
  if (value === null || value === undefined) return "-";
  return value.toLocaleString();
};

// 事件处理方法
const handleSearch = () => {
  currentPage.value = 1;
  // 这里可以添加搜索逻辑
};

const handleReset = () => {
  selectedProjectType.value = "";
  selectedDate.value = "";
  currentPage.value = 1;
};

const handlePageChange = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
  }
};

const handleViewDetail = (record) => {
  console.log("查看详情:", record);
  // 这里可以打开详情弹窗或跳转到详情页面
};

// 打开弹窗
const open = () => {
  modalRef.value?.open();
};

defineExpose({
  open,
});
</script>

<style lang="scss" scoped>
/* 明细台账弹窗样式 */
.detail-modal-content {
  /* padding: 20px; */
  /* background: #001220; */
  min-height: 200px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 筛选区域样式 */
.filter-section {
  /* padding: 16px 20px; */
  margin-bottom: 20px;
  flex-shrink: 0; // 筛选区域不收缩
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 24px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.label {
  font-size: 14px;
  color: #c4e5ff;
  font-family: Alibaba PuHuiTi;
  white-space: nowrap;
}

/* Arco Design 组件样式覆盖 */
:deep(.arco-select) {
  min-width: 120px;

  .arco-select-view-single {
    background: rgba(0, 0, 0, 0.3) !important;
    border: 1px solid rgba(0, 138, 255, 0.3) !important;
    color: #c4e5ff !important;

    &:hover {
      border-color: #008aff !important;
    }
  }

  .arco-select-view-value {
    color: #c4e5ff !important;
  }

  .arco-select-placeholder {
    color: rgba(196, 229, 255, 0.6) !important;
  }
}
:deep(.arco-select-view-single) {
  background-color: #001120;
}
:deep(.arco-picker) {
  background: #001120 !important;
  border: 1px solid rgba(0, 138, 255, 0.3) !important;

  &:hover {
    border-color: #008aff !important;
  }

  .arco-picker-input {
    color: #c4e5ff !important;

    &::placeholder {
      color: rgba(196, 229, 255, 0.6) !important;
    }
  }
}

/* 按钮样式 - 与大事记保持一致 */
:deep(.arco-btn) {
  width: 40px;
  height: 32px;
  box-sizing: border-box;
  border: 1px solid #10477b;
  border-radius: 4px;
  background-color: #001120;
  font-family: Alibaba PuHuiTi;
  font-size: 12px;
  color: #c4e5ff;

  &.search {
    background: linear-gradient(180deg, rgba(0, 138, 255, 0) 47%, #008aff 100%),
      radial-gradient(0% 87% at -13% 112%, #64c6ff 0%, rgba(8, 62, 115, 0) 100%),
      rgba(0, 138, 255, 0.3);
    box-shadow: inset 0px 0px 8px 1px rgba(0, 138, 255, 0.25);
    border-image: linear-gradient(
        90deg,
        rgba(0, 138, 255, 0),
        rgba(0, 138, 255, 1),
        rgba(0, 138, 255, 0.2),
        rgba(0, 138, 255, 0)
      )
      1 1;
    color: white;
  }

  &.reset {
    &:hover {
      background-color: rgba(16, 71, 123, 0.3);
      border-color: #10477b;
    }
  }
}

/* 表格容器样式 */
.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; // 确保flex子项能正确收缩
}

/* 表格区域样式 */
.table-section {
  flex: 1;
  min-height: 0; // 确保表格能正确收缩
  /* background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  border: 1px solid rgba(0, 138, 255, 0.2);
  padding: 20px; */
  .active-button {
    font-family: Alibaba PuHuiTi;
    font-weight: normal;
    font-size: 16px;
    color: #21c0f5;
    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
    font-style: normal;
    text-decoration-line: underline;
    text-transform: none;
    cursor: pointer;
  }
}

/* Arco Design Table 样式覆盖 */
:deep(.arco-table) {
  background: transparent;
  font-family: Alibaba PuHuiTi;

  .arco-table-container {
    border: none !important;
  }

  .arco-table-thead th {
    background: rgba(0, 138, 255, 0.1) !important;
    color: #c4e5ff !important;
    border-bottom: 1px solid rgba(0, 138, 255, 0.2) !important;
    font-weight: bold;
    font-size: 12px;
    padding: 12px 8px;
  }

  .arco-table-tbody td {
    background: transparent !important;
    color: rgba(196, 229, 255, 0.9) !important;
    border-bottom: 1px solid rgba(0, 138, 255, 0.1) !important;
    font-size: 12px;
    padding: 12px 8px;
  }

  .arco-table-tbody .arco-table-tr:hover td {
    background: rgba(0, 138, 255, 0.05) !important;
  }

  .arco-table-empty {
    color: rgba(196, 229, 255, 0.6);
  }
  .arco-table-th {
    font-family: Alibaba PuHuiTi;
    font-weight: normal;
    font-size: 20px;
    color: #ffffff;
    background: linear-gradient(180deg, rgba(0, 138, 255, 0) 47%, #008aff 100%),
      rgba(0, 138, 255, 0.3);
    /* box-shadow: inset 0px 0px 8px 1px rgba(0, 138, 255, 0.25); */
    border-image: linear-gradient(
        90deg,
        rgba(0, 138, 255, 0),
        rgba(0, 138, 255, 1),
        rgba(0, 138, 255, 0.2),
        rgba(0, 138, 255, 0)
      )
      1 1;
  }
  .arco-table-td {
    font-family: Alibaba PuHuiTi;
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 138, 255, 0.4);
    font-weight: normal;
    font-size: 16px;
    color: #ffffff;
    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
    font-style: normal;
    text-transform: none;
  }
  .arco-table-border:not(.arco-table-border-cell) .arco-table-container {
    border: none;
  }
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: rgba(30, 60, 100, 0.05);
  border-top: 1px solid rgba(0, 138, 255, 0.2);
  font-family: Alibaba PuHuiTi;
  flex-shrink: 0; // 分页区域不收缩，固定在底部
}

.pagination-info {
  font-size: 14px;
  color: #8a9ba8;

  .pagination-number {
    color: #008aff;
    font-weight: 500;
  }
}

.pagination {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-btn {
  min-width: 32px;
  height: 32px;
  padding: 0 8px;
  border: 1px solid rgba(0, 138, 255, 0.3);
  background: transparent;
  color: #8a9ba8;
  font-size: 14px;
  font-family: Alibaba PuHuiTi;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover:not(:disabled):not(.ellipsis) {
    border-color: #008aff;
    color: #c4e5ff;
    background: rgba(0, 138, 255, 0.1);
  }

  &.active {
    border-color: #008aff;
    background: linear-gradient(180deg, rgba(0, 138, 255, 0) 0%, #008aff 100%);
    color: #ffffff;
    box-shadow: 0px 0px 8px 0px rgba(0, 138, 255, 0.4);
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  &.ellipsis {
    cursor: default;
    border: none;
    background: transparent;

    &:hover {
      border: none;
      background: transparent;
      color: #8a9ba8;
    }
  }
}
</style>

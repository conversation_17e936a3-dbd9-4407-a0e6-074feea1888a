<template>
	<div class="radio-group">
		<template v-for="radio in radioList" :key="radio.value">
			<div
				class="radio"
				:class="{ 'is-active': radio.value === activeRadio }"
				@click="onClickRadio(radio)"
			>
				<div class="radio-label">
					{{ radio.label }}
				</div>
			</div>
		</template>
	</div>
</template>

<script setup>
const props = defineProps({
	modelValue: {
		type: String,
		default: "全部",
	},
});
const emit = defineEmits(["update:modelValue", "change"]);

const radioList = [
	{ label: "全部", value: "全部" },
	{ label: "指挥部", value: "指挥部" },
	{ label: "格贡一", value: "格贡一" },
	{ label: "格贡二", value: "格贡二" },
	{ label: "格贡三", value: "格贡三" },
	{ label: "格贡四", value: "格贡四" },
	{ label: "贡那一", value: "贡那一" },
	{ label: "贡那二", value: "贡那二" },
	{ label: "试验段", value: "试验段" },
];

const activeRadio = computed({
	get: () => props.modelValue,
	set: (val) => {
		emit("update:modelValue", val);
		emit("change");
	},
});

const onClickRadio = (radio) => {
	activeRadio.value = radio.value;
};
</script>

<style lang="scss" scoped>
.radio-group {
	display: flex;
	flex-direction: row;
	justify-content: space-between;

	.radio {
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		height: 40px;
		aspect-ratio: calc(99 / 42);
		background-image: url("@/assets/modules/PartyBuilding/bg/card-header-radio-bg.webp");
		background-size: auto 100%;
		background-repeat: no-repeat;

		&.is-active {
			background-image: url("@/assets/modules/PartyBuilding/bg/card-header-radio-bg-active.webp");
		}

		&-label {
			font-family: Alibaba PuHuiTi;
			font-size: 18px;
			color: #fefefe;
			letter-spacing: 1px;
			text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6);
		}
	}
}
</style>

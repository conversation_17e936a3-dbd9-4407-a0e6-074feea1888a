<template>
  <div class="sub-card" :class="isRight ? 'right' : ''">
    <div class="sub-card-hd">
      <img
        class="sub-card-hd-img"
        src="@/assets/modules/common/icons/card-header-right-arrow.svg"
        alt=""
      />
      <div class="sub-card-hd-title">{{ title }}</div>
    </div>
    <div class="sub-card-bd">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  // 右边卡片的背景图片左深右浅
  isRight: {
    type: Boolean,
    default: false,
  },
});
</script>

<style lang="scss">
.sub-card {
  padding: 0 12px;
  box-sizing: border-box;
  width: 100%;
  background-image: url('~@/assets/images/card-bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  &.right {
    background-image: url('~@/assets/images/card-bg-r.png');
  }
  &-hd {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding-top: 20px;
    &-img {
      width: 25px;
      height: 12px;
      margin-right: 5px;
    }
    &-title {
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-size: 16px;
      color: #ffffff;
      text-shadow: 0px 0px 9px #158eff;
      line-height: 20px;
    }
  }
  &-bd {
    padding-bottom: 24px;
    color: #fff;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    box-sizing: border-box;
  }
}
</style>

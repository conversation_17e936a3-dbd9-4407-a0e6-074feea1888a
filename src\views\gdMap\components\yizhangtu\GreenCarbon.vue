<template>
  <div class="green-carbon-card">
    <a-spin class="card-loading" dot :loading="loading">
      <BasicCard title="绿色低碳">
        <div class="card-content">
          <div class="progress-indicators">
            <div class="indicator-item">
              <div class="indicator-icon">
                <img src="@/assets/modules/yizhangtu/icons/target-carbon.svg" alt="目标降碳" />
              </div>
              <div class="indicator-content">
                <div class="indicator-value">
                  {{ targetData.targetCarbon }}<span class="unit">吨</span>
                </div>
                <div class="indicator-label">目标降碳</div>
              </div>
            </div>
            <div class="indicator-item">
              <div class="indicator-icon">
                <img src="@/assets/modules/yizhangtu/icons/target-progress.svg" alt="目标进度" />
              </div>
              <div class="indicator-content">
                <div class="indicator-value">{{ targetData.targetProgress }}%</div>
                <div class="indicator-label">目标进度</div>
              </div>
            </div>
            <div class="indicator-item">
              <div class="indicator-icon">
                <img src="@/assets/modules/yizhangtu/icons/actual-progress.svg" alt="实际进度" />
              </div>
              <div class="indicator-content">
                <div
                  class="indicator-value"
                  :style="getProgressStyle(targetData.completionRate, targetData.targetProgress)"
                >
                  {{ targetData.completionRate }}%
                </div>
                <div class="indicator-label">实际进度</div>
              </div>
            </div>
          </div>

          <div class="card-content-main">
            <ScreenTable :columns="tableColumns">
              <!-- 使用单一容器结构 -->
              <div ref="listContainerRef" class="scroll-container">
                <div class="carbon-data-list">
                  <div v-for="(item, index) in carbonData" :key="index" class="carbon-data-item">
                    <div class="col type">{{ item.type }}</div>
                    <div class="col progress">
                      <span class="progress-text"> {{ item.progress }}% </span>
                      <div class="progress-bar">
                        <div
                          class="progress-bar-inner"
                          :style="{
                            width: item.progress + '%',
                          }"
                        ></div>
                      </div>
                    </div>
                    <div class="col value">
                      <span class="current-value">{{ item.current }}</span>
                      <span class="separator">/</span>
                      <span class="target-value">{{ item.target }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </ScreenTable>
          </div>
        </div>
      </BasicCard>
    </a-spin>
  </div>
</template>

<script setup>
import { watch, ref, reactive, onMounted, onBeforeUnmount } from 'vue';
import BasicCard from '@/components/BasicCard/index.vue';
import ScreenTable from '../ScreenTable.vue';
import { useMarquee } from '@/hooks/useMarquee';
import emitter from '@/utils/emitter';
// 修改导入路径，使用services中的greenCarbon.js
import { getGreenCarbonData, getGreenCarbonTarget } from '../../services/yizhangtu.mock.js';

// 当前区域类型
const currentArea = ref('all');

// 目标数据 - 使用reactive以便直接修改属性
const targetData = reactive({
  targetCarbon: 0, // 目标降碳
  targetProgress: 0, // 目标进度
  completionRate: 0, // 实际进度
});

// 碳数据列表
const carbonData = ref([]);

// 数据加载状态
const loading = ref(false);

// 初始化加载数据
async function initData() {
  loading.value = true;
  try {
    // 始终使用总览数据
    const dataKey = 'all';

    // 获取目标数据
    const targetResult = await getGreenCarbonTarget(dataKey);
    Object.assign(targetData, targetResult);

    // 获取碳数据列表
    const dataResult = await getGreenCarbonData(dataKey);
    carbonData.value = dataResult;
  } catch (error) {
    console.error('加载绿色低碳数据失败:', error);
  } finally {
    loading.value = false;
  }
}

// 处理区域变化的函数
const handleAreaChange = async value => {
  // 更新当前区域
  currentArea.value = value;

  // 重新加载数据
  loading.value = true;
  try {
    // 获取目标数据
    const targetResult = await getGreenCarbonTarget(currentArea.value);
    Object.assign(targetData, targetResult);

    // 获取碳数据列表
    const dataResult = await getGreenCarbonData(currentArea.value);
    carbonData.value = dataResult;
  } catch (error) {
    console.error('加载绿色低碳数据失败:', error);
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 1000); // 延迟一点时间确保DOM已更新
  }
};

// 表格列配置
const tableColumns = [
  { title: '降碳类型', flex: 1 },
  { title: '降碳进度', flex: 1.3 },
  { title: '当前/目标降碳量（吨）', flex: 2, align: 'right' },
];

// 获取进度样式
const getProgressStyle = (actual, target) => {
  if (actual === target) {
    return { color: '#0783fa' }; // 默认颜色
  } else if (actual < target) {
    return { color: '#FF4042' }; // 小于目标进度时为红色
  } else {
    return { color: '#06D239' }; // 大于目标进度时为绿色
  }
};

// 使用跑马灯效果
const { containerRef: listContainerRef, resetScroll } = useMarquee({
  speed: 10, // 滚动速度
  delay: 3000, // 滚动到底部后停顿时间
  step: 1, // 每次滚动的像素
});

// 当数据变化时，重置滚动
watch(
  carbonData,
  () => {
    setTimeout(resetScroll, 300); // 延迟一点时间确保DOM已更新
  },
  { deep: true }
);

// 组件挂载时初始化数据并监听事件
onMounted(() => {
  initData();
  emitter.$on('pageNavChange', handleAreaChange);
});

// 组件销毁前移除事件监听
onBeforeUnmount(() => {
  emitter.$off('pageNavChange', handleAreaChange);
});
</script>

<style lang="scss" scoped>
.green-carbon-card {
  height: 269px;

  .card-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    &-main {
      flex: 1;
      overflow: hidden;
    }
  }

  .carbon-data-list {
    flex: 1;
    overflow: hidden; // 修改为hidden，由hooks控制滚动
    position: relative; // 添加相对定位
    // overflow-y: auto;

    .carbon-data-item {
      display: flex;
      padding: 5px 0;
      align-items: center;
      cursor: pointer;
      &:last-child {
        border-bottom: none;
      }

      .col {
        display: flex;
        align-items: center;
        padding: 0 2px;

        &.type {
          flex: 1;
          font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
          font-size: 12px;
          color: #e5f0ff;
          text-align: left;
        }

        &.progress {
          display: flex;
          gap: 14px;
          flex: 2;

          .progress-text {
            width: 35px;
            font-family: D-DIN-PRO, D-DIN-PRO;
            font-weight: bold;
            font-size: 14px;
            color: #48d6ff;
            overflow: hidden;
          }

          .progress-bar {
            flex: 1;
            width: flex;
            height: 6px;
            background-color: #093264;
            border-radius: 50px;

            .progress-bar-inner {
              height: 100%;
              background: linear-gradient(270deg, #0085ff 0%, #51e0ff 100%);
              border-radius: 50px;
            }
          }
        }

        &.value {
          flex: 1;
          justify-content: flex-end;
          font-family: 'D-DIN-PRO', sans-serif;
          text-align: right;
          overflow: hidden;

          .current-value {
            font-size: 14px;
            font-weight: bold;
            color: #fff;
          }

          .separator {
            margin: 0 5px;
            opacity: 0.5;
            color: #8c8c8c;
          }

          .target-value {
            font-size: 14px;
            opacity: 0.5;
            color: #8c8c8c;
          }
        }
      }
    }
  }
}

.scroll-container {
  height: 100%; // 容器需要有固定高度
  overflow: hidden; // 重要：设置为hidden，由hooks控制滚动
  position: relative; // 相对定位
}

.progress-indicators {
  height: 62px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .indicator-item {
    height: 38px;
    display: flex;
    align-items: center;
    .indicator-icon {
      margin-right: 4px;
      width: 44px;
      height: 38px;
      display: flex;
      justify-content: center;
      align-items: center;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .indicator-content {
      .indicator-value {
        margin-bottom: 2px;
        font-family: D-DIN-PRO-Bold D-DIN-PRO;
        font-weight: bold;
        font-size: 20px;
        color: #0783fa;
        .unit {
          margin-left: 2px;
          font-size: 10px;
          color: #ffffff;
          opacity: 0.8;
        }
      }
      .indicator-label {
        font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        font-size: 12px;
        color: #ffffff;
        opacity: 0.8;
      }
    }
  }
}
</style>

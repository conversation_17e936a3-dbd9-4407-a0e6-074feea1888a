<template>
  <TrafficCard title="工艺模拟">
    <div class="real-time-flow">
      <Video :videoList="videoList" />
    </div>
  </TrafficCard>
</template>

<script setup>
import request from "@/utils/request";
import Video from "./components/video.vue";
import emitter from "@/utils/emitter";

const videoList = ref([]);
const currentDepartmentId = ref(null);

onMounted(() => {
  // 监听区域双击事件
  emitter.$on("division-area-dblclick", handleAreaChange);
  getVideoList();
});

onBeforeUnmount(() => {
  emitter.$off("division-area-dblclick", handleAreaChange);
});

const handleAreaChange = (eventData) => {
  console.log("工艺模拟 - 接收到区域双击事件:", eventData);
  // currentDepartmentId.value = eventData.properties.id;
  // getVideoList();
};

const getVideoList = () => {
  const params = currentDepartmentId.value ? { departmentId: currentDepartmentId.value } : {};
  request.get("/api/screen/board/right/simulation/list", params).then(async (res) => {
    const stacks = res.data.map(async (path) => {
      const { data } = await request.get("/api/upload-histories/file/download/preurl", { path });
      return data;
    });
    videoList.value = await Promise.all(stacks);
  });
};
</script>

<style lang="scss" scoped>
.real-time-flow {
  height: 168px;
}
</style>

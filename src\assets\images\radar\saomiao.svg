<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="78.0206298828125" height="78.0205078125" viewBox="0 0 78.0206298828125 78.0205078125" fill="none">
<g mask="url(#mask-1_30255)" opacity="0.93">
<rect  x="0" y="0" width="78.02061855670095" height="78.02061855670098" rx="39.010309278350476"    fill="url(#linear_fill_1_30256_0)" >
</rect>
</g>
<g mask="url(#mask-1_30255)">
<path    stroke="url(#linear_border_1_30257_0)" stroke-width="1"    d="M78.0206 39.0103L39.0103 39.0103">
</path>
</g>
<defs>
<radialGradient id="linear_fill_1_30255_0" cx="0" cy="0" r="1" gradientTransform="translate(39.01030927835068 39.01030927835069) rotate(-180) scale(39.010309278350476, 39.010309278350476)" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#54708C" stop-opacity="0" />
<stop offset="1" stop-color="#77FFF4"  />
</radialGradient>
<mask id="mask-1_30255" style="mask-type:alpha" maskUnits="userSpaceOnUse">
<g opacity="0.93">
<rect  x="0" y="0" width="78.02061855670095" height="78.02061855670098" rx="39.010309278350476"    fill="url(#linear_fill_1_30255_0)" >
</rect>
</g>
</mask>
<radialGradient id="linear_fill_1_30256_0" cx="0" cy="0" r="1" gradientTransform="translate(39.010309278350476 0) rotate(-180) scale(78.02061855670098, 78.02061855670098)" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#2A5B8C" stop-opacity="0" />
<stop offset="0.7379534527972028" stop-color="#00468C" stop-opacity="0" />
<stop offset="0.8444602272727273" stop-color="#0067B0"  />
<stop offset="1" stop-color="#73D0FF"  />
</radialGradient>
<linearGradient id="linear_border_1_30257_0" x1="58.515380859375" y1="39.01025390625" x2="58.515380859375" y2="39.01025390625" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#FFFFFF"  />
<stop offset="1" stop-color="#FFFFFF"  />
</linearGradient>
</defs>
</svg>

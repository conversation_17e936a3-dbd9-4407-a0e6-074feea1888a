<svg width="22" height="23" viewBox="0 0 22 23" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#230;&#139;&#140;&#229;&#144;&#136;&#231;&#171;&#153;">
<g id="Ellipse 15" filter="url(#filter0_d_342_3750)">
<circle cx="10.9956" cy="11.5518" r="8" fill="url(#paint0_radial_342_3750)" fill-opacity="0.7"/>
<circle cx="10.9956" cy="11.5518" r="7.55492" stroke="url(#paint1_linear_342_3750)" stroke-width="0.890162"/>
</g>
<circle id="Ellipse 16" cx="11.0695" cy="11.6252" r="7.07681" fill="url(#paint2_radial_342_3750)" stroke="url(#paint3_linear_342_3750)" stroke-width="0.200286"/>
<g id="&#229;&#176;&#143;&#230;&#161;&#165;" filter="url(#filter1_d_342_3750)">
<g id="Frame" clip-path="url(#clip0_342_3750)">
<path id="Vector" d="M14.9486 8.69126H14.9472C14.9481 8.68755 14.9486 8.68374 14.9486 8.67992C14.9486 8.56074 14.5058 8.46412 13.9595 8.46412C13.8321 8.46412 13.7104 8.46943 13.5986 8.47901V12.9645L13.5983 12.9647L13.533 13.0486H14.9486V8.69126H14.9486ZM14.1056 14.0641L14.9484 13.0488H13.5329L13.2607 13.3987L13.813 14.0641H12.3512L13.3778 12.7442H11.0506L12.0771 14.0641H9.63444V12.7189H11.0453V12.744H13.378V7.319H13.3764C13.3775 7.31463 13.378 7.31014 13.378 7.30564C13.378 7.16542 12.8571 7.05176 12.2144 7.05176C11.5717 7.05176 11.0507 7.16541 11.0507 7.30564C11.0507 7.31012 11.0513 7.31457 11.0524 7.319H11.0453V10.9328H9.63444V10.1389H6.94418V14.0641H5.99597V14.9902H15.897V14.0641H14.1056ZM9.63444 11.3959H11.0453V12.2559H9.63444V11.3959ZM8.81855 13.1159H7.84829V12.4078H8.81855V13.1159ZM8.81855 11.6408H7.84829V10.9328H8.81855V11.6408Z" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_342_3750" x="0.325119" y="0.881272" width="21.341" height="21.341" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.33524"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.388235 0 0 0 0 0.640523 0 0 0 0 0.807843 0 0 0 0.51 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_342_3750"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3750" result="shape"/>
</filter>
<filter id="filter1_d_342_3750" x="5.9314" y="5.9668" width="10" height="10.4451" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.445081"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.150327 0 0 0 0 0.247059 0 0 0 0 0.145098 0 0 0 0.75 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_342_3750"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3750" result="shape"/>
</filter>
<radialGradient id="paint0_radial_342_3750" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(10.9956 11.5518) scale(8)">
<stop stop-color="#D3D3D3"/>
<stop offset="0.03" stop-color="#D3D3D3" stop-opacity="0.9216"/>
<stop offset="0.13" stop-color="#D3D3D3" stop-opacity="0.7056"/>
<stop offset="0.23" stop-color="#D3D3D3" stop-opacity="0.5329"/>
<stop offset="0.35" stop-color="#D3D3D3" stop-opacity="0.4225"/>
<stop offset="0.47" stop-color="#D3D3D3" stop-opacity="0.3364"/>
<stop offset="0.6" stop-color="#D3D3D3" stop-opacity="0.2916"/>
<stop offset="0.76" stop-color="#D3D3D3" stop-opacity="0.2601"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.5"/>
</radialGradient>
<linearGradient id="paint1_linear_342_3750" x1="10.9956" y1="19.5518" x2="10.9956" y2="3.55176" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<radialGradient id="paint2_radial_342_3750" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(11.0695 11.6252) scale(7.17695)">
<stop stop-color="#D3D3D3"/>
<stop offset="1" stop-color="#0B103B" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint3_linear_342_3750" x1="11.0695" y1="18.8022" x2="11.0695" y2="4.44824" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<clipPath id="clip0_342_3750">
<rect width="10" height="10" fill="white" transform="translate(5.9314 5.9668)"/>
</clipPath>
</defs>
</svg>

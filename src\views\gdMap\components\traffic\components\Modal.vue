<template>
  <a-modal
    v-model:visible="visible"
    :modal-class="['traffic-modal']"
    popup-container="#teleport-container"
    :mask-style="{
      'backdrop-filter': 'blur(12px)',
      'background-color': props.mask ? '' : 'transparent',
    }"
    :mask-closable="false"
    simple
    :footer="false"
  >
    <template #title>
      <div class="title">
        <slot name="title"></slot>
        {{ $slots.title ? "" : props.title }}
      </div>
      <modelClose class="icon-close" @click="close" />
      <modelDivider class="icon-divider" />
    </template>
    <slot />
  </a-modal>
</template>

<script setup>
import modelClose from "@/assets/modules/traffic/icon/model-close.svg?component";
import modelDivider from "@/assets/modules/traffic/icon/model-divider.svg?component";
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  mask: {
    type: Boolean,
    default: true,
  },
});
const visible = ref(false);

const emit = defineEmits(["close"]);

const close = () => {
  visible.value = false;
  emit("close");
};

const open = () => {
  visible.value = true;
};

defineExpose({ open });
</script>

<style lang="scss">
.traffic-modal {
  &.arco-modal {
    width: 66%;
    height: auto;
    aspect-ratio: calc(1296 / 859);
    padding: 0;
    background-image: url("@/assets/modules/traffic/bg/model-bg.svg");
    background-size: 100% 100%;
    background-color: transparent;
    display: inline-flex;
    flex-direction: column;

    .arco-modal-body {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 0 48px 24px 48px;
      margin-top: -10px;
    }

    .arco-modal-title {
      height: 31px;
      padding: 24px 48px;
      position: relative;
      align-items: center;
      justify-content: start;

      .title {
        width: 100%;
        padding-right: 38px;
        font-family: Alibaba PuHuiTi;
        font-size: 22px;
        letter-spacing: 2px;
        color: #ffffff;
        background-image: linear-gradient(180deg, #dff0fa 19%, #e0f0ff 46%, #a8d4f9 82%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        display: flex;
        align-items: center;
      }

      .icon-close {
        position: absolute;
        top: 16px;
        right: 16px;
        width: 40px;
        height: 40px;
        cursor: pointer;
      }

      .icon-divider {
        position: absolute;
        left: 48px;
        bottom: 8px;
        width: calc(100% - 2 * 48px);
        height: auto;
      }
    }
  }
}
</style>

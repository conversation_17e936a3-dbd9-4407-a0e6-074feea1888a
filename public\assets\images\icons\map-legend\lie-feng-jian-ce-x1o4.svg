<svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#232;&#163;&#130;&#231;&#188;&#157;&#231;&#155;&#145;&#230;&#181;&#139;">
<g id="Ellipse 15" filter="url(#filter0_d_342_3858)">
<circle cx="10.9956" cy="11.0186" r="8" fill="url(#paint0_radial_342_3858)" fill-opacity="0.7"/>
<circle cx="10.9956" cy="11.0186" r="7.55492" stroke="url(#paint1_linear_342_3858)" stroke-width="0.890162"/>
</g>
<circle id="Ellipse 16" cx="10.9951" cy="11.0174" r="7.01097" fill="url(#paint2_radial_342_3858)" stroke="url(#paint3_linear_342_3858)" stroke-width="0.200286"/>
<g id="Group 1321315401" filter="url(#filter1_d_342_3858)">
<g id="Frame">
<path id="Vector" d="M14.9626 13.0263L14.7843 11.4471L14.7002 10.546L14.4823 8.54599C14.413 7.92716 14.0516 7.4717 13.6358 7.4816H11.8387L12.1407 7.96677L11.6159 8.88263L12.7744 8.94204L11.2941 10.9223L12.3882 11.749L11.5021 13.6748L11.6258 14.5016H14.1308C14.3784 14.5016 14.6111 14.3382 14.7744 14.056C14.9279 13.7739 15.0022 13.3976 14.9626 13.0263ZM11.0021 12.1946L9.73964 11.1352L10.9228 9.27373L9.66538 9.66978L10.5912 8.07073L10.2199 7.4816H8.33861C7.92276 7.47665 7.56137 7.92716 7.49206 8.54599L7.24453 10.8629L7.13066 11.9124L7.0069 13.0164C6.96729 13.3877 7.0366 13.764 7.19502 14.0511C7.35839 14.3382 7.59107 14.5016 7.84355 14.5016H10.3139L10.2001 13.7243L11.0021 12.1946Z" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_342_3858" x="0.325119" y="0.348069" width="21.341" height="21.341" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.33524"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.388235 0 0 0 0 0.640523 0 0 0 0 0.807843 0 0 0 0.51 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_342_3858"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3858" result="shape"/>
</filter>
<filter id="filter1_d_342_3858" x="5.99597" y="6.01953" width="9.9082" height="10.3533" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.445081"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.150327 0 0 0 0 0.247059 0 0 0 0 0.145098 0 0 0 0.75 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_342_3858"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3858" result="shape"/>
</filter>
<radialGradient id="paint0_radial_342_3858" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(10.9956 11.0186) scale(8)">
<stop stop-color="#D3D3D3"/>
<stop offset="0.03" stop-color="#D3D3D3" stop-opacity="0.9216"/>
<stop offset="0.13" stop-color="#D3D3D3" stop-opacity="0.7056"/>
<stop offset="0.23" stop-color="#D3D3D3" stop-opacity="0.5329"/>
<stop offset="0.35" stop-color="#D3D3D3" stop-opacity="0.4225"/>
<stop offset="0.47" stop-color="#D3D3D3" stop-opacity="0.3364"/>
<stop offset="0.6" stop-color="#D3D3D3" stop-opacity="0.2916"/>
<stop offset="0.76" stop-color="#D3D3D3" stop-opacity="0.2601"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.5"/>
</radialGradient>
<linearGradient id="paint1_linear_342_3858" x1="10.9956" y1="19.0186" x2="10.9956" y2="3.01855" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<radialGradient id="paint2_radial_342_3858" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(10.9951 11.0174) scale(7.11111)">
<stop stop-color="#D3D3D3"/>
<stop offset="1" stop-color="#0B103B" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint3_linear_342_3858" x1="10.9951" y1="18.1285" x2="10.9951" y2="3.90625" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
</defs>
</svg>

<template>
  <div class="personnel-pie-chart">
    <v-chart class="chart" :option="chartOption" autoresize />
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { PieChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  GraphicComponent,
} from 'echarts/components';
import { Grid3DComponent } from 'echarts-gl/components';
import { SurfaceChart } from 'echarts-gl/charts';
import VChart from 'vue-echarts';

// 注册必要的组件
use([
  CanvasRenderer,
  PieChart,
  SurfaceChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  Grid3DComponent,
  GraphicComponent,
]);

// 定义props，使用一个对象包含所有人员数据
const props = defineProps({
  // 人员数据对象
  data: {
    type: Object,
    default: () => ({
      managerCount: 24,
      workerCount: 56,
      totalCount: null, // 如果为null则自动计算总和
    }),
  },
});

// 图表数据，使用计算属性从props获取
const chartData = computed(() => [
  { name: '管理人员', value: props.data.managerCount, itemStyle: { color: '#1ADECD' } },
  { name: '劳务人员', value: props.data.workerCount, itemStyle: { color: '#0783FA' } },
]);

// 计算总人数
const totalPersonnel = computed(() => {
  // 如果提供了totalCount，则使用它，否则计算总和
  return props.data.totalCount !== null
    ? props.data.totalCount
    : props.data.managerCount + props.data.workerCount;
});

// 常量配置
const CHART_COLORS = {
  manager: '#1ADECD',
  worker: '#0783FA',
  ring1: '#0066FF',
  ring2: '#00CCFF',
  ring3: '#1ADECD',
};

const RING_SIZES = {
  outer: 2.2,
  middle: 2.5,
  inner: 2.8,
};

// 参数方程生成函数 - 优化计算逻辑
function getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, height) {
  const midRatio = (startRatio + endRatio) / 2;
  const startRadian = startRatio * Math.PI * 2;
  const endRadian = endRatio * Math.PI * 2;
  const midRadian = midRatio * Math.PI * 2;

  // 如果只有一个扇形，则不实现选中效果
  if (startRatio === 0 && endRatio === 1) {
    isSelected = false;
  }

  // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
  k = k ?? 1 / 3;

  // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
  const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
  const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;

  // 计算高亮效果的放大比例（未高亮，则比例为 1）
  const hoverRate = isHovered ? 1.05 : 1;

  // 返回曲面参数方程
  return {
    u: {
      min: -Math.PI,
      max: Math.PI * 3,
      step: Math.PI / 32,
    },
    v: {
      min: 0,
      max: Math.PI * 2,
      step: Math.PI / 20,
    },
    x: function (u, v) {
      if (u < startRadian) {
        return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      if (u > endRadian) {
        return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
    },
    y: function (u, v) {
      if (u < startRadian) {
        return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      if (u > endRadian) {
        return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
    },
    z: function (u, v) {
      if (u < -Math.PI * 0.5) {
        return Math.sin(u);
      }
      if (u > Math.PI * 2.5) {
        return Math.sin(u);
      }
      return Math.sin(v) > 0 ? 1 * height : -1;
    },
  };
}

// 创建底部圆环函数 - 抽取重复代码
function createRing(name, opacity, color, size, zOffset) {
  return {
    name,
    type: 'surface',
    parametric: true,
    wireframe: { show: false },
    itemStyle: { opacity, color },
    parametricEquation: {
      u: { min: 0, max: Math.PI * 2, step: Math.PI / 20 },
      v: { min: 0, max: Math.PI, step: Math.PI / 20 },
      x: function (u, v) {
        return ((Math.sin(v) * Math.sin(u) + Math.sin(u)) / Math.PI) * size;
      },
      y: function (u, v) {
        return ((Math.sin(v) * Math.cos(u) + Math.cos(u)) / Math.PI) * size;
      },
      z: function (u, v) {
        return Math.cos(v) > 0 ? zOffset[0] : zOffset[1];
      },
    },
  };
}

// 生成3D饼图配置 - 优化结构和性能
function getPie3D(pieData, internalDiameterRatio) {
  const series = [];
  let sumValue = 0;
  let startValue = 0;
  let endValue = 0;
  const legendData = [];

  // 计算k值
  const k =
    typeof internalDiameterRatio !== 'undefined'
      ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
      : 1 / 3;

  // 计算总值
  pieData.forEach(item => {
    sumValue += item.value;
  });

  // 为每一个饼图数据，生成一个 series-surface 配置
  pieData.forEach((item, index) => {
    const seriesItem = {
      name: item.name || `series${index}`,
      type: 'surface',
      parametric: true,
      wireframe: { show: false },
      pieData: item,
      pieStatus: {
        selected: false,
        hovered: false,
        k,
      },
      emphasis: {
        itemStyle: {
          color: item.itemStyle?.color,
          opacity: 0.8,
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowOffsetY: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)',
        },
      },
    };

    // 设置样式
    if (item.itemStyle) {
      seriesItem.itemStyle = {};
      if (item.itemStyle.color) seriesItem.itemStyle.color = item.itemStyle.color;
      if (item.itemStyle.opacity) seriesItem.itemStyle.opacity = item.itemStyle.opacity;
    }

    series.push(seriesItem);
  });

  // 计算参数方程
  series.forEach((item, index) => {
    endValue = startValue + item.pieData.value;
    item.pieData.startRatio = startValue / sumValue;
    item.pieData.endRatio = endValue / sumValue;
    item.parametricEquation = getParametricEquation(
      item.pieData.startRatio,
      item.pieData.endRatio,
      false,
      false,
      k,
      1 // 固定高度
    );

    startValue = endValue;
    legendData.push(item.name);
  });

  // 添加底部三个圆环支撑
  series.push(
    createRing('底部圆环1', 0.2, CHART_COLORS.ring1, RING_SIZES.outer, [-0.5, -1]),
    createRing('底部圆环2', 0.15, CHART_COLORS.ring2, RING_SIZES.middle, [-0.6, -1.1]),
    createRing('底部圆环3', 0.1, CHART_COLORS.ring3, RING_SIZES.inner, [-0.7, -1.2])
  );

  return series;
}

// 计算图表配置
const chartOption = computed(() => {
  const series = getPie3D(chartData.value, 0.8);

  // 添加2D饼图用于显示标签
  series.push({
    name: 'pie2d',
    type: 'pie',
    label: { show: false },
    labelLine: { show: false },
    startAngle: -50,
    clockwise: false,
    radius: ['0', '0'],
    center: ['30%', '50%'],
    data: chartData.value,
    itemStyle: { opacity: 0 },
  });

  return {
    backgroundColor: 'transparent',
    title: { show: false },
    legend: {
      orient: 'vertical',
      left: '50%',
      top: '40%',
      itemGap: 8,
      data: chartData.value.map(item => item.name),
      formatter: name => {
        const item = chartData.value.find(d => d.name === name);
        return `${name}  {value|${item.value}}`;
      },
      itemWidth: 6,
      itemHeight: 6,
      textStyle: {
        color: '#fff',
        fontSize: 12,
        rich: {
          value: {
            fontSize: 14,
            fontWeight: 'bold',
            padding: [0, 0, 0, 48],
            color: '#fff',
          },
        },
      },
      icon: 'circle',
    },
    xAxis3D: { min: -1, max: 1 },
    yAxis3D: { min: -1, max: 1 },
    zAxis3D: { min: -1, max: 1 },
    grid3D: {
      show: false,
      boxHeight: 20,
      viewControl: {
        distance: 180,
        alpha: 25,
        beta: 40,
        autoRotate: true,
        autoRotateSpeed: 5,
        rotateSensitivity: 0,
      },
      left: '0%',
      width: '50%',
      height: '80%',
    },
    series,
    // 添加自定义图形组件显示总人数
    graphic: [
      {
        type: 'group',
        left: '50%',
        top: '20%',
        children: [
          {
            type: 'text',
            style: {
              text: '今日出勤总人数',
              fill: '#fff',
              fontSize: 12,
              fontWeight: 'normal',
            },
            left: 0,
          },
          {
            type: 'text',
            style: {
              text: totalPersonnel.value,
              fill: '#fff',
              fontSize: 14,
              fontWeight: 'bold',
            },
            left: 120,
          },
        ],
      },
    ],
  };
});

// 监听数据变化，可以添加防抖优化
let chartInstance = null;

onMounted(() => {
  // 可以在这里获取图表实例进行额外配置
});
</script>

<style scoped>
.personnel-pie-chart {
  width: 100%;
  height: 100%;
  background: rgba(9, 50, 100, 0.2);
  border-radius: 4px;
  padding: 10px;
  box-sizing: border-box;
}

.chart {
  width: 100%;
  height: 100%;
  min-height: 100px;
}
</style>

import maplibregl from "maplibre-gl";
import mapConfig from "@/config/engine/maplibre/map.config.js";

export class MaskLayer {
	constructor(map) {
		this.map = map;
		this.baseLayerId = "mask-layer";
		this.type = "custom";
		this.renderingMode = "2d";
		this.color = [0.02, 0.03, 0.08, 0.85];
	}

	async init() {
		this.map.addLayer({
			id: this.baseLayerId,
			type: this.type,
			renderingMode: this.renderingMode,
			onAdd: this.onAdd.bind(this),
			render: this.render.bind(this),
		});
	}

	onAdd(map, gl) {
		const vertexSource = `#version 300 es

            uniform mat4 u_matrix;
            in vec2 a_pos;
            void main() {
                gl_Position = u_matrix * vec4(a_pos, 0.0, 1.0);
            }`;

		const fragmentSource = `#version 300 es

		    out highp vec4 fragColor;

		    void main() {
		        fragColor = vec4(${this.color.join(",")});
		    }
		`;

		const vertexShader = gl.createShader(gl.VERTEX_SHADER);
		gl.shaderSource(vertexShader, vertexSource);
		gl.compileShader(vertexShader);

		const fragmentShader = gl.createShader(gl.FRAGMENT_SHADER);
		gl.shaderSource(fragmentShader, fragmentSource);
		gl.compileShader(fragmentShader);

		this.program = gl.createProgram();
		gl.attachShader(this.program, vertexShader);
		gl.attachShader(this.program, fragmentShader);
		gl.linkProgram(this.program);

		this.aPos = gl.getAttribLocation(this.program, "a_pos");

		const offset = 10;
		const left_top = maplibregl.MercatorCoordinate.fromLngLat({
			lng: mapConfig.bounds[0][0] - offset,
			lat: mapConfig.bounds[1][1] + offset,
		});
		const left_bottom = maplibregl.MercatorCoordinate.fromLngLat({
			lng: mapConfig.bounds[0][0] - offset,
			lat: mapConfig.bounds[0][1] - offset,
		});
		const right_top = maplibregl.MercatorCoordinate.fromLngLat({
			lng: mapConfig.bounds[1][0] + offset,
			lat: mapConfig.bounds[1][1] + offset,
		});
		const right_bottom = maplibregl.MercatorCoordinate.fromLngLat({
			lng: mapConfig.bounds[1][0] + offset,
			lat: mapConfig.bounds[0][1] - offset,
		});

		this.buffer = gl.createBuffer();
		gl.bindBuffer(gl.ARRAY_BUFFER, this.buffer);
		gl.bufferData(
			gl.ARRAY_BUFFER,
			new Float32Array([
				left_top.x,
				left_top.y,
				right_top.x,
				right_top.y,
				left_bottom.x,
				left_bottom.y,
				right_bottom.x,
				right_bottom.y,
			]),
			gl.STATIC_DRAW
		);
	}

	render(gl, args) {
		gl.useProgram(this.program);
		gl.uniformMatrix4fv(
			gl.getUniformLocation(this.program, "u_matrix"),
			false,
			args.defaultProjectionData.mainMatrix
		);
		gl.bindBuffer(gl.ARRAY_BUFFER, this.buffer);
		gl.enableVertexAttribArray(this.aPos);
		gl.vertexAttribPointer(this.aPos, 2, gl.FLOAT, false, 0, 0);
		gl.enable(gl.BLEND);
		// gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);
		gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);
	}
}

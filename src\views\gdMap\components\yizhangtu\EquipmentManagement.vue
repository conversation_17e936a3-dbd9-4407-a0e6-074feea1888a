<template>
	<div class="equipment-manage-card">
		<a-spin class="card-loading" dot :loading="loading">
			<BasicCard title="设备管理">
				<div class="card-content">
					<div class="target-info">
						<div class="target-item">
							<div class="target-icon">
								<img
									src="@/assets/modules/yizhangtu/icons/total-num-equipment.png"
									alt="设备总数"
								/>
							</div>
							<div class="target-content">
								<div class="target-label">设备总数</div>
								<div class="target-value">
									{{ targetCarbon }}
								</div>
							</div>
						</div>
						<div class="target-item">
							<div class="target-icon">
								<img
									src="@/assets/modules/yizhangtu/icons/overall-online-rate.png"
									alt="整体在线率"
								/>
							</div>
							<div class="target-content">
								<div class="target-label">整体在线率</div>
								<div class="target-value">{{ completionRate }}<span class="unit">%</span></div>
							</div>
						</div>
					</div>
					<div class="card-content-main">
						<ScreenTable :columns="tableColumns">
							<div class="equipment-list">
								<div v-for="(item, index) in equipmentData" :key="index" class="equipment-item">
									<div class="equipment-progress">
										<div class="progress-bar">
											<div class="progress-bar-inner" :style="{ width: item.percentage + '%' }">
												<span class="equipment-name">{{ item.name }}</span>
												<span class="online-value">{{ item.online }}</span>
											</div>
											<div class="progress-bar-bg">
												<span class="total-value">{{ item.total }}</span>
											</div>
										</div>
									</div>
									<div class="equipment-percentage">{{ item.percentage }}%</div>
								</div>
							</div>
						</ScreenTable>
					</div>
				</div>

				<template #extra>
					<div class="date-filter">
						<a-dropdown trigger="click">
							<div class="filter-btn">
								<span class="filter-btn-text">{{ selectedDays }}天</span>
								<span class="arrow-down">
									<icon-caret-down />
								</span>
							</div>
							<template #content>
								<a-doption v-for="day in dayOptions" :key="day" @click="selectedDays = day">
									{{ day }}天
								</a-doption>
							</template>
						</a-dropdown>
					</div>
				</template>
			</BasicCard>
		</a-spin>
	</div>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount } from "vue";
import BasicCard from "@/components/BasicCard/index.vue";
import ScreenTable from "../ScreenTable.vue";
import emitter from "@/utils/emitter";
import { IconCaretDown } from "@arco-design/web-vue/es/icon";
import { getEquipmentData } from "@/views/gdMap/services/yizhangtu.mock.js";

// 删除props，改用本地ref变量
const targetCarbon = ref("125");
const completionRate = ref("60.80");

// 选中的天数，默认为7天
const selectedDays = ref(7);
// 天数选项
const dayOptions = [3, 7, 15, 30];

// 表格列配置
const tableColumns = [
	{ title: "设备", width: "33%" },
	{ title: "在线量", width: "35%" },
	{ title: "总量", width: "15%" },
	{ title: "在线率", width: "17%", align: "center" },
];

// 设备数据
const equipmentData = ref([
	{ name: "运输车辆", online: 45, total: 60, percentage: 75 },
	{ name: "自有机械", online: 50, total: 90, percentage: 55 },
	{ name: "分包机械", online: 45, total: 90, percentage: 50 },
	{ name: "租赁机械", online: 45, total: 90, percentage: 50 },
	{ name: "闲置机械", online: 45, total: 90, percentage: 50 },
]);

const loading = ref(false);

// 当前区域类型
const currentArea = ref("all");

// 设备类型映射
const equipmentTypes = [
	{ key: "transport", name: "运输车辆" },
	{ key: "selfOwned", name: "自有机械" },
	{ key: "subcontracted", name: "分包机械" },
	{ key: "leased", name: "租赁机械" },
	{ key: "idle", name: "闲置机械" },
];

// 加载数据的方法
const loadData = async (value = "all") => {
	// 更新当前区域
	currentArea.value = value;

	// 重新加载数据
	loading.value = true;
	try {
		const result = await getEquipmentData(selectedDays.value, currentArea.value);

		// console.log('result', result);

		// 更新设备数据
		if (result) {
			// 更新总数和在线率
			if (result.total) {
				targetCarbon.value = result.total.total;
				completionRate.value = result.total.rate.toFixed(2);
			}

			// 更新设备列表数据
			equipmentData.value = equipmentTypes.map((type) => {
				const data = result[type.key] || { online: 0, total: 0, rate: 0 };
				return {
					name: type.name,
					online: data.online,
					total: data.total,
					percentage: data.rate,
				};
			});
		}
	} catch (error) {
		console.error("加载数据失败:", error);
	} finally {
		setTimeout(() => {
			loading.value = false;
		}, 1000); // 延迟一点时间确保DOM已更新
	}
};

// 监听天数变化，重新加载数据
watch(selectedDays, () => {
	loadData(currentArea.value);
});

onMounted(() => {
	loadData();
	emitter.$on("pageNavChange", loadData);
});

// 组件销毁前移除事件监听
onBeforeUnmount(() => {
	emitter.$off("pageNavChange", loadData);
});
</script>

<style lang="scss" scoped>
.equipment-manage-card {
	height: 278px;

	.card-content {
		display: flex;
		flex-direction: column;
		width: 100%;
		&-main {
			flex: 1;
			overflow: hidden;
		}
	}

	.target-info {
		padding: 10px 0;
		display: flex;
		justify-content: center;
		gap: 22px;

		.target-item {
			display: flex;
			align-items: center;
			min-width: 0; // 允许子元素收缩
			flex: 0 1 auto; // 不放大，可收缩，基础大小自动

			.target-icon {
				width: 32px;
				height: 32px;
				margin-right: 8px;
				flex-shrink: 0; // 图标不收缩

				img {
					width: 100%;
					height: 100%;
				}
			}

			.target-content {
				position: relative;
				display: flex;
				align-items: center;
				min-width: 0; // 允许子元素收缩
				height: 100%;

				&::before {
					position: absolute;
					left: 0;
					bottom: 0;
					content: "";
					width: 100%;
					height: 5px;
					background: url("@/assets/modules/common/bg/card-label-bg.svg") no-repeat;
					background-size: 100% 100%;
				}

				.target-label {
					white-space: nowrap; // 防止文本换行
					margin-right: 4px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-size: 12px;
					color: #ffffff;
					flex-shrink: 0; // 标签不收缩
				}

				.target-value {
					font-family: D-DIN-PRO, D-DIN-PRO;
					font-weight: bold;
					font-size: 18px;
					color: #48d6ff;
					white-space: nowrap; // 防止数值换行

					.unit {
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-size: 10px;
						color: #ffffff;
					}
				}
			}
		}
	}

	.equipment-list {
		padding: 3px 0;
		width: 100%;

		.equipment-item {
			padding: 3px 0;
			display: flex;
			align-items: center;
			cursor: pointer;
			transition: all 0.3s ease;

			&:hover {
				background-color: rgba(72, 214, 255, 0.08);
			}

			&:last-child {
				margin-bottom: 0;
			}

			.equipment-progress {
				flex: 1;
				margin-right: 10px;

				.progress-bar {
					position: relative;
					width: 100%;
					height: 22px;
					background-color: rgba(27, 52, 91, 0.8);
					overflow: hidden;
					.progress-bar-inner {
						height: 100%;
						background: linear-gradient(270deg, #48d6ff 0%, rgba(72, 214, 255, 0) 100%);
						display: flex;

						font-size: 12px;
						font-family: Source Han Sans CN-Medium, Source Han Sans CN;
						color: #ffffff;
						.equipment-name {
							position: absolute;
							top: 50%;
							left: 6px;
							transform: translateY(-50%);
						}

						.online-value {
							position: absolute;
							top: 50%;
							left: 120px;
							min-width: 30px;
							text-align: center;
							transform: translateY(-50%);
						}
					}

					.progress-bar-bg {
						position: absolute;
						top: 0;
						right: 0;
						width: 30px;
						height: 100%;
						display: flex;
						align-items: center;
						justify-content: center;
						// padding-right: 8px;

						.total-value {
							font-size: 12px;
							font-family: Source Han Sans CN-Medium, Source Han Sans CN;
							color: #ffffff;
						}
					}
				}
			}

			.equipment-percentage {
				width: 59px;
				font-family: D-DIN-PRO-Bold, D-DIN-PRO;
				font-weight: bold;
				font-size: 14px;
				color: #48d6ff;
				text-align: center;
			}
		}
	}
}

.date-filter {
	.filter-btn {
		padding: 0 5px;
		display: flex;
		justify-content: space-between;
		width: 52px;
		height: 26px;
		line-height: 26px;
		background: #0b3567;
		box-shadow: inset 0px 0px 4px 0px #0e9aff;
		border-radius: 2px;
		border: 1px solid #1299ff;
		box-sizing: border-box;
		cursor: pointer;
		color: #ffffff;
		&-text {
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			font-size: 12px;
		}
		.arrow-down {
			width: 12px;
			height: 12px;
			img {
				width: 100%;
				height: 100%;
			}
		}
	}
}
</style>

<template>
  <div class="property-dialog" v-if="model">
    <div class="property-dialog-header">
      <div class="property-dialog-header-title">属性表</div>
      <div class="property-dialog-header-close" @click="close">
        <icon-close />
      </div>
    </div>
    <div class="property-dialog-body">
      <div class="section-block">
        <div class="item-row" v-for="item in info.Layer" :key="item.name">
          <div class="item-label">{{ labelMap[item.name] }}</div>
          <div class="item-value" :title="item.value">{{ item.value }}</div>
        </div>
      </div>
      <a-collapse
        :default-active-key="['1', 2]"
        destroy-on-hide
        :bordered="false"
        v-if="info.detail.length > 0"
      >
        <a-collapse-item header="详细信息" key="1">
          <div class="section-block">
            <div class="item-row" v-for="item in info.detail" :key="item.name">
              <div class="item-label">{{ item.name }}</div>
              <div class="item-value" :title="item.value">{{ item.value }}</div>
            </div>
          </div>
          <a-collapse destroy-on-hide :bordered="false">
            <a-collapse-item header="Properties" key="1.1">
              <div class="section-block">
                <div class="item-row" v-for="(item, index) in info.properties" :key="index">
                  <div class="item-label">{{ Object.keys(item)[0] }}</div>
                  <div class="item-value" :title="Object.values(item)[0]">
                    {{ Object.values(item)[0] }}
                  </div>
                </div>
              </div>
            </a-collapse-item>
          </a-collapse>
        </a-collapse-item>
      </a-collapse>
    </div>
  </div>
</template>

<script setup>
import { IconClose } from '@arco-design/web-vue/es/icon';

const model = defineModel();

const props = defineProps({
  info: {
    type: Object,
    default: () => ({
      Layer: [],
      detail: [],
      properties: [],
    }),
  },
});
const labelMap = {
  objectId: '对象ID',
  layerType: '类型',
  propertyName: '图层名称',
  layerId: '图层ID',
};

const close = () => {
  model.value = false;
};
</script>

<style lang="scss">
.property-dialog {
  position: absolute;
  color: #fff;
  top: 150px;
  right: 20%;
  z-index: 4;
  width: 300px;
  border-radius: 4px;
  border: 1px solid #58a1e7;
  background: rgba(19, 51, 92, 0.6);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  box-sizing: border-box;
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 10px;
    background: rgba(19, 51, 92, 0.55);
    &-title {
      font-size: 14px;
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      text-shadow: 0px 0px 9px #158eff;
      letter-spacing: 1.6px;
    }
    &-close {
      cursor: pointer;
    }
  }
  &-body {
    padding: 6px 12px;
    overflow-y: auto;
    max-height: 400px;
    min-height: 210px;
  }
  .section-block {
    font-size: 12px;
    line-height: 14px;
    .item-row {
      display: flex;
      align-items: center;
      padding: 3px 12px;
      &:nth-child(2n) {
        border-radius: 4px;
        background: rgba(19, 51, 92, 0.65);
      }
    }
    .item-label {
      width: 100px;
      text-align: left;
    }
    .item-value {
      flex: 1;
      text-align: left;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .arco-collapse {
    overflow: visible;
  }
  .arco-collapse-item-header {
    background-color: transparent !important;
    border: none !important;
    padding: 10px 0 3px 4px !important;
    overflow: visible !important;
    font-size: 12px;
    line-height: 14px;
  }
  .arco-collapse-item .arco-collapse-item-icon-hover {
    left: -12px;
    top: 10px;
    transform: none;
  }
  .arco-collapse-item-content-box {
    padding: 0 !important;
  }
  .arco-collapse-item-content {
    padding-left: 0 !important;
    padding-right: 0 !important;
    background-color: transparent !important;
    overflow: visible;
  }
}
</style>

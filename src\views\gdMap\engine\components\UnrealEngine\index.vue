<template>
  <div class="unreal-engine-container">
    <div id="player" class="unreal-engine"></div>
    <!-- <LayerTree /> -->
    <PropertyDialog :info="info" v-model="visible" />

    <!-- 添加单个标记信息弹窗 -->
    <div v-if="markerInfo.visible" class="marker-info-container">
      <!-- :style="{ left: markerInfo.position.x + 'px', top: markerInfo.position.y + 'px' }" -->
      <MarkerInfo
        :label="markerInfo.label"
        :properties="markerInfo.properties"
        @close="closeMarkerInfo"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import emitter from "@/utils/emitter";
// import LayerTree from './components/LayerTree.vue';
import PropertyDialog from "./components/PropertyDialog.vue";
import MarkerInfo from "@/views/gdMap/engine/components/MapLibreEngine/components/MarkerInfo.vue";

import unrealConfig from "./config.js";

const props = defineProps({
  options: {
    type: Object,
    default: () => ({}),
  },
});

let unreal = null;
let unrealApi = null;
const engineStatus = ref({
  initialized: false,
  paused: false,
});

const info = ref({
  Layer: [],
  detail: [],
  properties: [],
});
const visible = ref(false);

// 单个标记信息
const markerInfo = ref({
  visible: false,
  position: { x: 0, y: 0 },
  label: "",
  properties: {},
});

// 区域标记数据
let areaMarkData = null;

// 加载区域标记数据
const loadAreaMarkData = async () => {
  try {
    const response = await fetch("/config/engine/map/json/area-mark.json");
    areaMarkData = await response.json();
    console.log("区域标记数据加载成功", areaMarkData);
  } catch (error) {
    console.error("加载区域标记数据失败:", error);
  }
};

// 根据markerId查找区域标记
const findAreaMarkByMarkerId = (markerId) => {
  if (!areaMarkData || !areaMarkData.features) return null;

  return areaMarkData.features.find((feature) => feature.properties?.unreal?.markerId === markerId);
};

// 显示标记信息
const showMarkerInfo = (position, label, properties) => {
  markerInfo.value = {
    visible: true,
    position,
    label,
    properties,
  };
};

// 关闭标记信息
const closeMarkerInfo = () => {
  markerInfo.value.visible = false;
};

// 当前相机高度
let currentCameraHeight = 0;

const onReady = () => {
  engineStatus.value.initialized = true;
  window.unrealApi = unrealApi;
  // unrealApi.settings.getInteractiveMode();
  // 关闭交互
  // unrealApi.settings.setEnableInteract(false);
  unrealApi?.settings.setMainUIVisibility(false);
  // unrealApi?.settingsPanel.getCameraMode(val => {
  //   console.log('cameraMode', val);
  // });
  // unrealApi?.tools.showUIPanel(31, [300, 300], val => {
  //   console.log('showUIPanel', val);
  // });

  // 初始化相机高度
  unrealApi?.camera.get((location) => {
    if (location) {
      currentCameraHeight = location.z;
      console.log("初始相机高度:", currentCameraHeight);
    }
  });
  emitter.$emit("unrealReady");
  emitter.$emit("engineOnLoad");
  unrealApi?.settings.setEnableCameraMovingEvent(true);

  // 设置初始视角
  resetCamera();

  // 加载区域标记数据
  loadAreaMarkData();
};

// setMainUIVisibility

const onEvent = (event) => {
  const eventType = event.eventtype;
  //图层类型
  const layerType = event.Type;
  //图层Id
  const layerId = event.Id || event.ID;
  // 图层名称
  const propertyName = event.PropertyName;
  //点击ActorId
  const objectId = event.ObjectID;
  //当前点击位置
  const mousePosition = event.MousePosition; // 获取鼠标点击位置

  // 监听双击事件
  if (eventType === "LeftMouseButtonDoubleClick") {
    console.log("=== 虚幻引擎双击显示高亮事件触发 ===");
    console.log("事件类型:", eventType);
    console.log("图层类型:", layerType);
    console.log("图层ID:", layerId);
    console.log("属性名称:", propertyName);
    console.log("对象ID:", objectId);
    console.log("鼠标位置:", mousePosition);
    console.log("完整事件对象:", event);
    console.log("=== 虚幻引擎双击事件处理完成 ===");
  }

  if (eventType === "LeftMouseButtonClick") {
    console.log(
      "触发事件类型：鼠标左键单击，eventType：",
      layerType,
      layerId,
      objectId,
      mousePosition,
      event
    );

    // 处理marker3d类型的点击
    if (layerType === "marker3d" && layerId) {
      // 查找匹配的区域标记
      const areaMark = findAreaMarkByMarkerId(layerId);

      if (areaMark) {
        console.log("找到匹配的区域标记:", areaMark);

        // 计算弹窗位置（向上偏移20px）
        const popupPosition = {
          x: mousePosition ? mousePosition.x : window.innerWidth / 2,
          y: mousePosition ? mousePosition.y - 20 : window.innerHeight / 2 - 20,
        };

        // 显示标记信息
        showMarkerInfo(popupPosition, areaMark.properties.label, {
          ...areaMark.properties,
          formData: areaMark.properties.formData || [],
        });
      } else {
        // 如果没有找到匹配的标记，关闭当前显示的标记信息
        closeMarkerInfo();
      }

      unrealApi?.infoTree.getBPFunction(layerId, (res) => {
        console.log("marker3D", res);
      });
    } else if (layerType === "TileLayer" && layerId) {
      // 关闭标记信息
      closeMarkerInfo();

      visible.value = true;
      // 处理图层信息
      info.value.Layer = [
        { name: "objectId", value: objectId },
        { name: "layerType", value: layerType },
        { name: "propertyName", value: propertyName },
        { name: "layerId", value: layerId },
      ];
      unrealApi?.tileLayer.getActorInfoFromDB(
        { tileLayerId: layerId, objectIds: [objectId] },
        (val) => {
          // 设置详细信息
          if (val.result == 0) {
            const data = val.data?.[layerId]?.[objectId] || {};
            info.value.detail = [
              { name: "UUID", value: data.UUID },
              { name: "ModelHash", value: data.ModelHash },
              { name: "File", value: data.File },
              { name: "Geom", value: data.Geom },
            ];
            info.value.properties = data.Properties;
          }
        }
      );
    } else {
      info.value.Layer = [];
      info.value.detail = [];
      info.value.properties = [];
      visible.value = false;

      // 点击空白区域时关闭标记信息
      closeMarkerInfo();
    }
  }
  // 相机动画结束
  if (eventType === "CameraTourFinished") {
    console.log("相机动画结束", event);
    emitter.$emit("cameraTourFinished");
  }

  // 处理相机位置变化事件
  if (eventType === "CameraChanged" || eventType === "CameraStopMove") {
    // 获取当前相机位置
    unrealApi?.camera.get((location) => {
      if (location) {
        const newHeight = location.z;
        // 检查高度是否有显著变化（避免微小变化触发）
        if (Math.abs(newHeight - currentCameraHeight) > 1000) {
          console.log("相机高度发生显著变化:", newHeight);
          currentCameraHeight = newHeight;

          // 根据高度执行不同操作
          if (newHeight < 60000) {
            // 隐藏g109流光线
            unrealApi?.infoTree.hide("E66CA48345644A6F9D43D281BDF0A339");
            // 项目驻地标记
            unrealApi?.infoTree.hide("C6304A634B5EE6A06845C3B3DA5DAFBC");
          } else {
            // 显示g109流光线
            unrealApi?.infoTree.show("E66CA48345644A6F9D43D281BDF0A339");
            // 项目驻地标记
            unrealApi?.infoTree.show("C6304A634B5EE6A06845C3B3DA5DAFBC");
          }

          // 发出高度变化事件，供其他组件使用
          emitter.$emit("cameraHeightChanged", {
            height: newHeight,
            previousHeight: currentCameraHeight,
          });
        }
      }
    });
  } else if (eventType === "CameraMoving") {
    console.log("相机正在移动", event);
  }
};

// 重置相机到初始位置
const resetCamera = () => {
  setCameraPosition(unrealConfig.initialCameraPosition);
};

// 设置相机位置
const setCameraPosition = (cameraPosition) => {
  if (!unrealApi || !engineStatus.value.initialized) {
    console.warn("Unreal引擎未初始化，无法设置相机位置");
    return false;
  }

  try {
    // 检查传入的参数格式是否正确
    if (!cameraPosition || !cameraPosition.coordinate) {
      console.error("相机位置参数格式不正确");
      return false;
    }

    console.log("cameraPosition", cameraPosition);

    const { coordinate, fOV, pitch, orientation, flyTime } = cameraPosition;

    // 调用引擎API设置相机位置
    unrealApi.camera.lookAt(
      coordinate.x,
      coordinate.y,
      coordinate.z,
      0,
      pitch || 0,
      orientation || 0,
      flyTime || 2
    );

    return true;
  } catch (error) {
    console.error("设置相机位置失败:", error);
    return false;
  }
};

// 视频流地址
const host = "*************:33470";
// const host = 'iot-render.ccccltd.cn:8087';
// 初始化 Unreal Engine
const init = async (options = {}) => {
  console.log("unreal 引擎初始化...", options);
  const finalOptions = {
    // 必选参数，网页显示视频流的domId
    domId: "player",
    // 实例id
    iid: "1781012366194",
    // 必选参数，二次开发时必须指定，否则无法进行二次开发
    apiOptions: {
      // 必选参数，与云渲染主机通信成功后的回调函数 注意：只有在onReady之后才可以调用DigitalTwinAPI接口
      onReady: onReady,
      onEvent: onEvent,
    },
    ui: {
      startupInfo: false, // 可选参数，是否显示页面加载详细信息，默认值false
      statusButton: false, // 可选参数，是否显示状态按钮，默认false
      mainUI: false,
    },
    // useHttps: true, // 可选参数，是否使用https，默认false
    // 可选参数，设置三维交互的键盘事件接收者 注意：接收类型有视频标签(video)，网页文档(document)，空(none)
    keyEventTarget: "none",
    ...options,
  };
  if (engineStatus.value.initialized) return unreal;
  // 构造player对象
  unreal = new window.DigitalTwinPlayer(host, finalOptions);
  unreal.setActionEventEnabled(false);
  // 构造DigitalTwinAPI对象并初始化
  unrealApi = unreal.getAPI();
};

// 对外暴露方法
defineExpose({
  init,
  resetCamera,
  setCameraPosition,
});
</script>

<style lang="scss">
.unreal-engine {
  width: 100%;
  height: 100%;
}

.marker-info-container {
  position: absolute;
  bottom: 300px;
  right: 20%;
  z-index: 1000;
  pointer-events: auto;
}
</style>

<template>
  <div class="nav-sidebar">
    <div
      v-for="(item, index) in navItems"
      :key="item.value || index"
      :class="[
        'nav-item',
        {
          active: activeValue === item.value || isParentActive(item),
          disabled: item.disabled,
        },
      ]"
      @click="handleNavClick(item, index)"
      @mouseenter="handleMouseEnter(item)"
      @mouseleave="handleMouseLeave"
    >
      <div class="nav-item-content">
        <div class="nav-text">{{ item.text }}</div>
      </div>
      <div class="nav-divider"></div>

      <!-- 二级菜单 -->
      <div
        v-if="
          item.children &&
          item.children.length > 0 &&
          hoveredItemKey === item.value
        "
        class="sub-menu"
      >
        <div
          v-for="(subItem, subIndex) in item.children"
          :key="subItem.value || subIndex"
          :class="[
            'sub-menu-item',
            {
              active: activeValue === subItem.value,
            },
          ]"
          @click.stop="handleSubNavClick(subItem, item, subIndex)"
        >
          {{ subItem.text }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";

// 定义props
const props = defineProps({
  // 导航项数据
  navItems: {
    type: Array,
    required: true,
    default: () => [],
  },
  // 使用v-model绑定的当前选中项
  modelValue: {
    type: Object,
    default: () => ({}),
  },
});

// 定义emit，添加update:modelValue事件
const emit = defineEmits(["nav-click", "update:modelValue"]);

// 响应式状态
const activeValue = ref(
  props.modelValue?.value || (props.navItems[0] && props.navItems[0].value) || ""
);
const hoveredItemKey = ref(null);
const activeParent = ref(null);

// 处理导航点击
const handleNavClick = (item, index) => {
  // 如果导航项被禁用，则不执行任何操作
  if (item.disabled) {
    return;
  }

  // 如果有子菜单，不执行选中操作
  if (item.children && item.children.length > 0) {
    return;
  }

  // 更新当前选中的导航项
  activeValue.value = item.value;
  activeParent.value = null;

  // 触发v-model更新
  emit("update:modelValue", item);
  
  // 触发导航项点击事件
  emit("nav-click", item, index);
};

// 处理子菜单项点击
const handleSubNavClick = (subItem, parentItem, subIndex) => {
  activeValue.value = subItem.value;
  activeParent.value = parentItem;

  // 触发v-model更新，包含父菜单信息
  emit("update:modelValue", { ...subItem, parent: parentItem });
  
  // 触发导航项点击事件，传递子菜单信息
  emit("nav-click", subItem, subIndex, parentItem);
};

// 处理鼠标进入
const handleMouseEnter = (item) => {
  if (item.children && item.children.length > 0) {
    hoveredItemKey.value = item.value;
  }
};

// 处理鼠标离开
const handleMouseLeave = () => {
  hoveredItemKey.value = null;
};

// 判断是否为当前选中子菜单的父菜单
const isParentActive = (item) => {
  if (!item.children || item.children.length === 0) {
    return false;
  }

  // 检查是否有子菜单被选中
  return (
    item.children.some((child) => child.value === activeValue.value) ||
    activeParent.value === item
  );
};
</script>

<style scoped lang="scss">
.nav-sidebar {
  display: inline-block; // 修改为inline-block以允许内容撑开宽度
  min-width: fit-content; // 使用fit-content确保宽度至少与内容一样宽
  color: #fff;
  font-size: 16px;
}

.nav-item {
  position: relative;
  cursor: pointer;
  transition: all 0.3s;
  white-space: nowrap; // 防止文本换行
  &:first-child {
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      padding: 0 2px;
      background: linear-gradient(
        270deg,
        rgba(7, 136, 255, 0) 0%,
        rgba(6, 106, 255, 0.16) 30%,
        #0587ff 52%,
        rgba(4, 89, 255, 0.18) 74%,
        rgba(3, 149, 255, 0.01) 100%
      );
    }
  }
}

.nav-item-content {
  padding: 7px 33px;
  display: flex;
  align-items: center;
  justify-content: center;
  .nav-text {
    font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
    font-size: 14px;
    color: #ffffff;
  }
}

.nav-icon {
  margin-right: 8px;
}

.nav-divider {
  position: relative;
  height: 1px;
  padding: 0 2px;
  background: linear-gradient(
    270deg,
    rgba(7, 136, 255, 0) 0%,
    rgba(6, 106, 255, 0.16) 30%,
    #0587ff 52%,
    rgba(4, 89, 255, 0.18) 74%,
    rgba(3, 149, 255, 0.01) 100%
  );

  &::before,
  &::after {
    content: "";
    position: absolute;
    width: 3px;
    height: 1px;
    background: #0567fa;
    top: 0;
  }

  &::before {
    left: 0;
  }

  &::after {
    right: 0;
  }
}

.nav-item.active {
  height: 31px;
  background: linear-gradient(
    270deg,
    rgba(3, 134, 255, 0) 0%,
    rgba(5, 135, 255, 0.58) 40%,
    rgba(6, 150, 255, 0.6) 43%,
    rgba(6, 135, 255, 0.61) 47%,
    rgba(7, 151, 255, 0) 100%
  );

  .nav-item-content {
    .nav-text {
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-size: 14px;
      color: #ffffff;
      text-shadow: 0px 1px 2px rgba(0, 253, 255, 0.5);
    }
  }
}

.nav-item:hover:not(.disabled) {
  background: linear-gradient(
    270deg,
    rgba(3, 134, 255, 0) 0%,
    rgba(5, 135, 255, 0.58) 40%,
    rgba(6, 150, 255, 0.6) 43%,
    rgba(6, 135, 255, 0.61) 47%,
    rgba(7, 151, 255, 0) 100%
  );
}

.nav-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 二级菜单样式 */
.sub-menu {
  position: absolute;
  top: 0;
  left: 100%;
  min-width: 120px;

  // border: 1px solid rgba(5, 135, 255, 0.3);
  // border-radius: 4px;
  // z-index: 10;
  padding: 12px 0;

  // width: 78px;
  // height: 99px;
  background: linear-gradient(
    270deg,
    #13335c 0%,
    #13335c 18%,
    #13335c 85%,
    #13335c 100%
  );
  border-radius: 4px;
  border: 1px solid;
  border-image: linear-gradient(
      90deg,
      rgba(7, 131, 250, 0),
      rgba(10, 135, 255, 1),
      rgba(7, 131, 250, 1),
      rgba(7, 131, 250, 1),
      rgba(7, 131, 250, 0)
    )
    1 1;
}

.sub-menu-item {
  margin-bottom: 12px;
  padding: 0 16px;
  cursor: pointer;
  transition: all 0.3s;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-size: 12px;
  color: #c5cdd4;

  &:last-child {
    margin-bottom: 0;
  }

  &:hover {
    color: #0783fa;
  }

  &.active {
    color: #0783fa;
  }
}
</style>

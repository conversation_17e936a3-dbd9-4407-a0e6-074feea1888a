<template>
  <div class="app-container" id="app-container">
    <router-view></router-view>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted } from "vue";
import autofit from "autofit.js";

// autofit配置
let autofitInstance = null;

onMounted(() => {
  // 初始化autofit
  autofitInstance = autofit.init({
    el: "#app-container", // 容器元素
    dw: 4900,
    dh: 2180,
    el: "#large-screen",
    resize: true,
    scale: "width", // 新增比例模式配置
    // transition: 300  // 可选：添加过渡动画
  });
});

onUnmounted(() => {
  // 销毁autofit实例
  if (autofitInstance) {
    autofitInstance.destroy();
  }
});
</script>

<style>
html,
body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.app-container {
  position: relative;
  width: 4900px; /* 设计稿宽度 */
  height: 2180px; /* 设计稿高度 */
  transform-origin: 0 0;
  overflow: hidden;
}
</style>

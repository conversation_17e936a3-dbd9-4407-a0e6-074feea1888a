<template>
	<div class="basic-page-header">
		<div class="basic-page-header-left">
			<!-- <slot name="left"></slot> -->
			<div class="basic-page-header-title">
				<div class="basic-page-header-title-main">
					{{ title }}
				</div>
				<div class="basic-page-header-title-translation">
					{{ translation }}
				</div>
			</div>
		</div>
		<div class="basic-page-header-mid">
			<slot name="mid"></slot>
		</div>
		<div class="basic-page-header-right">
			<slot name="right"></slot>
		</div>
	</div>
</template>

<script setup>
defineProps({
	title: {
		type: String,
		default: "智慧公路数字大屏系统",
	},
	translation: {
		type: String,
		default: "Smart Highway Data Visualization System",
	},
});
</script>

<style lang="scss" scoped>
.basic-page-header {
	position: relative;
	padding: 0;
	width: 100%;
	height: 168px;
	display: flex;
	align-items: flex-start;
	justify-content: space-between;
	background: var(--top-bg-url) no-repeat;
	background-size: calc(675 / 1920 * 100%) 150%;
	box-sizing: border-box;
	z-index: 4;
	&-left {
		flex: 0 1 auto; // 自适应宽度
		display: flex;
		min-width: 100px; // 设置最小宽度
	}
	&-title {
		padding-top: 8px;
		padding-left: 50px;
		display: flex;
		flex-direction: column;
		align-items: start;
		background-image: linear-gradient(var(--header-title-text-color) 0%, #fcfdfd 25%);
		background-clip: text;
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		font-family: Alibaba PuHuiTi;
		font-weight: 700;
		&-main {
			font-size: 34px;
			letter-spacing: 6px;
		}
		&-translation {
			font-size: 16px;
			letter-spacing: 1px;
		}
	}

	&-right {
		flex: 0 1 auto; // 自适应宽度
		display: flex;
		min-width: 100px; // 设置最小宽度
		margin-right: 24px;
		padding: 0 10px;
	}
}
</style>

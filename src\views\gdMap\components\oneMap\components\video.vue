<template>
  <div class="video-carousel">
    <div class="video-wrapper">
      <video
        v-if="currentVideo"
        ref="videoRef"
        :src="currentVideo"
        :controls="false"
        @ended="onEnded"
      ></video>
      <div class="play-btn" v-if="!isPlaying" @click.stop="handleFullscreen">
        <img :src="playIcon" alt="play" />
      </div>
      <!-- <div class="pause-btn" v-if="isPlaying" @click.stop="togglePlay">
        <img :src="pauseIcon" alt="pause" />
      </div> -->
      <div class="arrow left" @click.stop="prevVideo">
        <img :src="currentIndex === 0 ? Lnbg : Lbg" alt="prev" />
      </div>
      <div class="arrow right" @click.stop="nextVideo">
        <img :src="currentIndex === props.videoList.length - 1 ? Rnbg : Rbg" alt="next" />
      </div>
      <div class="fullscreen-btn" @click="handleFullscreen">
        <img :src="maximize" alt="maximize" />
      </div>
    </div>
    <!-- <div class="video-title">{{ currentVideo.title }}</div> -->
  </div>

  <MediaPlayer ref="MediaPlayerRef" title="工艺模拟" />
</template>

<script setup>
import { ref, computed, watch } from "vue";
import playIcon from "@/assets/images/people/play.svg";
import pauseIcon from "@/assets/images/people/pause.svg";
import Lbg from "@/assets/images/people/l-bg.svg";
import Rbg from "@/assets/images/people/r-bg.svg";
import Lnbg from "@/assets/images/people/l-nbg.svg";
import Rnbg from "@/assets/images/people/r-nbg.svg";
import maximize from "@/assets/images/people/maximize.svg";
import MediaPlayer from "./MediaPlayer.vue";
const props = defineProps({
  videoList: {
    type: Array,
    default: () => [
      // { src: 'video1.mp4', title: '视频1', poster: '' },
      // { src: 'video2.mp4', title: '视频2', poster: '' },
      // { src: 'video3.mp4', title: '视频3', poster: '' }
    ],
  },
});

const currentIndex = ref(0);
const isPlaying = ref(false);
const videoRef = ref(null);
const MediaPlayerRef = ref(null);

const currentVideo = computed(() => props.videoList[currentIndex.value]);

function togglePlay() {
  if (!videoRef.value) return;
  if (isPlaying.value) {
    videoRef.value.pause();
  } else {
    videoRef.value.play();
  }
  isPlaying.value = !isPlaying.value;
}

function prevVideo() {
  if (currentIndex.value === 0) return;
  currentIndex.value--;
  resetAndPause();
}
function nextVideo() {
  if (currentIndex.value === props.videoList.length - 1) return;
  currentIndex.value++;
  resetAndPause();
}
function resetAndPause() {
  isPlaying.value = false;
  if (videoRef.value) {
    videoRef.value.pause();
    videoRef.value.currentTime = 0;
  }
}
function onEnded() {
  isPlaying.value = false;
}

function handleFullscreen() {
  // const video = videoRef.value;
  // if (video) {
  // 	if (video.requestFullscreen) {
  // 		video.requestFullscreen();
  // 	} else if (video.webkitRequestFullscreen) {
  // 		video.webkitRequestFullscreen();
  // 	} else if (video.mozRequestFullScreen) {
  // 		video.mozRequestFullScreen();
  // 	} else if (video.msRequestFullscreen) {
  // 		video.msRequestFullscreen();
  // 	}
  // }
  const fileList = props.videoList.map((url, index) => ({ type: "video", url, index }));
  MediaPlayerRef.value.open(fileList, currentIndex.value);
}

watch(currentIndex, () => {
  resetAndPause();
});
</script>

<style lang="scss" scoped>
.video-carousel {
  position: relative;
  width: 100%;
  height: 100%;

  .video-wrapper {
    position: relative;
    width: 100%;
    height: 168px;

    video {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 8px;
    }

    .play-btn,
    .pause-btn {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 48px;
      height: 48px;
      cursor: pointer;
      z-index: 2;
    }

    .arrow {
      position: absolute;
      top: 50%;
      width: 24px;
      height: 24px;
      transform: translateY(-50%);
      cursor: pointer;
      z-index: 2;

      &.left {
        left: 8px;
      }

      &.right {
        right: 8px;
      }

      img {
        width: 100%;
        height: 100%;
      }
    }

    .arrow.disabled {
      pointer-events: none;
      opacity: 0.4;
    }

    .fullscreen-btn {
      position: absolute;
      right: 8px;
      bottom: 8px;
      width: 14px;
      height: 14px;
      cursor: pointer;
      z-index: 3;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .video-title {
    margin-top: 8px;
    text-align: center;
    color: #fff;
    font-size: 14px;
  }
}
</style>

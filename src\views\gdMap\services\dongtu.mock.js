import axios from 'axios';

export async function getProjectStat() {
  const response = await axios.get('/mock/dongtu/projectStat.json');
  return response.data;
}

export async function getAlarmStat() {
  const response = await axios.get('/mock/dongtu/alarmStat.json');
  return response.data;
}

export async function getMeasurePoint() {
  const response = await axios.get('/mock/dongtu/measurePoint.json');
  return response.data;
}

export async function getPointInfo() {
  const response = await axios.get('/mock/dongtu/pointInfo.json');
  return response.data;
}

export async function getPersonPatrol() {
  const response = await axios.get('/mock/dongtu/personPatrol.json');
  return response.data;
}

export async function getEarlyWarning() {
  const response = await axios.get('/mock/dongtu/earlyWarning.json');
  return response.data;
}

export async function getMonitorOverview() {
  const response = await axios.get('/mock/dongtu/monitorOverview.json');
  return response.data;
}

export async function getMonitorRank() {
  const response = await axios.get('/mock/dongtu/monitorRank.json');
  return response.data;
}

export async function getTimeCurve() {
  const response = await axios.get('/mock/dongtu/timeCurve.json');
  return response.data;
}

export async function getDataOverview() {
  const response = await axios.get('/mock/dongtu/dataOverview.json');
  return response.data;
}

// 测点列表数据
export async function getPointSelect() {
  const response = await axios.get('/mock/dongtu/pointSelect.json');
  return response.data;
}

<template>
  <div class="report-container">
    <a-trigger
      trigger="click"
      v-model="visible"
      :unmount-on-close="false"
      :popup-translate="[104, -10]"
      :click-outside-to-close="false"
      @popup-visible-change="handlePopupVisibleChange"
    >
      <div class="report-trigger">
        <img src="@/assets/images/report.png" alt="report" />
        <p class="report-title">汇报</p>
      </div>
      <!-- <template #content>
        <div class="demo-basic">
          <a-empty />
        </div>
      </template> -->
      <template #content>
        <a-carousel
          class="report-carousel"
          :default-current="1"
          @change="handleChange"
          show-arrow="hover"
        >
          <a-carousel-item v-for="(item, index) in renderList" :key="index">
            <div class="page-container">
              <div
                class="report-item"
                v-for="it in item"
                :key="it.id"
                @click="selected(it)"
                :class="{ 'report-item-selected': selectedItem?.id === it.id }"
              >
                <img :src="it.image" alt="report" />
                <p class="report-item-title">{{ it.name }}</p>
                <div class="report-item-play" v-show="selectedItem?.id === it.id" @click="play(it)">
                  <icon-play-arrow v-if="count % 2 === 0" />
                  <icon-pause v-else />
                </div>
              </div>
            </div>
          </a-carousel-item>
        </a-carousel>
      </template>
    </a-trigger>
  </div>
</template>

<script setup>
import { ref, computed, onBeforeUnmount, onMounted, nextTick } from 'vue';
import emitter from '@/utils/emitter';
import { IconPlayArrow, IconPause } from '@arco-design/web-vue/es/icon';

const list = ref([]);
const visible = ref(false);
const selectedItem = ref(null);
const renderList = computed(() => {
  // 将list转为四个一组
  const result = [];
  for (let i = 0; i < list.value.length; i += 4) {
    result.push(list.value.slice(i, i + 4));
  }
  return result;
});

const selected = item => {
  if (selectedItem.value?.id === item.id) {
    return;
  }
  // 停止上一个动画
  window.unrealApi?.camera.stopAnimation();
  selectedItem.value = item;
  count.value = 0;
};

const count = ref(0);

const resetCount = () => {
  count.value = 0;
};

const play = item => {
  console.log(item);
  count.value++;
  if (count.value == 1) {
    window.unrealApi?.camera.playAnimation(item.id);
  } else if (count.value % 2 === 0) {
    window.unrealApi?.camera.pauseAnimation();
  } else {
    window.unrealApi?.camera.resumeAnimation();
  }
};

const getReport = () => {
  console.log('getReport');
  window.unrealApi?.camera.getAnimationList(val => {
    console.log('动画列表', val);
    if (val.result === 0) {
      list.value = val.data;
      // 获取缩略图
      list.value.forEach(item => {
        window.unrealApi?.camera.getAnimationImage(item.name, val => {
          item.image = val.image ? `data:image/png;base64,${val.image}` : '';
        });
      });
    }
  });
};

const handlePopupVisibleChange = visible => {
  // if (visible) {
  //   getReport();
  // } else {
  //   window.unrealApi?.camera.stopAnimation();
  //   selectedItem.value = null;
  //   count.value = 0;
  // }
};

onMounted(() => {
  emitter.$on('unrealReady', getReport);
  emitter.$on('cameraTourFinished', resetCount);

  if (window.unrealApi?.camera) {
    getReport();
  }
});

onBeforeUnmount(() => {
  emitter.$off('unrealReady', getReport);
  emitter.$off('cameraTourFinished', resetCount);
});

const handleChange = current => {
  console.log(current);
};
</script>

<style lang="scss">
.report-container {
  .report-trigger {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    img {
      width: 41px;
      height: 44px;
    }
  }
  .report-title {
    width: 55px;
    margin-top: 2px;
    font-size: 14px;
    font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
    text-shadow: 0px 0px 9px #158eff;
    letter-spacing: 1.6px;
    color: #ffffff;
    text-align: center;
  }
}
.report-carousel {
  width: 260px;
  height: 170px;
  color: #fff;
  border-radius: 4px;
  border: 1px solid #58a1e7;
  background: rgba(19, 51, 92, 0.6);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  .page-container {
    box-sizing: border-box;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 66px);
    gap: 6px;
    padding: 12px 12px 20px;
    .report-item {
      position: relative;
      width: 100%;
      border-radius: 4px;
      cursor: pointer;
      box-sizing: border-box;
      &-selected {
        border: 1px solid #ffffff;
      }
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 4px;
      }
      &-title {
        position: absolute;
        bottom: 0;
        left: 0;
        height: 18px;
        line-height: 14px;
        text-align: left;
        background: rgba(0, 0, 0, 0.5);
        color: #fff;
        font-size: 12px;
        box-sizing: border-box;
        padding: 2px 4px;
        border-radius: 2px;
      }
      &-play {
        position: absolute;
        top: 42%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 24px;
      }
    }
  }
}
</style>

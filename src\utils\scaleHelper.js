/**
 * 尺寸换算工具
 * 帮助开发者在设计稿尺寸(4900×2180)和开发尺寸(1920×1080)之间转换
 */

// 设计稿尺寸
const DESIGN_WIDTH = 4900;
const DESIGN_HEIGHT = 2180;

// 开发尺寸
const DEV_WIDTH = 1920;
const DEV_HEIGHT = 1080;

// 比例系数
const SCALE_X = DESIGN_WIDTH / DEV_WIDTH; // ≈ 2.552
const SCALE_Y = DESIGN_HEIGHT / DEV_HEIGHT; // ≈ 2.019

/**
 * 将开发尺寸转换为设计稿尺寸
 * @param {number} width - 开发宽度
 * @param {number} height - 开发高度
 * @returns {Object} 设计稿尺寸
 */
export function devToDesign(width, height) {
  return {
    width: width * SCALE_X,
    height: height * SCALE_Y,
  };
}

/**
 * 将设计稿尺寸转换为开发尺寸
 * @param {number} width - 设计稿宽度
 * @param {number} height - 设计稿高度
 * @returns {Object} 开发尺寸
 */
export function designToDev(width, height) {
  return {
    width: width / SCALE_X,
    height: height / SCALE_Y,
  };
}

/**
 * 生成组件样式，用于在设计稿环境中正确显示按开发尺寸设计的组件
 * @param {number} width - 开发宽度
 * @param {number} height - 开发高度
 * @returns {Object} 样式对象
 */
export function getComponentStyle(width, height) {
  return {
    width: `${width}px`,
    height: `${height}px`,
    transform: `scale(${SCALE_X}, ${SCALE_Y})`,
    transformOrigin: "0 0",
  };
}

export default {
  devToDesign,
  designToDev,
  getComponentStyle,
  SCALE_X,
  SCALE_Y,
};

<template>
	<v-chart class="chart" :option="option" autoresize />
</template>

<script setup>
import VChart from "vue-echarts";
import dayjs from "dayjs";

const props = defineProps({
	data: {
		type: Array,
		default: () => [],
	},
});

const option = ref(null);

onMounted(() => {
	nextTick(() => {
		setOptions();
	});
});

watch(
	() => props.data,
	() => setOptions()
);

const setOptions = () => {
	option.value = {
		tooltip: {
			trigger: "axis",
			backgroundColor: "rgba(0,0,0,0.7)",
			borderColor: "#3A6B8A",
			textStyle: { color: "#C4E5FF" },
		},
		legend: {
			data: ["计划", "实际"],
			right: 20,
			top: 10,
			icon: "rect",
			itemWidth: 6,
			itemHeight: 6,
			itemGap: 20,
			// itemStyle:{
			//     borderWidth: 0,
			//     // borderColor: '#3A6B8A',
			// },

			textStyle: { color: "#C4E5FF", fontSize: 12 },
		},
		grid: {
			left: 10,
			right: 20,
			top: 40,
			bottom: 10,
			containLabel: true,
		},
		xAxis: {
			type: "category",
			axisLine: { lineStyle: { color: "#5E738F" } },
			axisLabel: { color: "#C4E5FF", fontSize: 10 },
			axisTick: { show: false },
			data: props.data.map(({ date }) => dayjs(date).format("MM-DD")),
		},
		yAxis: {
			type: "value",
			// min: 0,
			// max: 150,
			scale: true,
			splitNumber: 5,
			axisLine: { show: false },
			splitLine: { lineStyle: { color: "#244A6A", type: "dashed" } },
			axisLabel: { color: "#C4E5FF", fontSize: 10 },
		},
		dataZoom: [
			{
				type: "inside",
				xAxisIndex: 0,
				zoomLock: true,
				// 显示最后11天
				start: props.data.length > 11 ? ((props.data.length - 11) / props.data.length) * 100 : 0,
				end: 100,
			},
		],
		series: [
			{
				name: "计划",
				type: "line",
				symbol: "circle",
				symbolSize: 5,
				lineStyle: {
					color: "#38E6FD",
					width: 2,
					shadowColor: "#38E6FD",
					shadowBlur: 12,
					shadowOffsetY: 4,
					shadowOffsetX: 0,
				},
				itemStyle: { color: "#38E6FD", borderColor: "#38E6FD", borderWidth: 2 },
				data: props.data.map(({ planRemainingNum }) => planRemainingNum),
			},
			{
				name: "实际",
				type: "line",
				symbol: "circle",
				symbolSize: 5,
				lineStyle: {
					color: "#4D6FFF",
					width: 2,
					shadowColor: "#4D6FFF",
					shadowBlur: 12,
					shadowOffsetY: 4,
					shadowOffsetX: 0,
				},
				itemStyle: { color: "#4D6FFF", borderColor: "#4D6FFF", borderWidth: 2 },
				data: props.data.map(({ actualRemainingNum }) => actualRemainingNum),
			},
		],
	};
};
</script>

<style lang="scss" scoped>
.chart {
	width: 100%;
	height: 100%;
	min-height: 0;
}
</style>

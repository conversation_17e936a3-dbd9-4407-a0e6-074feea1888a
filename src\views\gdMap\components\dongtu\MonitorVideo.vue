<template>
  <div class="left-card">
    <m-card title="监控视频" :height="238" :isRight="true">
      <template #extra>
        <m-select class="monitor-select" :options="pointOptions" :selected="selectedPonit" />
      </template>
      <div class="monitor-video">
        <div id="mse"></div>
      </div>
    </m-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import Player from 'xgplayer';
import mCard from '@/components/mCard/index.vue';
import mSelect from '@/components/mSelect/index.vue';
import 'xgplayer/dist/index.min.css';
import { getPointSelect } from '@/views/gdMap/services/dongtu.mock.js';

const pointOptions = ref([]);
const selectedPonit = ref('');

onMounted(async () => {
  const pointList = await getPointSelect();
  pointOptions.value = pointList.list;
  selectedPonit.value = pointList.list?.[0] || '';
  initPlayer();
});

const initPlayer = () => {
  const player = new Player({
    id: 'mse',
    autoplay: true,
    url: '/mock/dongtu/monitorVideo.mp4',
    width: 350,
    height: 195,
    loop: true, // 添加循环播放
    autoplayMuted: true,
  });
};
</script>

<style lang="scss">
.monitor-video {
  color: #fff;
  height: 100%;
  box-sizing: border-box;
  #mse {
    height: 100%;
    width: 100%;
  }
}
</style>

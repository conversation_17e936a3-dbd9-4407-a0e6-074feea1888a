<template>
	<div class="basic-card">
		<!-- 头部插槽 -->
		<template v-if="useHeaderSlot">
			<slot name="header" />
		</template>
		<div v-else class="basic-card-hd">
			<!-- 当使用整张图片作为标题栏时显示 -->
			<div v-if="headerImage" class="basic-card-hd-full-image">
				<img class="basic-card-hd-full-image-img" :src="headerImage" alt="" />
			</div>

			<!-- 当使用默认标题栏样式时显示 -->
			<template v-else>
				<!-- <div class="basic-card-hd-icon">
					<img class="basic-card-hd-icon-img" :src="iconSrc" alt="" />
				</div> -->
				<div class="basic-card-hd-bg">
					<!-- <img class="basic-card-hd-bg-img" :src="bgSrc" alt="" /> -->
				</div>
				<div class="basic-card-hd-title">{{ title }}</div>
			</template>

			<!-- <div class="saoguang">
				<img src="@/assets/images/m-card/saoguang.svg" alt="" />
			</div> -->

			<!-- 添加右侧extra插槽 -->
			<div v-if="$slots.extra" class="basic-card-hd-extra">
				<slot name="extra"></slot>
			</div>
		</div>
		<div class="basic-card-bd">
			<slot></slot>
		</div>
	</div>
</template>
<script setup>
// import iconDefault from "@/assets/modules/common/icons/card-header-icon-1.svg";
// import bgDefault from "@/assets/modules/common/bg/card-title-bg-1.svg";

const props = defineProps({
	title: {
		type: String,
		default: "标题",
	},

	// 修改图标源默认值
	// iconSrc: {
	// 	type: String,
	// 	default: iconDefault,
	// },
	// 修改背景源默认值
	// bgSrc: {
	// 	type: String,
	// 	default: bgDefault,
	// },
	// 整张标题图片
	headerImage: {
		type: String,
		default: "",
	},
	// 开启头部插槽
	useHeaderSlot: {
		type: Boolean,
		default: false,
	},
});
</script>
<style lang="scss">
.basic-card {
	display: flex;
	flex-direction: column;

	&-hd {
		display: flex;
		align-items: center;
		position: relative;
		height: var(--card-header-height);
		flex-shrink: 0; // 防止头部被压缩
		// 新增：整张图片标题样式
		&-full-image {
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
			height: 100%;
			z-index: 2;

			&-img {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
		}

		// 添加右侧extra插槽样式
		&-extra {
			position: absolute;
			right: 12px;
			top: 0;
			height: 100%;
			display: flex;
			align-items: center;
			z-index: 3;
		}

		&-icon {
			margin-left: 10px;
			width: auto;
			height: auto;
			z-index: 2;
			&-img {
				width: auto;
				height: auto;
				max-height: 30px;
				object-fit: none;
				display: block;
				filter: drop-shadow(0 0 2px rgba(64, 207, 255, 0.5));
			}
		}

		&-bg {
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
			height: 100%;
			z-index: 1;
			background-image: var(--card-header-bg);
			background-size: auto 100%;
			background-repeat: no-repeat;

			&-img {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
		}

		&-title {
			margin-left: 64px;

			font-family: Alibaba PuHuiTi;
			font-weight: 700;
			font-size: 24px;
			color: #ffffff;
			letter-spacing: 4px;
			line-height: normal;
			text-align: left;
			font-style: normal;
			text-transform: none;
			z-index: 2;
		}

		.saoguang {
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
			height: 39px;
			overflow: hidden;
			pointer-events: none;
			img {
				width: 89px;
				height: 39px;
				animation: saoguangMove 6s linear infinite;
			}
		}
	}
	&-bd {
		display: flex;
		width: 100%;
		position: relative;
		overflow: auto;
		pointer-events: all;
		margin-top: 8px;
		background: rgba(116, 25, 15, 0.4);
	}

	& + .basic-card {
		margin-top: 16px;
	}
}

@keyframes saoguangMove {
	from {
		transform: translateX(-160px);
	}
	to {
		transform: translateX(2000px);
	}
}
</style>

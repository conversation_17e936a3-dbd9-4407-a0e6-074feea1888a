<svg width="102" height="31" viewBox="0 0 102 31" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#229;&#136;&#134;&#233;&#131;&#168;&#229;&#188;&#185;&#230;&#161;&#134;">
<g id="&#229;&#188;&#185;&#230;&#161;&#134;" clip-path="url(#clip0_1129_2881)">
<rect width="102" height="31" fill="#070707" fill-opacity="0.5"/>
<g id="Union">
<mask id="path-1-inside-1_1129_2881" fill="white">
<path d="M15 2.5H2.5V28H99.5V1H102V30.5H0V2.5H-42V0H15V2.5ZM253 2.5H169V0H253V2.5Z"/>
</mask>
<path d="M15 2.5V3H15.5V2.5H15ZM2.5 2.5V2H2V2.5H2.5ZM2.5 28H2V28.5H2.5V28ZM99.5 28V28.5H100V28H99.5ZM99.5 1V0.5H99V1H99.5ZM102 1H102.5V0.5H102V1ZM102 30.5V31H102.5V30.5H102ZM0 30.5H-0.5V31H0V30.5ZM0 2.5H0.5V2H0V2.5ZM-42 2.5H-42.5V3H-42V2.5ZM-42 0V-0.5H-42.5V0H-42ZM15 0H15.5V-0.5H15V0ZM253 2.5V3H253.5V2.5H253ZM169 2.5H168.5V3H169V2.5ZM169 0V-0.5H168.5V0H169ZM253 0H253.5V-0.5H253V0ZM15 2.5V2H2.5V2.5V3H15V2.5ZM2.5 2.5H2V28H2.5H3V2.5H2.5ZM2.5 28V28.5H99.5V28V27.5H2.5V28ZM99.5 28H100V1H99.5H99V28H99.5ZM99.5 1V1.5H102V1V0.5H99.5V1ZM102 1H101.5V30.5H102H102.5V1H102ZM102 30.5V30H0V30.5V31H102V30.5ZM0 30.5H0.5V2.5H0H-0.5V30.5H0ZM0 2.5V2H-42V2.5V3H0V2.5ZM-42 2.5H-41.5V0H-42H-42.5V2.5H-42ZM-42 0V0.5H15V0V-0.5H-42V0ZM15 0H14.5V2.5H15H15.5V0H15ZM253 2.5V2H169V2.5V3H253V2.5ZM169 2.5H169.5V0H169H168.5V2.5H169ZM169 0V0.5H253V0V-0.5H169V0ZM253 0H252.5V2.5H253H253.5V0H253Z" fill="#A6BED4" fill-opacity="0.419608" mask="url(#path-1-inside-1_1129_2881)"/>
</g>
<g id="5" filter="url(#filter0_dd_1129_2881)">
<rect width="2.09409" height="18" transform="matrix(0.716302 0.69779 0 1 -2 39.5)" fill="#00D2FF"/>
</g>
<path id="4" d="M163 1H101.5" stroke="#677B83" stroke-opacity="0.509804" stroke-width="2.5" stroke-dasharray="4 1.5"/>
<g id="5_2" filter="url(#filter1_ddddd_1129_2881)">
<path d="M228 1H127.5" stroke="#93E9FF" stroke-width="2.5" stroke-dasharray="4 1.5"/>
</g>
<rect id="Rectangle 5094" width="2.09409" height="17.2473" transform="matrix(0.716302 0.69779 0 1 0.5 5.5)" fill="#00D2FF"/>
</g>
</g>
<defs>
<filter id="filter0_dd_1129_2881" x="-30.5" y="11" width="58.5" height="76.4612" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="14.25"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.870588 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1129_2881"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="14.25"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.870588 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1129_2881" result="effect2_dropShadow_1129_2881"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1129_2881" result="shape"/>
</filter>
<filter id="filter1_ddddd_1129_2881" x="99" y="-28.75" width="157.5" height="59.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="14.25"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.670588 0 0 0 0 0.933333 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1129_2881"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="14.25"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.670588 0 0 0 0 0.933333 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1129_2881" result="effect2_dropShadow_1129_2881"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="14.25"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.670588 0 0 0 0 0.933333 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_1129_2881" result="effect3_dropShadow_1129_2881"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="14.25"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.670588 0 0 0 0 0.933333 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect3_dropShadow_1129_2881" result="effect4_dropShadow_1129_2881"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="14.25"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.670588 0 0 0 0 0.933333 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect4_dropShadow_1129_2881" result="effect5_dropShadow_1129_2881"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_1129_2881" result="shape"/>
</filter>
<clipPath id="clip0_1129_2881">
<rect width="102" height="31" fill="white"/>
</clipPath>
</defs>
</svg>

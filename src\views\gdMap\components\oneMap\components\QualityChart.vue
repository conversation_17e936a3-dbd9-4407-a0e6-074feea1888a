<template>
	<div class="chart-three-container">
		<v-chart class="chart" :option="option" autoresize />
		<div class="center-value">{{ total.toLocaleString() }}</div>
		<div class="legend-container-title">已验评工程数量</div>
		<div class="legend-list">
			<div
				v-for="(item, idx) in legendList"
				:key="item.name"
				class="legend-row-bg"
				@click="toggleLegend(idx)"
			>
				<span
					class="legend-dot"
					:class="{ checked: item.checked, unchecked: !item.checked }"
					:style="item.checked ? { '--bg-color': colorList[idx] } : {}"
				></span>
				<span class="legend-label">{{ item.name }}</span>
				<span class="legend-percent">{{ item.percent }}%</span>
				<span class="legend-value">{{ item.value.toLocaleString() }}</span>
			</div>
		</div>
	</div>
</template>
<script lang="ts" setup>
import { ref, computed, watch, defineProps } from "vue";
import VChart from "vue-echarts";
import { getPie3d } from "./utils.ts";
import "echarts-gl";

const colorList = ["#4ECB73", "#f9a304", "#CB4E50"];

const props = defineProps({
	data: {
		type: Array as () => { name: string; value: number; color: string }[],
		default: () => [],
	},
});

const option = ref<any>(null);

// legendList 需要响应式，不能用 computed
const legendList = ref<any[]>([]);

const total = computed(() =>
	legendList.value.reduce((sum, item) => (item.checked ? sum + item.value : sum), 0)
);

const updateLegendList = () => {
	legendList.value = props.data.map((item) => ({
		...item,
		percent: 0, // 先占位，后面 setOptions 里再算
		checked: true,
	}));
};

const setOptions = () => {
	// 计算总数
	const sum = legendList.value.reduce((sum, item) => sum + item.value, 0);
	// 更新百分比
	legendList.value.forEach((item) => {
		item.percent = item.checked && sum ? ((item.value / sum) * 100).toFixed(0) : "0";
	});
	// 只显示 checked 为 true 的数据
	const pieData = legendList.value
		.filter((item) => item.checked)
		.map((item) => ({
			value: item.value,
			name: item.name,
			percent: item.percent,
			itemStyle: { color: item.color },
		}));
	option.value = getPie3d(pieData, 0.85, colorList);
};

const toggleLegend = (idx: number) => {
	legendList.value[idx].checked = !legendList.value[idx].checked;
	// 至少保留一个
	if (legendList.value.filter((item) => item.checked).length === 0) {
		legendList.value[idx].checked = true;
		return;
	}
	setOptions();
};

watch(
	() => props.data,
	() => {
		updateLegendList();
		setOptions();
	},
	{ immediate: true }
);
</script>
<style lang="scss" scoped>
.chart-three-container {
	width: 100%;
	height: 100%;
	position: relative;

	.chart {
		width: 50%;
		height: 100%;
		position: relative;
		z-index: 15;

		&::after {
			content: "";
			position: absolute;
			width: 122px;
			height: 81px;
			background: url(@/assets/images/people/image_185.svg) no-repeat;
			background-size: 100% 100%;
			opacity: 0.4;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
			z-index: 0;
			pointer-events: none;
		}
	}

	.legend-container-title {
		font-size: 12px;
		color: #b1d2e9;
		position: absolute;
		left: 20%;
		bottom: 9px;
		transform: translateX(-20%);
		text-align: center;
		// z-index: 16;
	}

	.legend-list {
		// width: 50%;
		// margin: 0 auto;
		// position: absolute;
		// top: 50%;
		// left: 50%;
		// transform: translate(-50%, -50%);
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translateY(-50%);
		z-index: 4;

		.legend-row-bg {
			font-size: 12px;
			color: #c4e5ff;
			min-width: 150px;
			height: 25px;
			background: rgba(0, 0, 0, 0.3);
			padding: 4px 8px;
			margin-bottom: 8px;
			display: flex;
			align-items: center;
			// justify-content: space-between;
			cursor: pointer;
			transition: background 0.2s;
		}

		.legend-dot {
			width: 6px;
			height: 6px;
			display: inline-block;
			// border-radius: 50%;
			margin-right: 8px;
			transition: border 0.2s, opacity 0.2s;
			// border: 2px solid #ccc;
			opacity: 0.3;
		}

		.legend-dot.checked {
			// border: 2px solid #29A9FF;
			opacity: 1;
			background: var(--bg-color);
		}

		.legend-dot.unchecked {
			// border: 2px solid #ccc;
			background-color: #ccc;
			opacity: 0.3;
		}

		.legend-label {
			width: 50px;
			margin-left: 8px;
			border-right: 1px solid rgba(196, 229, 255, 0.5);
		}

		.legend-percent {
			margin-left: 12px;
			// font-family: D-DIN-PRO, D-DIN-PRO;
		}

		.legend-value {
			// color: #3feafd;
			flex: 1;
			margin-left: 8px;
			text-align: right;
			font-style: normal;
			text-transform: none;
		}
	}
}

.legend-row-bg.unchecked {
	opacity: 0.5;
}

.legend-checkbox {
	width: 6px;
	height: 6px;
	// border: 1px solid #1b2527;
	// border-radius: 1px;
	margin-right: 10px;
	display: inline-block;
	position: relative;
	background: #fff;
}

.legend-checkbox.checked {
	// content: "";
	background: #35d8e5;
	// position: absolute;
	// left: 4px;
	// top: 1px;
	// width: 7px;
	// height: 12px;
	// border: solid #3feafd;
	// border-width: 0 3px 3px 0;
	// transform: rotate(45deg);
}

.text-gradient {
	background: -webkit-linear-gradient(top, #f7fdfd 0%, #36c5d2 100%);
	background: linear-gradient(to bottom, #f7fdfd 0%, #36c5d2 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

.center-value {
	position: absolute;
	left: 23%;
	top: 35%;
	transform: translate(-23%, -50%);
	z-index: 2;
	font-family: D-DIN-PRO, D-DIN-PRO;
	font-weight: 600;
	font-size: 20px;
	text-align: center;
	// font-style: normal;
	// text-transform: none;
	// text-shadow: 0px 0px 10px #29A9FF;
	background: -webkit-linear-gradient(top, #fff 0%, #29a9ff 100%);
	background: linear-gradient(to bottom, #fff 0%, #29a9ff 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}
</style>

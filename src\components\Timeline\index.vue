<template>
  <div class="milestone-timeline" ref="timelineRef" @mousedown="startDrag" @touchstart="startDrag">
    <div
      class="timeline-nodes"
      ref="nodesContainerRef"
      :style="{
        transform: `translateX(${translateX}px)`,
        transition: isDragging ? 'none' : 'transform 0.3s ease',
        width: totalWidth + 'px',
        justifyContent: 'flex-start',
      }"
    >
      <div class="timeline-track"></div>
      <div
        v-for="item in props.items"
        :key="item.id"
        class="timeline-node"
        :class="{
          active: selectedNodeId === item.id,
        }"
        :style="{ width: `${nodeWidth}px`, flex: 'none' }"
        @click="handleNodeClick(item, $event)"
      >
        <div class="node-date">{{ item.date }}</div>
        <div class="node-dot-container">
          <div class="node-dot"></div>
        </div>
      </div>
    </div>
  </div>

  <Teleport to="body">
    <transition name="popup-scroll">
      <div v-if="showPopup && selectedNode" class="timeline-node-popup" @click.stop>
        <div class="popup-content">
          <div v-if="!groupedProgressInfos.length" class="empty-state">
            <span>无节点</span>
          </div>
          <div v-else class="timeline-container" ref="timelineContainerRef">
            <div
              v-for="(group, groupIndex) in groupedProgressInfos"
              :key="groupIndex"
              class="progress-group"
              :class="{
                'last-group': groupIndex === groupedProgressInfos.length - 1,
                'past-date': isProgressDatePast(group.date),
              }"
              :ref="
                el => {
                  if (groupIndex === groupedProgressInfos.length - 1) lastGroupRef = el;
                }
              "
            >
              <div class="progress-date">{{ group.date }}</div>
              <div class="progress-items">
                <div
                  v-for="(info, infoIndex) in group.items"
                  :key="infoIndex"
                  class="progress-item"
                >
                  <div class="progress-text" :class="getStatusClass(info)">
                    {{ info.text }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </Teleport>
</template>

<script setup>
import {
  defineProps,
  defineEmits,
  ref,
  computed,
  onMounted,
  watch,
  onBeforeUnmount,
  nextTick,
} from 'vue';

// 重新引入 Teleport 组件
import { Teleport } from 'vue';

// 保持props和其他代码不变
const props = defineProps({
  // 时间轴节点数据
  items: {
    type: Array,
    required: true,
    default: () => [],
  },
  // 一次显示的节点数量
  visibleCount: {
    type: Number,
    default: 4, // 默认显示4个节点
  },
  // 当前日期，用于定位初始显示位置
  currentDate: {
    type: String,
    default: () => {
      // 默认为当前日期
      const now = new Date();
      return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(
        now.getDate()
      ).padStart(2, '0')}`;
    },
  },
  // 默认选中的节点ID
  defaultSelectedId: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['select', 'scroll']);

// 引用DOM元素
const timelineRef = ref(null);
const nodesContainerRef = ref(null);

// 拖拽相关状态
const isDragging = ref(false);
const startX = ref(0);
const translateX = ref(0);
const nodeWidth = ref(100); // 默认节点宽度
const minTranslateX = ref(0);

// 弹窗相关状态
const showPopup = ref(false);
const selectedNode = ref(null);
// 选中节点的ID
const selectedNodeId = ref('');

// 计算总宽度
const totalWidth = computed(() => {
  return props.items.length * nodeWidth.value;
});

// 计算容器宽度
const containerWidth = computed(() => {
  if (timelineRef.value) {
    return timelineRef.value.clientWidth;
  }
  return 0;
});

// 计算每个节点的宽度
const calculateNodeWidth = () => {
  if (!timelineRef.value) return;

  if (props.visibleCount && props.visibleCount > 0) {
    // 如果设置了可见数量，根据可见数量计算节点宽度
    const availableWidth = timelineRef.value.clientWidth - 48; // 24px左右padding
    nodeWidth.value = availableWidth / props.visibleCount;
  } else {
    // 默认节点宽度
    nodeWidth.value = 100;
  }

  // 计算最小translateX（最大右移距离）
  calculateMinTranslateX();
};

// 计算最小translateX（最大右移距离）
const calculateMinTranslateX = () => {
  if (!timelineRef.value) return;

  const containerWidth = timelineRef.value.clientWidth;
  const totalContentWidth = props.items.length * nodeWidth.value;

  // 如果内容宽度小于容器宽度，则不需要滚动
  if (totalContentWidth <= containerWidth) {
    minTranslateX.value = 0;
  } else {
    // 最小值为负的(总内容宽度-容器宽度)
    minTranslateX.value = -(totalContentWidth - containerWidth);
  }

  // 确保当前translateX在有效范围内
  if (translateX.value < minTranslateX.value) {
    translateX.value = minTranslateX.value;
  }
};

// 处理节点点击
const handleNodeClick = (item, event) => {
  // 更新选中节点ID
  selectedNodeId.value = item.id;

  // 如果点击的是当前选中的节点，则切换弹窗显示状态
  if (selectedNode.value && selectedNode.value.id === item.id) {
    // 切换弹窗显示状态
    showPopup.value = !showPopup.value;

    // 如果是关闭弹窗，延迟一段时间后重置selectedNode，确保关闭动画完成
    if (showPopup.value) {
      // 如果是打开弹窗，更新弹窗位置和高度
      nextTick(() => {
        updatePopupPosition();
        updateTimelineHeight();
      });
    }
  } else {
    // 如果当前有弹窗显示，先关闭它
    if (showPopup.value) {
      showPopup.value = false;
      // 延迟一段时间后再打开新弹窗，确保关闭动画完成
      setTimeout(() => {
        selectedNode.value = item;
        showPopup.value = true;
        // 在下一个事件循环中更新弹窗位置和高度
        nextTick(() => {
          updatePopupPosition();
          updateTimelineHeight();
        });
      }, 400); // 与动画持续时间相匹配
    } else {
      // 如果没有弹窗显示，直接打开新弹窗
      selectedNode.value = item;
      showPopup.value = true;
      // 在下一个事件循环中更新弹窗位置和高度
      nextTick(() => {
        updatePopupPosition();
        updateTimelineHeight();
      });
    }
  }

  emit('select', item);
};

// 新增函数：更新弹窗位置
const updatePopupPosition = () => {
  const popupEl = document.querySelector('.timeline-node-popup');
  const timelineEl = timelineRef.value;
  if (popupEl && timelineEl) {
    const timelineRect = timelineEl.getBoundingClientRect();
    popupEl.style.top = `${timelineRect.bottom - 15}px`;
    // 修改为基于时间轴居中显示
    const timelineCenter = timelineRect.left + timelineRect.width / 2;
    const popupWidth = popupEl.offsetWidth;
    popupEl.style.left = `${timelineCenter - popupWidth / 2}px`;

    // popupEl.style.left = `${timelineRect.left - 15}px`;
  }
};

// 监听弹窗显示状态变化
watch(showPopup, newVal => {
  if (newVal) {
    // 当弹窗显示时，等待DOM更新后计算高度
    nextTick(() => {
      updateTimelineHeight();

      // 为每个进度组设置动画延迟索引
      const groups = document.querySelectorAll('.progress-group');
      groups.forEach((group, index) => {
        group.style.setProperty('--index', index);
      });
    });
  }
});

// 开始拖拽
const startDrag = e => {
  // 如果内容不需要滚动，则不处理拖拽
  if (totalWidth.value <= containerWidth.value) return;

  // 如果是从弹窗开始拖拽，则不处理
  if (e.target.closest('.timeline-node-popup')) return;

  isDragging.value = true;
  startX.value = e.type === 'touchstart' ? e.touches[0].clientX : e.clientX;

  // 阻止默认行为，防止文本选择等
  e.preventDefault();

  // 添加移动和结束事件监听
  document.addEventListener('mousemove', onDrag);
  document.addEventListener('touchmove', onDrag, { passive: false });
  document.addEventListener('mouseup', endDrag);
  document.addEventListener('touchend', endDrag);
};

// 拖拽中
const onDrag = e => {
  if (!isDragging.value) return;

  // 阻止默认行为，防止页面滚动
  e.preventDefault();

  const currentX = e.type === 'touchmove' ? e.touches[0].clientX : e.clientX;
  const diff = currentX - startX.value;

  // 计算新的translateX，并限制在有效范围内
  let newTranslateX = translateX.value + diff;

  // 限制左右边界
  if (newTranslateX > 0) {
    // 添加阻尼效果，使其不能无限右拉
    newTranslateX = newTranslateX * 0.3;
  } else if (newTranslateX < minTranslateX.value) {
    // 添加阻尼效果，使其不能无限左拉
    const overScroll = minTranslateX.value - newTranslateX;
    newTranslateX = minTranslateX.value - overScroll * 0.3;
  }

  translateX.value = newTranslateX;
  startX.value = currentX;

  // 发出滚动事件
  emit('scroll', {
    position: translateX.value,
    percentage: Math.abs(translateX.value) / (totalWidth.value - containerWidth.value),
  });
};

// 结束拖拽
const endDrag = () => {
  if (!isDragging.value) return;

  isDragging.value = false;

  // 移除事件监听
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('touchmove', onDrag);
  document.removeEventListener('mouseup', endDrag);
  document.removeEventListener('touchend', endDrag);

  // 回弹到有效范围
  if (translateX.value > 0) {
    translateX.value = 0;
  } else if (translateX.value < minTranslateX.value) {
    translateX.value = minTranslateX.value;
  }
};

// 查找当前日期最接近的节点索引
const findClosestDateIndex = () => {
  if (!props.items || props.items.length === 0) return -1;

  // 将当前日期转换为时间戳
  const currentTimestamp = new Date(props.currentDate).getTime();

  // 找到日期完全匹配的节点
  const exactMatchIndex = props.items.findIndex(item => {
    return new Date(item.date).getTime() === currentTimestamp;
  });

  // 如果找到完全匹配的节点，返回该索引
  if (exactMatchIndex !== -1) {
    return exactMatchIndex;
  }

  // 如果没有完全匹配的节点，找到最接近的节点
  let closestIndex = 0;
  let minDiff = Infinity;

  props.items.forEach((item, index) => {
    const itemTimestamp = new Date(item.date).getTime();
    const diff = Math.abs(itemTimestamp - currentTimestamp);

    if (diff < minDiff) {
      minDiff = diff;
      closestIndex = index;
    }
  });

  return closestIndex;
};

// 滚动到当前日期附近的节点
const scrollToCurrentDateArea = () => {
  const closestIndex = findClosestDateIndex();
  if (closestIndex === -1) return;

  // 计算应该显示的起始节点索引
  // 如果当前日期等于某个节点，这个节点应该是第二个节点
  // 否则，显示最接近当前日期的节点及其周围的节点
  let startIndex;

  // 检查是否有完全匹配的节点
  const exactMatchIndex = props.items.findIndex(item => {
    return new Date(item.date).getTime() === new Date(props.currentDate).getTime();
  });

  if (exactMatchIndex !== -1) {
    // 如果有完全匹配的节点，将其作为第二个节点
    startIndex = Math.max(0, exactMatchIndex - 1);
  } else {
    // 否则，将最接近的节点居中显示
    startIndex = Math.max(0, closestIndex - Math.floor(props.visibleCount / 2));
  }

  // 确保不会超出数组边界
  startIndex = Math.min(startIndex, props.items.length - props.visibleCount);
  startIndex = Math.max(0, startIndex);

  // 计算目标位置
  const targetPosition = -(startIndex * nodeWidth.value);

  // 确保不超出边界
  const boundedPosition = Math.max(minTranslateX.value, Math.min(0, targetPosition));

  // 设置位置
  translateX.value = boundedPosition;
};

// 初始化选中节点
const initSelectedNode = () => {
  // 如果有默认选中的节点ID，则使用它
  if (props.defaultSelectedId) {
    selectedNodeId.value = props.defaultSelectedId;
    const defaultNode = props.items.find(item => item.id === props.defaultSelectedId);
    if (defaultNode) {
      selectedNode.value = defaultNode;
    }
  } else {
    // 否则，选择最接近当前日期的节点
    const closestIndex = findClosestDateIndex();
    if (closestIndex !== -1 && props.items[closestIndex]) {
      selectedNodeId.value = props.items[closestIndex].id;
      selectedNode.value = props.items[closestIndex];
    }
  }
};

// 监听items变化
watch(
  () => props.items,
  () => {
    calculateNodeWidth();
    // 当items变化时，初始化选中节点
    initSelectedNode();
    // 滚动到当前日期附近的节点
    setTimeout(() => {
      scrollToCurrentDateArea();
    }, 0);
  },
  { deep: true, immediate: true }
);

// 监听currentDate变化
watch(
  () => props.currentDate,
  () => {
    // 当currentDate变化时，滚动到当前日期附近的节点
    setTimeout(() => {
      scrollToCurrentDateArea();
    }, 0);
  }
);

// 监听窗口大小变化
const handleResize = () => {
  calculateNodeWidth();
  scrollToCurrentDateArea();
};

// 添加全局点击事件监听器
const handleGlobalClick = e => {
  if (
    showPopup.value &&
    !e.target.closest('.timeline-node-popup') &&
    !e.target.closest('.timeline-node')
  ) {
    showPopup.value = false;
  }
};

onMounted(() => {
  calculateNodeWidth();

  // 初始滚动到当前日期附近的节点
  setTimeout(() => {
    scrollToCurrentDateArea();
  }, 0);

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);
  // 添加全局点击事件监听
});

// 组件卸载时清理
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('touchmove', onDrag);
  document.removeEventListener('mouseup', endDrag);
  document.removeEventListener('touchend', endDrag);
  // 移除全局点击事件监听
  document.removeEventListener('click', handleGlobalClick);
});

// 添加计算属性来处理分组的进度信息
const groupedProgressInfos = computed(() => {
  if (!selectedNode.value || !selectedNode.value.progressInfos) return [];

  // 按日期分组
  const groups = {};
  selectedNode.value.progressInfos.forEach(info => {
    if (!groups[info.date]) {
      groups[info.date] = [];
    }
    groups[info.date].push(info);
  });

  // 转换为数组格式
  return Object.keys(groups).map(date => ({
    date,
    items: groups[date],
  }));
});

// 添加引用以获取时间线容器和最后一个组元素
const timelineContainerRef = ref(null);
let lastGroupRef = null;

// 更新连接线高度的函数
const updateTimelineHeight = () => {
  nextTick(() => {
    if (!timelineContainerRef.value || !lastGroupRef) return;

    const container = timelineContainerRef.value;
    const lastGroup = lastGroupRef;

    // 获取容器和最后一个组的高度
    const containerHeight = container.offsetHeight;
    const lastGroupHeight = lastGroup.offsetHeight;

    // 设置自定义属性，用于CSS变量
    container.style.setProperty(
      '--timeline-height',
      `calc(${containerHeight}px - ${lastGroupHeight}px + 36px)`
    );
  });
};

// 监听弹窗显示状态变化
watch(showPopup, newVal => {
  if (newVal) {
    // 当弹窗显示时，等待DOM更新后计算高度
    nextTick(() => {
      updateTimelineHeight();
    });
  }
});

// 监听进度信息变化
watch(
  groupedProgressInfos,
  () => {
    if (showPopup.value) {
      nextTick(() => {
        updateTimelineHeight();
      });
    }
  },
  { deep: true }
);

// 判断日期是否已过或是当天
const isPastDate = dateStr => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  let date;

  // 处理"M月D日"格式（如"7月1日"）
  if (dateStr.includes('月') && dateStr.includes('日')) {
    const currentYear = today.getFullYear();
    const month = parseInt(dateStr.split('月')[0]) - 1; // 月份从0开始
    const day = parseInt(dateStr.split('月')[1].split('日')[0]);

    // 创建日期对象
    date = new Date(currentYear, month, day);

    // 如果计算出的日期在未来，可能是去年的日期
    if (date > today && month < today.getMonth()) {
      date = new Date(currentYear - 1, month, day);
    }
  }
  // 处理"YYYY.MM"格式（如"2025.07"）
  else if (dateStr.includes('.')) {
    const parts = dateStr.split('.');
    const year = parseInt(parts[0]);
    const month = parseInt(parts[1]) - 1; // 月份从0开始

    // 创建日期对象，默认为该月第1天
    date = new Date(year, month, 1);
  }
  // 其他格式，尝试直接解析
  else {
    date = new Date(dateStr);
  }

  // 设置时分秒为0
  date.setHours(0, 0, 0, 0);

  return date <= today;
};

// 根据状态返回对应的类名
const getStatusClass = info => {
  if (info.status === 'completed' || info.status === '已完成') {
    return 'completed-text';
  } else if (info.status === 'delayed' || info.status === '滞后') {
    return 'delayed-text';
  } else if (info.status === 'pending' || info.status === '未开始') {
    return 'pending-text';
  }
  return '';
};

// 判断进度信息日期是否已过
const isProgressDatePast = dateStr => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // 查找当前日期组中的第一个进度信息
  const group = groupedProgressInfos.value.find(g => g.date === dateStr);
  if (group && group.items && group.items.length > 0) {
    // 使用第一个进度信息的fullDate字段
    const firstItem = group.items[0];
    if (firstItem.fullDate) {
      const date = new Date(firstItem.fullDate);
      date.setHours(0, 0, 0, 0);
      return date <= today;
    }
  }

  return date <= today;
};
</script>

<style lang="scss" scoped>
.milestone-timeline {
  padding: 10px 0;
  position: relative;
  height: 60px;
  overflow: hidden;
  touch-action: pan-y;
  user-select: none;

  .timeline-nodes {
    position: relative;
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 12px;
    box-sizing: border-box;
    will-change: transform;

    .timeline-track {
      position: absolute;
      left: 0;
      right: 0;
      top: 50%;
      height: 2px;
      transform: translateY(-50%);
      border-radius: 8px;
      background: #fff;
      opacity: 0.3;
      z-index: 1;
    }

    .timeline-node {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: relative;
      cursor: pointer;
      transition: all 0.3s ease;
      z-index: 2;
      height: 100%;
      overflow: visible;

      .node-date {
        position: absolute;
        top: 0;
        font-size: 14px;
        color: #ffffff;
        white-space: nowrap;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .node-dot-container {
        width: 20px;
        height: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      .node-dot {
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background: #d9d9d9;
        transition: all 0.3s ease;
      }

      // 根据日期状态设置节点颜色
      &.active {
        .node-dot {
          background: #48d6ff; // 已过日期和当前日期用蓝色
        }
      }

      &:hover {
        .node-dot {
          transform: scale(1.2);
        }
      }

      &.active {
        .node-date {
          color: #48d6ff;
          font-weight: bold;
        }

        .node-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: #48d6ff;
        }

        .node-description {
          opacity: 1;
        }
      }

      .node-description {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        font-size: 10px;
        white-space: nowrap;
        font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        color: #48d6ff;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &:hover {
        .node-dot {
          transform: scale(1.2);
        }
      }

      &.active {
        .node-date {
          color: #48d6ff;
          font-weight: bold;
        }

        .node-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: #48d6ff;
        }

        .node-description {
          opacity: 1;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.timeline-node-popup {
  position: fixed;
  z-index: 9999;
  min-width: 190px;
  max-width: 335px;
  width: auto;
  border: 1px solid;
  border-radius: 4px;
  transform-origin: center;
  will-change: transform, opacity;
  background: linear-gradient(270deg, #13335c 0%, #13335c 18%, #13335c 85%, #13335c 100%);
  border-image: linear-gradient(
      90deg,
      rgba(7, 131, 250, 0),
      rgba(10, 135, 255, 1),
      rgba(7, 131, 250, 1),
      rgba(7, 131, 250, 1),
      rgba(7, 131, 250, 0)
    )
    1 1;
  backdrop-filter: blur(8px);

  .popup-content {
    padding: 20px 16px;
    overflow: hidden;

    .timeline-container {
      position: relative;

      &::before {
        content: '';
        position: absolute;
        left: 4px;
        top: -6px;
        height: var(--timeline-height); /* 默认值为整个容器高度+12px */
        width: 1px;
        background: linear-gradient(
          180deg,
          rgba(255, 255, 255, 0) 0%,
          #ffffff 55%,
          rgba(255, 255, 255, 0) 100%
        );
        z-index: 1;
      }
    }

    .progress-group {
      margin-left: 10px;
      position: relative;
      font-size: 12px;
      z-index: 2;
      &:not(:last-child) {
        padding-bottom: 16px;
      }

      &::before {
        content: '';
        position: absolute;
        left: -10px;
        top: 4px;
        width: 9px;
        height: 9px;
        background: #707070;
        border-radius: 50%;
        border: 1px solid #ffffff;
        box-sizing: border-box;
        z-index: 2;
      }

      // 过去日期的节点点显示为蓝色
      &.past-date::before {
        background: #0783fa;
      }

      .progress-date {
        position: relative;
        margin-bottom: 6px;
        padding: 0 8px;
        font-size: 14px;
        height: 16px;
        color: #fff;
        font-family: Roboto-Bold, Roboto;
        font-weight: bold;
      }

      .progress-items {
        position: relative;
        padding-left: 8px;
      }
    }

    .progress-item {
      position: relative;
      display: flex;
      align-items: flex-start;
      padding: 2px 0;

      .progress-text {
        color: #fff;
        line-height: 1.5;
        flex: 1;
        &.completed-text {
          color: #06d239; // 已完成状态的颜色
        }

        &.delayed-text {
          color: #ffd43a; // 滞后状态的颜色
        }

        &.pending-text {
          color: #ffffff; // 未开始状态的颜色
        }
      }
    }

    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 60px;
      color: #fff;
      font-size: 14px;
      opacity: 0.7;
    }
  }
}

/* 卷轴动画效果 */
.popup-scroll-enter-active,
.popup-scroll-leave-active {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  overflow: hidden;
}

.popup-scroll-enter-from {
  opacity: 0;
  transform: scaleX(0);
  max-height: 0;
}

.popup-scroll-enter-to {
  opacity: 1;
  transform: scaleX(1);
  max-height: 1000px;
}

.popup-scroll-leave-from {
  opacity: 1;
  transform: scaleX(1);
  max-height: 1000px;
}

.popup-scroll-leave-to {
  opacity: 0;
  transform: scaleX(0);
  max-height: 0;
}

/* 内容项的级联动画 */
.progress-group {
  animation: fade-in 0.3s ease-out forwards;
  animation-delay: calc(0.05s * var(--index, 0));
  opacity: 0;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>

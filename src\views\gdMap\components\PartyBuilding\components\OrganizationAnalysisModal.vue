<template>
	<Modal ref="ModalRef" title="党组织分析-明细查询">
		<DepartmentRadioGroup v-model="department" @change="getData" />
		<div class="tab">
			<template v-for="tab in tabList" :key="tab.value">
				<div
					class="tab-item"
					:class="{ 'is-active': tab.value === activeTab }"
					@click="onClickTab(tab)"
				>
					{{ tab.label }}
				</div>
			</template>
		</div>
		<a-table
			class="table"
			:columns="columns"
			:data="tableData"
			:scroll="{ x: '100%', y: '100%' }"
			:pagination="false"
		/>
	</Modal>
</template>

<script setup>
import request from "@/utils/request";
import Modal from "./Modal.vue";
import DepartmentRadioGroup from "./DepartmentRadioGroup.vue";

const tabList = [
	{ label: "全系统", value: "全系统" },
	// { label: "境内党组织", value: "境内党组织" },
	{ label: "其他党组织", value: "其他党组织" },
];
const columns = [
	{
		title: "合计",
		dataIndex: "合计",
	},
	{
		title: "党委",
		dataIndex: "党委",
	},
	{
		title: "党总支",
		dataIndex: "党总支",
	},
	{
		title: "党支部",
		dataIndex: "党支部",
	},
];
const ModalRef = ref(null);
const department = ref("全部");
const activeTab = ref("全系统");
const tableData = ref([]);

const onOpen = () => {
	ModalRef.value?.open();
	getData();
};

const onClickTab = (tab) => {
	activeTab.value = tab.value;
	getData();
};

const getData = () => {
	const pararms = {
		department: unref(department),
		deptRelate: unref(activeTab),
	};
	request.get("/api/screen/dangjian/stat/party/detail", pararms).then((res) => {
		if (res && res.code == 200) {
			tableData.value = [res.data];
		}
	});
};

defineExpose({
	open: onOpen,
});
</script>

<style lang="scss" scoped>
.tab {
	margin-top: 26px;
}

.table {
	margin-top: 24px;
}
</style>

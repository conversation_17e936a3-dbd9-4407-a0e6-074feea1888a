<svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#230;&#182;&#181;&#230;&#180;&#158;">
<g id="Ellipse 15" filter="url(#filter0_d_505_2995)">
<circle cx="10.9912" cy="11.2998" r="8" fill="url(#paint0_radial_505_2995)" fill-opacity="0.7"/>
<circle cx="10.9912" cy="11.2998" r="7.55492" stroke="url(#paint1_linear_505_2995)" stroke-width="0.890162"/>
</g>
<circle id="Ellipse 16" cx="11.0646" cy="11.3742" r="7.07681" fill="url(#paint2_radial_505_2995)" stroke="url(#paint3_linear_505_2995)" stroke-width="0.200286"/>
<g id="&#230;&#182;&#181;&#230;&#180;&#158;_2" filter="url(#filter1_d_505_2995)">
<g id="Frame" clip-path="url(#clip0_505_2995)">
<path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M7.96179 9.86463C8.06645 9.82347 8.1647 9.77942 8.25756 9.73337C8.30901 9.7079 8.34163 9.65557 8.34163 9.59823V8.77054C8.34163 8.66124 8.22895 8.58834 8.12944 8.63326C8.04273 8.67229 7.95163 8.70943 7.85501 8.74419C7.79553 8.76565 7.756 8.82237 7.756 8.88561V9.72446C7.756 9.83062 7.86304 9.90353 7.96179 9.86463ZM10.5134 7.3C10.4048 7.31845 10.3034 7.34355 10.2077 7.37429C10.1456 7.39424 10.1038 7.45234 10.1038 7.51747V8.36398C10.1038 8.46386 10.1992 8.53526 10.2955 8.50878C10.3796 8.48569 10.4679 8.46687 10.5617 8.45307C10.6352 8.44215 10.6894 8.37866 10.6894 8.30425V7.44845C10.6894 7.35522 10.6053 7.28444 10.5134 7.3ZM11.7559 7.36965C11.6613 7.34192 11.5605 7.31908 11.4522 7.30189C11.3606 7.28733 11.2776 7.35785 11.2776 7.45058V8.30676C11.2776 8.38154 11.3324 8.44529 11.4064 8.4557C11.5013 8.46913 11.5904 8.48682 11.6746 8.50816C11.7702 8.53237 11.8632 8.4611 11.8632 8.36247V7.51383C11.8633 7.44732 11.8198 7.38822 11.7559 7.36965ZM9.27472 7.90195C9.24059 7.92739 9.20631 7.95314 9.17176 7.97909L9.17175 7.9791C9.11258 8.02355 9.05261 8.06861 8.99113 8.11376C8.97214 8.12776 8.95671 8.14602 8.94607 8.16707C8.93543 8.18813 8.92989 8.21139 8.9299 8.23498V9.02013C8.9299 9.14423 9.07169 9.21513 9.17095 9.14046L9.17919 9.13427L9.17921 9.13426C9.27108 9.06526 9.36115 8.99762 9.45216 8.93329C9.49194 8.90506 9.51553 8.85938 9.51553 8.81057V8.02241C9.51553 7.89856 9.37411 7.82779 9.27472 7.90195ZM14.7992 9.11047V9.96665C14.7992 10.0409 14.8533 10.1042 14.9267 10.1155C15.1935 10.1568 15.4939 10.1826 15.8359 10.1886C15.9198 10.1901 15.9888 10.1224 15.9889 10.0384L15.9912 9.17899C15.9915 9.09667 15.9253 9.02941 15.843 9.02803C15.5171 9.02251 15.2288 8.99905 14.9714 8.9614C14.8806 8.9481 14.7992 9.01875 14.7992 9.11047ZM12.8499 8.00192L12.8499 8.00189L12.8499 8.00188C12.7972 7.9609 12.7452 7.92036 12.693 7.88061C12.594 7.80532 12.4516 7.87635 12.4516 8.0007V8.78773C12.4516 8.83654 12.4753 8.88247 12.5152 8.91058C12.6096 8.97709 12.7014 9.04723 12.7941 9.11926C12.8932 9.19605 13.0372 9.12566 13.0372 9.00043V8.22092C13.0371 8.17424 13.0155 8.13007 12.9785 8.10159C12.935 8.06822 12.8923 8.03493 12.8499 8.00192ZM6.9952 8.95613C6.74034 8.9944 6.45713 9.01837 6.1394 9.02402C6.05721 9.02552 5.99133 9.0924 5.99133 9.17447V10.034C5.99133 10.1182 6.06035 10.1862 6.14455 10.1846C6.47921 10.1785 6.77573 10.1519 7.041 10.1097C7.11416 10.098 7.16786 10.035 7.16786 9.96087V9.10483C7.16774 9.0131 7.08605 8.94245 6.9952 8.95613ZM14.1122 8.75197C14.0153 8.71696 13.9245 8.67957 13.8385 8.64029C13.7388 8.59461 13.6253 8.66739 13.6253 8.77719V9.6035C13.6253 9.66034 13.6575 9.71217 13.708 9.73802C13.8006 9.78507 13.899 9.82999 14.0047 9.87203C14.1036 9.91131 14.2109 9.8384 14.2109 9.73199V8.89339C14.211 8.83015 14.1717 8.77343 14.1122 8.75197ZM10.9546 9.04966C10.1222 9.04966 9.67572 9.38529 9.17265 9.76348C8.57181 10.2152 7.89022 10.7276 6.37393 10.8014C6.15973 10.8118 5.99121 10.988 5.99121 11.2024V12.7601C5.99121 12.9818 6.17103 13.1616 6.39276 13.1616H9.248C9.27372 13.1616 9.28941 13.1331 9.27548 13.1114C9.06856 12.7887 8.94835 12.4039 8.94835 11.9909C8.94835 10.8508 9.86312 9.92678 10.9915 9.92678C12.1198 9.92678 13.0346 10.851 13.0346 11.9909C13.0346 12.4041 12.9144 12.7888 12.7073 13.1115C12.6934 13.1333 12.7089 13.1616 12.7347 13.1616H15.5813C15.8024 13.1616 15.9818 12.9829 15.9828 12.7618L15.9897 11.2086C15.9907 10.9933 15.8216 10.8156 15.6065 10.8057C14.067 10.7347 13.4274 10.2367 12.8525 9.78904C12.3532 9.40034 11.9029 9.04966 10.9546 9.04966ZM10.1824 12.8102C9.96782 12.5934 9.84725 12.2993 9.84725 11.9927C9.84725 11.686 9.96782 11.3919 10.1824 11.1751C10.3971 10.9583 10.6881 10.8365 10.9917 10.8365C11.2952 10.8365 11.5862 10.9583 11.8009 11.1751C12.0155 11.3919 12.136 11.686 12.136 11.9927C12.136 12.2993 12.0155 12.5934 11.8009 12.8102C11.5862 13.027 11.2952 13.1489 10.9917 13.1489C10.6881 13.1489 10.3971 13.027 10.1824 12.8102Z" fill="url(#paint4_linear_505_2995)"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_505_2995" x="0.320725" y="0.629319" width="21.341" height="21.341" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.33524"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.388235 0 0 0 0 0.640523 0 0 0 0 0.807843 0 0 0 0.51 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_505_2995"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_505_2995" result="shape"/>
</filter>
<filter id="filter1_d_505_2995" x="5.92676" y="5.71387" width="10" height="10.4451" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.445081"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.150327 0 0 0 0 0.247059 0 0 0 0 0.145098 0 0 0 0.75 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_505_2995"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_505_2995" result="shape"/>
</filter>
<radialGradient id="paint0_radial_505_2995" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(10.9912 11.2998) scale(8)">
<stop stop-color="#000101" stop-opacity="0"/>
<stop offset="1" stop-color="#07B5FA"/>
</radialGradient>
<linearGradient id="paint1_linear_505_2995" x1="10.9912" y1="19.2998" x2="10.9912" y2="3.2998" gradientUnits="userSpaceOnUse">
<stop stop-color="#07B5FA"/>
<stop offset="0.49" stop-color="#0783FA" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#0783FA" stop-opacity="0.13"/>
</linearGradient>
<radialGradient id="paint2_radial_505_2995" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(11.0646 11.3742) scale(7.17695)">
<stop stop-color="#07B5FA"/>
<stop offset="1" stop-color="#0B103B" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint3_linear_505_2995" x1="11.0646" y1="18.5512" x2="11.0646" y2="4.19727" gradientUnits="userSpaceOnUse">
<stop stop-color="#07B5FA"/>
<stop offset="0.49" stop-color="#07B5FA" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#07B5FA" stop-opacity="0.13"/>
</linearGradient>
<linearGradient id="paint4_linear_505_2995" x1="8.32951" y1="12.8226" x2="9.29237" y2="7.00743" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#07B5FA"/>
</linearGradient>
<clipPath id="clip0_505_2995">
<rect width="10" height="10" fill="white" transform="translate(5.92676 5.71387)"/>
</clipPath>
</defs>
</svg>

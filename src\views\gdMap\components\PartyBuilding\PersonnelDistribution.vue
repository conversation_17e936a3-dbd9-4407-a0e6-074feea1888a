<template>
	<BasicCard title="党员分布情况">
		<div class="personnel-distribution-card">
			<div class="card-content">
				<div class="card-content-item">
					<div class="card-content-item-label">性别结构</div>
					<div class="gender">
						<div class="gender-male">
							<div class="gender-left">
								<img src="@/assets/modules/PartyBuilding/icon/male.svg" />
							</div>
							<div class="gender-mid">
								<div class="gender-mid-header">
									<div class="gender-mid-icon" />
									<div class="gender-mid-label">男性</div>
									<div class="gender-mid-num">{{ maleData.count || 0 }}</div>
									<div class="gender-mid-unit">人</div>
								</div>
								<div
									class="gender-mid-progress"
									:style="{ '--progress': (maleData.decimal * 1 || 0) + '%' }"
								>
									<div class="gender-mid-progress-container">
										<div class="gender-mid-progress-bar"></div>
									</div>
								</div>
							</div>
							<div class="gender-right">{{ maleData.decimal * 1 || 0 }}%</div>
						</div>
						<div class="gender-female">
							<div class="gender-left">
								<img src="@/assets/modules/PartyBuilding/icon/female.svg" />
							</div>
							<div class="gender-mid">
								<div class="gender-mid-header">
									<div class="gender-mid-icon" />
									<div class="gender-mid-label">女性</div>
									<div class="gender-mid-num">{{ femaleData.count || 0 }}</div>
									<div class="gender-mid-unit">人</div>
								</div>
								<div
									class="gender-mid-progress"
									:style="{ '--progress': (femaleData.decimal * 1 || 0) + '%' }"
								>
									<div class="gender-mid-progress-container">
										<div class="gender-mid-progress-bar"></div>
									</div>
								</div>
							</div>
							<div class="gender-right">{{ femaleData.decimal * 1 || 0 }}%</div>
						</div>
					</div>
				</div>
				<div class="card-content-item">
					<div class="card-content-item-label">年龄结构</div>
					<div class="age-chart">
						<AgeChart :data="ageData" />
					</div>
				</div>
				<div class="flex-row">
					<div class="card-content-item">
						<div class="card-content-item-label">民族结构</div>
						<div class="ethnic-chart">
							<EthnicChart :data="nationData" />
						</div>
					</div>
					<div class="card-content-item flex-1">
						<div class="card-content-item-label">学历结构</div>
						<div class="education-chart">
							<EducationChart :data="educationData" />
						</div>
					</div>
				</div>
			</div>
		</div>
	</BasicCard>
</template>

<script setup>
import { usePartyBuildingStore } from "@/store";
import request from "@/utils/request";
import AgeChart from "./components/AgeChart.vue";
import EthnicChart from "./components/EthnicChart.vue";
import EducationChart from "./components/EducationChart.vue";

const PartyBuildingStore = usePartyBuildingStore();
const maleData = ref({});
const femaleData = ref({});
const ageData = ref([]);
const nationData = ref([]);
const educationData = ref([]);

watch(
	() => PartyBuildingStore.getDepartment,
	() => getData()
);

onMounted(() => {
	getData();
});

const getData = () => {
	getGenderData();
	getAgeData();
	getNationData();
	getEducationData();
};

const getGenderData = () => {
	request
		.get("/api/screen/dangjian/stat/gender", { department: PartyBuildingStore.getDepartment })
		.then((res) => {
			if (res && res.code == 200) {
				res.data.forEach((item) => {
					if (item.name == "男") {
						maleData.value = item;
					} else if (item.name == "女") {
						femaleData.value = item;
					}
				});
			}
		});
};

const getAgeData = () => {
	request
		.get("/api/screen/dangjian/stat/age", { department: PartyBuildingStore.getDepartment })
		.then((res) => {
			if (res && res.code == 200) {
				ageData.value = res.data.map(({ name, count }) => ({ name, value: count }));
			}
		});
};

const getNationData = () => {
	request
		.get("/api/screen/dangjian/stat/nation", { department: PartyBuildingStore.getDepartment })
		.then((res) => {
			if (res && res.code == 200) {
				nationData.value = res.data.map(({ name, count }) => ({ name, value: count }));
			}
		});
};

const getEducationData = () => {
	request
		.get("	/api/screen/dangjian/stat/education", { department: PartyBuildingStore.getDepartment })
		.then((res) => {
			if (res && res.code == 200) {
				educationData.value = res.data.map(({ name, count }) => ({ name, value: count }));
			}
		});
};
</script>

<style lang="scss" scoped>
.personnel-distribution-card {
	height: 604px;
	width: 100%;
}

.card-content {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
	overflow: hidden;
	row-gap: 4px;

	&-item {
		display: flex;
		flex-direction: column;
		padding: 0 12px;

		&-label {
			font-family: Alibaba PuHuiTi;
			font-size: 18px;
			color: #e4f2ff;
			padding: 8px;
		}
	}
}

.gender {
	&-male,
	&-female {
		display: flex;
		flex-direction: row;
	}

	&-female {
		margin-top: 16px;
	}

	&-left {
		img {
			width: 40px;
			height: 40px;
			margin: 4px;
		}
	}

	&-mid {
		flex: 1;
		margin-left: 12px;

		&-header {
			display: flex;
			flex-direction: row;
			align-items: center;
		}

		&-icon {
			width: 12px;
			height: 12px;
			background: #ffcc00;
			clip-path: polygon(
				50% 0%,
				63% 38%,
				100% 38%,
				69% 59%,
				82% 100%,
				50% 75%,
				18% 100%,
				31% 59%,
				0% 38%,
				37% 38%
			);
		}

		&-label,
		&-unit {
			font-family: Alibaba PuHuiTi;
			font-size: 12px;
			color: #ffffff;
			text-align: left;
		}

		&-label {
			margin-left: 8px;
		}

		&-unit {
			padding-top: 4px;
		}

		&-num {
			font-family: D-DIN-PRO;
			font-weight: bold;
			font-size: 28px;
			letter-spacing: 2px;
			background: var(--card-number-text-color);
			background-clip: text;
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			margin-left: 20px;
			padding-bottom: 4px;
		}

		&-progress {
			--progress: 50%; /* 进度百分比 */
			position: relative;

			&-container {
				width: 100%;
				height: 8px;
				background: #be4538;
				clip-path: polygon(0 0, 100% 0, calc(100% - 8px) 100%, 0 100%);
			}

			&-bar {
				width: 100%;
				height: 100%;
				background: linear-gradient(90deg, #ff3d0d 0%, #ffb400 48%, #fff600 100%);
				clip-path: polygon(
					0 0,
					var(--progress, 100%) 0,
					calc(var(--progress, 100%) - 8px) 100%,
					0 100%
				);
			}

			&::after {
				content: "";
				position: absolute;
				left: calc(var(--progress, 100%) - 9px);
				top: -2px;
				width: 18px;
				height: 12px;
				background: #fff600;
				clip-path: polygon(9px 0, 100% 0, 9px 100%, 0 100%);
			}

			&[style*="--progress: 0%"] {
				&::after {
					display: none;
				}
			}
		}
	}

	&-right {
		width: 60px;
		margin-left: 12px;
		font-family: D-DIN-PRO;
		font-weight: bold;
		font-size: 16px;
		color: #ffffff;
		letter-spacing: 1px;
		display: flex;
		align-items: end;
		justify-content: end;
	}
}

.age-chart {
	width: 100%;
	height: 198px;
}

.ethnic-chart {
	width: 140px;
	height: 140px;
	margin: auto 0;
}

.education-chart {
	width: 100%;
	height: 170px;
}

.flex-row {
	display: flex;
	flex-direction: row;
	column-gap: 48px;
}

.flex-1 {
	flex: 1;
}
</style>

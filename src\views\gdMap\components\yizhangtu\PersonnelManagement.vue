<template>
  <div class="personnel-manage-card">
    <a-spin class="card-loading" dot :loading="loading">
      <BasicCard title="人员管理">
        <div class="card-content">
          <!-- 饼状图 -->
          <div class="personnel-chart">
            <PersonnelPieChart :data="personnelData" />
          </div>

          <div class="personnel-warning">
            <div class="warning-header">
              <div class="warning-title">
                <div class="warning-icon">
                  <span class="icon-arrow">
                    <img :src="RightArrow" alt="" />
                  </span>
                  <span class="title-text">人员预警</span>
                  <span class="title-icon">
                    <img :src="PersonWarning" alt="" />
                  </span>
                </div>
                <div class="date-filter">
                  <a-dropdown trigger="click">
                    <div class="filter-btn">
                      <span class="filter-btn-text">{{ selectedDays }}天</span>
                      <span class="arrow-down">
                        <icon-caret-down />
                      </span>
                    </div>
                    <template #content>
                      <a-doption v-for="day in dayOptions" :key="day" @click="changeDays(day)">
                        {{ day }}天
                      </a-doption>
                    </template>
                  </a-dropdown>
                </div>
              </div>
            </div>
            <ScreenTable height="350px" :columns="tableColumns">
              <div ref="listContainerRef" class="scroll-container">
                <div class="warning-table">
                  <div class="table-body">
                    <div class="table-row" v-for="(item, index) in warningData" :key="index">
                      <div class="col time">{{ item.time }}</div>
                      <div class="col type" @click="locateToPosition(item)">{{ item.type }}</div>
                      <div class="col area">{{ item.area }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </ScreenTable>
          </div>
        </div>
      </BasicCard>
    </a-spin>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount } from 'vue';
import BasicCard from '@/components/BasicCard/index.vue';
import ScreenTable from '../ScreenTable.vue';
import RightArrow from '@/assets/modules/common/icons/card-header-right-arrow.svg';
import PersonWarning from '@/assets/modules/yizhangtu/icons/person-warning.svg';
import PersonnelPieChart from './components/PersonnelPieChart.vue';
import { IconCaretDown } from '@arco-design/web-vue/es/icon';
import { useMarquee } from '@/hooks/useMarquee';
import {
  getPersonnelData,
  getPersonnelWarningData,
} from '@/views/gdMap/services/yizhangtu.mock.js';
import emitter from '@/utils/emitter';

// 使用跑马灯效果
const { containerRef: listContainerRef, resetScroll } = useMarquee({
  speed: 10, // 滚动速度
  delay: 3000, // 滚动到底部后停顿时间
  step: 1, // 每次滚动的像素
});

// 选中的天数，默认为7天
const selectedDays = ref(7);
// 天数选项
const dayOptions = [3, 7, 15, 30];

// 表格列配置
const tableColumns = [
  { title: '时间', flex: 1 },
  { title: '预警类型', flex: 1, align: 'center' },
  { title: '工区', flex: 1, align: 'right' },
];

const loading = ref(false);

// 当前区域类型
const currentArea = ref('all');
const personnelData = ref({
  managerCount: 0,
  workerCount: 0,
  totalCount: null, // 如果为null则自动计算总和
});
const warningData = ref([]);

// 定位到地图上的指定位置
const locateToPosition = item => {
  if (!item || !item.coordinates || item.coordinates.length !== 2) {
    // Message.warning('该预警记录没有位置信息');
    return;
  }

  // 发送定位事件到地图引擎
  emitter.$emit('locateToPosition', {
    type: 'personnelAlert',
    data: item, // 传递完整数据，以便地图引擎可以显示更多信息
  });
};

// 加载数据的方法
const loadData = async (value = 'all') => {
  // 更新当前区域
  currentArea.value = value;

  // 重新加载数据
  loading.value = true;
  try {
    personnelData.value = await getPersonnelData(currentArea.value);

    warningData.value = await getPersonnelWarningData(selectedDays.value, currentArea.value);
  } catch (error) {
    console.error('加载数据失败:', error);
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 1000); // 延迟一点时间确保DOM已更新
  }
};

// 切换时间范围
const changeDays = async newDays => {
  selectedDays.value = newDays;
  // 重新加载数据
  loading.value = true;
  warningData.value = await getPersonnelWarningData(selectedDays.value, currentArea.value);
  // 重新加载数据
  loading.value = false;
};

// 当数据变化时，重置滚动
watch(
  warningData,
  () => {
    setTimeout(resetScroll, 300); // 延迟一点时间确保DOM已更新
  },
  { deep: true }
);

// 组件挂载时初始化数据并监听事件
onMounted(() => {
  loadData();
  emitter.$on('pageNavChange', loadData);
});

// 组件销毁前移除事件监听
onBeforeUnmount(() => {
  emitter.$off('pageNavChange', loadData);
});
</script>

<style lang="scss" scoped>
.personnel-manage-card {
  height: 350px;
}

.card-content {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.personnel-chart {
  height: 110px;
}

.personnel-warning {
  flex: 1;
  overflow: hidden;
  background: rgba(10, 35, 75, 0.6);
  border: 1px solid rgba(23, 66, 114, 0.6);
  box-sizing: border-box;
  .warning-header {
    padding-left: 12px;
    padding-right: 5px;
    display: flex;
    align-items: center;
    height: 36px;
    border-bottom: 1px solid rgba(23, 66, 114, 0.6);

    .warning-title {
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 5px;

      .warning-icon {
        display: flex;
        align-items: center;

        .icon-arrow {
          width: 24px;
          height: 16px;
          margin-right: 5px;
          img {
            width: 100%;
            height: 100%;
          }
        }

        .title-text {
          margin-right: 3px;
          font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
          font-size: 16px;
          color: #ffffff;
          text-shadow: 0px 0px 9px #158eff;
        }

        .title-icon {
          width: 16px;
          height: 16px;
          cursor: pointer;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }

  .warning-table {
    flex: 1;
    overflow-y: auto;

    .table-body {
      .table-row {
        display: flex;
        height: 28px;
        align-items: center;

        // 单行背景色
        &:nth-child(odd) {
          background-color: #091e3f;
        }

        // 双行背景色
        &:nth-child(even) {
          background-color: #142b50;
        }

        &:last-child {
          border-bottom: none;
        }

        .col {
          padding: 0 9px;
          font-size: 12px;
          color: #fff;
          font-family: Source Han Sans CN, Source Han Sans CN;
          cursor: pointer;
          &.time {
            flex: 1;
            color: #fff;
            text-align: left;
          }

          &.type {
            flex: 1;
            &:hover {
              color: #0783fa;
              font-weight: bold;
            }
          }

          &.area {
            flex: 1;
            text-align: right;
          }
        }
      }
    }
  }
}

.date-filter {
  .filter-btn {
    padding: 0 5px;
    display: flex;
    justify-content: space-between;

    width: 52px;
    height: 26px;
    line-height: 26px;
    background: #0b3567;
    box-shadow: inset 0px 0px 4px 0px #0e9aff;
    border-radius: 2px;
    border: 1px solid #1299ff;
    box-sizing: border-box;
    cursor: pointer;
    color: #ffffff;
    &-text {
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      font-size: 12px;
    }
    .arrow-down {
      width: 12px;
      height: 12px;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}

.scroll-container {
  height: 100%; // 容器需要有固定高度
  overflow: hidden; // 重要：设置为hidden，由hooks控制滚动
  position: relative; // 相对定位
}
</style>

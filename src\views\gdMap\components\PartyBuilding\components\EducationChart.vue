<template>
	<v-chart class="chart" :option="option" autoresize />
</template>

<script setup>
import * as echarts from "echarts";
import VChart from "vue-echarts";

const props = defineProps({
	data: {
		type: Array,
		default: [],
	},
});

const option = ref(null);

onMounted(() => {
	nextTick(() => {
		setOptions();
	});
});

watch(
	() => props.data,
	() => setOptions()
);

const setOptions = () => {
	const maxValue = Math.max(...props.data.map(({ value }) => value));
	option.value = {
		tooltip: {
			show: true,
			borderColor: "#FFA062",
			order: "valueDesc",
		},
		color: ["#FDFF77", "#FFA062"],
		radar: {
			scale: true,
			radius: "75%",
			startAngle: 0,
			axisName: {
				fontFamily: "Alibaba PuHuiTi",
				fontSize: 12,
				color: "#FFF9DE",
			},
			indicator: props.data.map(({ name }) => ({ name, max: maxValue })),
			axisLine: {
				lineStyle: {
					color: "#FFBF87",
					width: 0.5,
				},
			},
			splitLine: {
				lineStyle: {
					color: "#FFBF87",
					width: 2,
				},
			},
			splitArea: {
				show: false,
			},
		},
		series: [
			{
				name: "学历结构",
				type: "radar",
				symbol: "none",
				lineStyle: {
					width: 1,
				},
				data: [
					{
						value: props.data.map(({ value }) => value),
						areaStyle: {
							color: new echarts.graphic.LinearGradient(1, 1, 0, 0, [
								{ offset: 0, color: "rgba(253, 255, 119, 0)" },
								{ offset: 1, color: "rgba(253, 255, 119, 1)" },
							]),
						},
						itemStyle: {
							color: "#FFA062",
						},
					},
					// {
					// 	value: [5000, 14000, 28000, 26000, 42000, 21000],
					// 	areaStyle: {
					// 		color: new echarts.graphic.LinearGradient(1, 1, 0, 0, [
					// 			{ offset: 0, color: "rgba(255, 160, 98, 0)" },
					// 			{ offset: 1, color: "rgba(255, 160, 98, 1)" },
					// 		]),
					// 	},
					// },
				],
			},
		],
	};
};
</script>

<style lang="scss" scoped>
.chart {
	width: 100%;
	height: 100%;
}
</style>

import mapConfig from "@/config/engine/maplibre/map.config.js";

// 图层缩放级别常量
const LAYER_ZOOM_LEVELS = {
	BASE: {
		MIN: mapConfig.minzoom,
		MAX: mapConfig.maxzoom,
	},
	GLOW: {
		MIN: mapConfig.minzoom,
		MAX: mapConfig.maxzoom,
	},
};

const LINE_COLOR = {
	0: "#00FF2B",
	1: "#FADD00",
	2: "#FF0000",
	3: "#440001",
};

/**
 * 路况图层
 */
export class ConditionLayer {
	constructor(map) {
		this.map = map;
	}

	async init() {
		try {
			// 测试拥堵路线
			const response1 = await fetch("/config/engine/map/json/road/jam_test1.json");
			const geoJSON1 = await response1.json();
			this.addConditionLayer({ ...geoJSON1, id: 1, lineType: "0", direction: true });
			this.addConditionLayer({ ...geoJSON1, id: 3, lineType: "1", direction: false });
			const response2 = await fetch("/config/engine/map/json/road/jam_test2.json");
			const geoJSON2 = await response2.json();
			this.addConditionLayer({ ...geoJSON2, id: 2, lineType: "2", direction: true });
			this.addConditionLayer({ ...geoJSON2, id: 4, lineType: "3", direction: false });
		} catch (error) {}
	}

	async addConditionLayer(data) {
		if (!this.map.conditionMap) {
			this.map.conditionMap = new Map();
		}
		const { id = Date.now().toString() } = data;
		const sourceId = `condition-source-${id}`;
		const layerId = `condition-layer-${id}`;
		this.map.conditionMap.set(id, {
			sourceId,
			layerId,
			data,
		});

		this.map.addSource(sourceId, {
			type: "geojson",
			data: data,
		});

		this.map.addLayer({
			id: layerId,
			type: "line",
			source: sourceId,
			layout: {
				"line-join": "miter",
				"line-cap": "square",
			},
			paint: {
				"line-color": LINE_COLOR[data.lineType],
				"line-width": [
					"interpolate",
					["linear"],
					["zoom"], // 基于 zoom 层级
					8,
					2,
					11,
					6,
					14,
					12,
				],
				"line-offset": [
					"interpolate",
					["linear"],
					["zoom"], // 基于 zoom 层级
					8,
					data.direction ? -2 : 2,
					11,
					data.direction ? -8 : 8,
					14,
					data.direction ? -14 : 14,
				],
			},
			minzoom: LAYER_ZOOM_LEVELS.BASE.MIN + 3,
			maxzoom: LAYER_ZOOM_LEVELS.BASE.MAX,
		});
	}
}

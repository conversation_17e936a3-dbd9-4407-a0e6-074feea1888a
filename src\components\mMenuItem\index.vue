<template>
	<div
		class="m-menu-item"
		:class="{ 'is-active': index === activeIndex, 'is-disabled': props.disabled }"
		@click="handleClick"
	>
		<slot></slot>
		<!-- 添加底部指示点 -->
		<div class="menu-line"></div>
	</div>
</template>

<script setup>
import { defineComponent, inject } from "vue";
const props = defineProps({
	index: {
		type: [String, Number],
		required: true,
	},
	disabled: <PERSON><PERSON><PERSON>,
});
const updateActive = inject("updateActive");
const activeIndex = inject("activeIndex");
const isActive = () => {
	return false; // 这里需要根据实际情况来判断是否活跃
};

const handleClick = () => {
	if (props.disabled) return;
	updateActive(props.index);
};
</script>

<style lang="scss" scoped>
.m-menu-item {
	padding: 0 35px;
	position: relative;
	z-index: 1;
	display: flex;
	align-items: center;
	pointer-events: all;
	justify-content: start;
	height: 42px;
	line-height: 42px;
	cursor: pointer;
	font-family: Alibaba PuHuiTi;
	font-size: 20px;
	font-weight: 700;
	color: var(--header-menu-text-color);
	opacity: 0.5;
	letter-spacing: 2px;
	overflow: initial !important;
	.menu-line {
		position: absolute;
		bottom: -2px; // 调整位置，使其位于底部线上
		width: 0px;
		height: 4px;
		border-radius: 16px;
		background: transparent;
		transition: all 0.3s ease;
		z-index: 2; // 确保在线上方显示
	}

	&:hover:not(&.is-disabled),
	&.is-active {
		background-image: var(--header-menu-text-active-color);
		background-clip: text;
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		opacity: 1;
	}

	&.is-active {
		.menu-line {
			background: var(--header-menu-line-active-color);
			width: 26px;
		}
	}

	&.is-disabled {
		cursor: no-drop;
		opacity: 0.25;
	}
}
</style>

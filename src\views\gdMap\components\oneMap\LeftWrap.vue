<template>
	<div>
		<template v-if="headTab === 0">
			<ProjectAdv />
			<PeopleSafe />
			<Green />
		</template>
		<template v-else-if="headTab === 1 && footerTab === 0">
			<!-- <People /> -->
		</template>
	</div>
</template>

<script setup>
import emitter from "@/utils/emitter";
import ProjectAdv from "./ProjectAdv.vue";
import PeopleSafe from "./PeopleSafe.vue";
import Green from "./Green.vue";
import People from "./People.vue";

const headTab = ref(0);
const footerTab = ref(0);

onMounted(() => {
	emitter.$on("header-tab-change", handleHeadTabChange);
	// emitter.$on("footer-tab-change", handleFooterTabChange);
});

onUnmounted(() => {
	emitter.$off("header-tab-change", handleHeadTabChange);
	// emitter.$off("footer-tab-change", handleFooterTabChange);
});

const handleHeadTabChange = (value) => {
	headTab.value = value;
};

const handleFooterTabChange = (data, display) => {
	footerTab.value = data;
	if (data !== 0 && display) {
		emitter.$emit("toggleSidebars", true);
	}
};
</script>

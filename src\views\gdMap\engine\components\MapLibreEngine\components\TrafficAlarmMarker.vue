<template>
	<DefaultMarker panelTitle="拥堵预警点" :markerClass="markerClass">
		<template #icon>
			<component :is="icon"></component>
		</template>
		<template #content>
			<div class="data-list">
				<div class="data-item">
					<span class="data-key">拥堵开始时间:</span>
					<span class="data-value">
						{{ props.properties["startTime"] }}
					</span>
				</div>
				<div class="data-item">
					<span class="data-key">位置:</span>
					<span class="data-value">
						{{ props.properties["location"] }}
					</span>
				</div>
				<div class="data-item">
					<span class="data-key">拥堵持续时间:</span>
					<span class="data-value"> {{ props.properties["congestionDuration"] }}分钟 </span>
				</div>
			</div>
		</template>
	</DefaultMarker>
</template>

<script setup>
import { TrafficAlarmType } from "@/utils/dict";
import alarmBellDanger from "@/assets/modules/traffic/icon/alarmBell-danger.svg?component";
import alarmBellPrimary from "@/assets/modules/traffic/icon/alarmBell-primary.svg?component";
import alarmBellWarning from "@/assets/modules/traffic/icon/alarmBell-warning.svg?component";
import DefaultMarker from "./DefaultMarker.vue";

const props = defineProps({
	properties: Object,
});

const markerClass = computed(() => {
	const item = TrafficAlarmType.getItem(props.properties["eventType"]);
	return item?.class || "is-blue";
});

const icon = computed(() => {
	const item = TrafficAlarmType.getItem(props.properties["eventType"]);
	if (item) {
		return { alarmBellDanger, alarmBellPrimary, alarmBellWarning }[item.icon];
	}
	return null;
});
</script>

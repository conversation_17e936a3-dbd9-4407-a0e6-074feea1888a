/**
 * 城市标签图层
 */
export class CityLayer {
	constructor(map) {
		this.map = map;
		this.mainSourceId = "city-main-source";
		this.mainLabelLayerId = "city-main-label-layer";
		this.mainCircleLayerId = "city-main-circle-layer";
		this.ZOrder = 3;
		this.IdMap = new Map();
	}

	init() {
		this.loadMainCity();
	}

	async loadMainCity() {
		const response = await fetch("/config/engine/map/json/point/city-main.json");
		const geojson = await response.json();

		this.map.addSource(this.mainSourceId, {
			type: "geojson",
			data: geojson,
		});

		this.map.addLayer({
			id: this.mainLabelLayerId,
			type: "symbol",
			source: this.mainSourceId,
			layout: {
				"text-field": ["get", "name"],
				"text-anchor": ["get", "anchor"],
				"text-radial-offset": 0.5,
				"text-justify": "auto",
				"text-size": 20,
				"text-font": ["D-DIN-PRO"],
			},
			paint: {
				"text-color": "#ffffff",
			},
		});

		this.IdMap.set("main-label", { sourceId: this.mainSourceId, layerId: this.mainLabelLayerId });

		this.map.addLayer({
			id: this.mainCircleLayerId,
			type: "circle",
			source: this.mainSourceId,
			paint: {
				"circle-radius": 5, // 圆点半径
				"circle-color": "#ffffff", // 圆点颜色
			},
		});

		this.IdMap.set("main-circle", { sourceId: this.mainSourceId, layerId: this.mainCircleLayerId });
	}
}

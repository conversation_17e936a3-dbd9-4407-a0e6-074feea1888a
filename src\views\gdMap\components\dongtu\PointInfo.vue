<template>
  <div class="left-card">
    <m-card title="测点信息" :height="299" :isRight="true">
      <div class="point-info">
        <a-tree ref="treeRef" blockNode :data="treeData" :show-line="true">
          <template #switcher-icon="_, { isLeaf }">
            <icon-caret-down v-if="!isLeaf" />
          </template>
          <template #title="node, { isLeaf }">
            <span v-if="!isLeaf" class="title-text">{{ node.title }}</span>
            <template v-else>
              <span class="title-text" :style="{ color: setColor(node.status) }">{{
                node.title
              }}</span>
              <span class="status-dot" :style="{ backgroundColor: setColor(node.status) }"></span>
              <span class="title-status" :style="{ color: setColor(node.status) }">{{
                node.status == 'online' ? '在线' : '离线'
              }}</span>
            </template>
          </template>
        </a-tree>
      </div>
    </m-card>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue';
import { IconCaretDown } from '@arco-design/web-vue/es/icon';
import mCard from '@/components/mCard/index.vue';
import { getPointInfo } from '@/views/gdMap/services/dongtu.mock.js';

const treeData = ref();
const treeRef = ref();
onMounted(async () => {
  const defaultData = await getPointInfo();
  treeData.value = defaultData.treeData;
  nextTick(() => {
    treeRef.value?.expandNode(['0-0', '0-0-0'], true);
  });
});

const setColor = status => {
  return status == 'online' ? '#48D6FF' : '#FF4042';
};
</script>

<style lang="scss">
.point-info {
  color: #fff;
  height: 100%;
  padding: 16px 10px;
  box-sizing: border-box;
  overflow: auto;
  .arco-tree-node-title-block {
    overflow: hidden;
  }
  .arco-tree-node-title-text {
    display: flex;
    align-items: center;
    overflow: hidden;
  }
  .arco-tree-node-selected .arco-tree-node-title {
    color: #fff;
  }
  .arco-tree-show-line .arco-tree-node-indent-block::before {
    border-left: 1px solid rgba(255, 255, 255, 0.5);
    bottom: 6px;
  }
  .arco-tree-show-line
    .arco-tree-node-is-leaf:not(.arco-tree-node-is-tail)
    .arco-tree-node-indent::after {
    border-left: none;
  }
  .arco-tree-node-is-leaf .arco-tree-node-switcher::before {
    position: absolute;
    top: 50%;
    box-sizing: border-box;
    height: 1px;
    border-top: 1px solid rgba(255, 255, 255, 0.5);
    transform: translateY(-50%);
    content: '';
    left: -16px;
    right: 4px;
  }
  .title-text {
    display: inline-block;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .status-dot {
    display: inline-block;
    width: 5px;
    height: 5px;
    border-radius: 50%;
    margin-right: 4px;
    margin-left: 8px;
    background-color: #fff;
  }
  .title-status {
    display: inline-block;
  }
}
</style>

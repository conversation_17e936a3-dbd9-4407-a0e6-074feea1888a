<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#229;&#188;&#185;&#230;&#161;&#134;&#229;&#133;&#179;&#233;&#151;&#173;&#230;&#140;&#137;&#233;&#146;&#174;">
<g id="&#232;&#183;&#175;&#229;&#190;&#132; 20">
<g filter="url(#filter0_i_1129_3520)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M40 40H8L0 32V0H32L40 8V40Z" fill="#0093FF" fill-opacity="0.1"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M40 40H8L0 32V0H32L40 8V40Z" stroke="#0093FF"/>
</g>
<g id="&#229;&#133;&#179;&#233;&#151;&#173;">
<rect id="&#231;&#159;&#169;&#229;&#189;&#162;" opacity="0.01" x="9.33331" y="9.33334" width="21.3333" height="21.3333" fill="white"/>
<path id="Vector 88" d="M12.8888 12.8889L27.111 27.1112" stroke="white" stroke-width="2" stroke-linecap="square"/>
<path id="Vector 89" d="M12.8888 27.1111L27.111 12.8889" stroke="white" stroke-width="2" stroke-linecap="square"/>
</g>
</g>
<defs>
<filter id="filter0_i_1129_3520" x="-0.5" y="-0.5" width="41" height="42" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.576471 0 0 0 0 1 0 0 0 0.395366 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1129_3520"/>
</filter>
</defs>
</svg>

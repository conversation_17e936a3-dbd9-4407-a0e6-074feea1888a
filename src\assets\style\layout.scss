.flex-table {
	flex: 1;

	&.arco-table {
		.arco-spin {
			height: 100%;
			.arco-table-container {
				height: 100%;
			}
		}
	}
}

.card-picker {
	position: relative;
	cursor: pointer;

	&.is-active {
		.border {
			position: absolute;
			width: var(--border-size, 16px);
			height: var(--border-size, 16px);
			background-color: #00f6ff;
			--border-width: 1px;

			&-left-top {
				left: 0px;
				top: 0px;
				clip-path: polygon(
					0 0,
					100% 0,
					100% var(--border-width),
					var(--border-width) var(--border-width),
					var(--border-width) 100%,
					0 100%
				);
			}

			&-left-bottom {
				left: 0px;
				bottom: 0px;
				clip-path: polygon(
					0 0,
					var(--border-width) 0,
					var(--border-width) calc(100% - var(--border-width)),
					100% calc(100% - var(--border-width)),
					100% 100%,
					0 100%
				);
			}

			&-right-top {
				right: 0px;
				top: 0px;
				clip-path: polygon(
					0 0,
					100% 0,
					100% 100%,
					calc(100% - var(--border-width)) 100%,
					calc(100% - var(--border-width)) var(--border-width),
					0 var(--border-width)
				);
			}

			&-right-bottom {
				right: 0px;
				bottom: 0px;
				clip-path: polygon(
					calc(100% - var(--border-width)) 0,
					100% 0,
					100% 100%,
					0 100%,
					0 calc(100% - var(--border-width)),
					calc(100% - var(--border-width)) calc(100% - var(--border-width))
				);
			}
		}

		.dot {
			position: absolute;
			width: var(--dot-size, 4px);
			height: var(--dot-size, 4px);
			box-shadow: 0px 0px 13px 0px #33c3ff, 0px 0px 13px 0px #33c3ff;
			box-sizing: border-box;
			border: 1px solid #35daf4;

			&-left-top {
				left: var(--dot-offset, 8px);
				top: var(--dot-offset, 8px);
			}

			&-right-bottom {
				right: var(--dot-offset, 8px);
				bottom: var(--dot-offset, 8px);
			}
		}

		&::after {
			content: "";
			position: absolute;
			left: var(--bg-padding, 4px);
			top: var(--bg-padding, 4px);
			width: calc(100% - var(--bg-padding, 4px) * 2);
			height: calc(100% - var(--bg-padding, 4px) * 2);
			background-color: rgba(0, 138, 255, 0.09);
			box-sizing: border-box;
			border: 1px solid;
			border-image: linear-gradient(180deg, rgba(0, 111, 175, 0), rgba(0, 111, 175, 1)) 1 1;
		}
	}
}

<svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#233;&#135;&#142;&#231;&#148;&#159;&#229;&#138;&#168;&#231;&#137;&#169;&#228;&#191;&#157;&#230;&#138;&#164;&#231;&#171;&#153;">
<g id="Ellipse 15" filter="url(#filter0_d_342_3911)">
<circle cx="10.9956" cy="10.8545" r="8" fill="url(#paint0_radial_342_3911)" fill-opacity="0.7"/>
<circle cx="10.9956" cy="10.8545" r="7.55492" stroke="url(#paint1_linear_342_3911)" stroke-width="0.890162"/>
</g>
<circle id="Ellipse 16" cx="10.9956" cy="10.8543" r="7.01097" fill="url(#paint2_radial_342_3911)" stroke="url(#paint3_linear_342_3911)" stroke-width="0.200286"/>
<g id="Group 1321315401" filter="url(#filter1_d_342_3911)">
<g id="Frame" clip-path="url(#clip0_342_3911)">
<path id="Vector" d="M8.24121 6.99747C8.34743 6.80714 8.50097 6.64746 8.68699 6.53386C8.873 6.42026 9.08519 6.3566 9.30302 6.34903C10.0499 6.32971 10.3356 7.02159 10.3449 7.00215C10.3543 6.98272 11.2054 6.65668 11.7404 7.62811C11.9605 8.16031 11.7158 8.8057 11.6573 8.85967C11.784 9.069 11.8513 9.30891 11.8519 9.55363C11.8526 9.79834 11.7865 10.0386 11.6608 10.2486C11.1656 10.9921 10.2665 10.8135 10.2665 10.8135L9.77012 11.8717L9.78651 15.3071L8.95415 15.3159L8.92606 11.8911L8.33838 10.7907C7.53412 10.8732 7.04946 10.5129 6.85513 9.7488C6.79366 9.43646 6.85803 9.11249 7.03424 8.84738C6.88874 8.66497 6.80462 8.44124 6.79389 8.20815C6.78317 7.97507 6.8464 7.74456 6.97453 7.54956C7.30701 6.97277 7.75421 6.89504 8.24121 6.99759V6.99747ZM12.9087 9.91984C12.9087 9.91984 12.8244 9.85615 12.787 9.92218C12.794 10.3477 13.055 10.5397 13.3934 10.6279L14.3814 10.6543L14.3779 10.9002L13.8933 10.9016C13.8927 10.9396 13.901 10.9772 13.9174 11.0114C13.9338 11.0457 13.9579 11.0757 13.9878 11.0992C14.0177 11.1226 14.0527 11.1388 14.0899 11.1466C14.1271 11.1543 14.1655 11.1534 14.2023 11.1438C14.2105 11.1478 14.2176 11.1536 14.2233 11.1607C14.229 11.1678 14.233 11.1761 14.2351 11.185L13.6626 11.9902H11.5695L10.997 12.583L11.2616 13.3557L10.8378 13.7711L10.8179 15.3133L11.0965 15.3185L11.107 13.9424L11.4676 13.8543L12.0143 15.334L12.2824 15.3185L11.738 13.7264L12.0307 13.3739L13.5128 13.3505L13.5549 13.4135L13.137 15.3439L13.4226 15.3486L13.7457 13.8502L13.7984 13.8558L14.2491 15.3432L14.5535 15.3272L14.0021 13.4688L13.9857 13.3517L14.1695 13.2301L14.639 11.7124L15.5556 11.0999L15.5626 10.9059L14.5945 10.8961V10.6539C14.691 10.6394 14.7833 10.6043 14.8651 10.5511C14.9469 10.4979 15.0163 10.4277 15.0686 10.3453C15.073 10.3287 15.0727 10.3113 15.068 10.2949C15.0633 10.2785 15.0542 10.2637 15.0417 10.252C15.0292 10.2403 15.0138 10.2322 14.9971 10.2285C14.9804 10.2249 14.9631 10.2258 14.9469 10.2312C14.0618 10.8751 13.8652 10.1259 13.8113 10.0369C13.7913 10.0194 13.7654 10.0102 13.7388 10.0112C13.7123 10.0122 13.6871 10.0233 13.6685 10.0423C13.6357 10.0826 13.6334 10.1573 13.8183 10.4162C13.8209 10.4208 13.8223 10.426 13.8224 10.4313C13.8225 10.4365 13.8214 10.4418 13.8191 10.4465C13.8168 10.4513 13.8134 10.4554 13.8092 10.4586C13.805 10.4618 13.8001 10.4639 13.7949 10.4648C13.4308 10.5054 12.904 10.427 12.9087 9.92136V9.91984ZM8.91903 10.9576C8.91508 10.9576 8.91118 10.9585 8.90762 10.9602C8.90406 10.9619 8.90092 10.9644 8.89845 10.9675C8.89597 10.9706 8.89423 10.9742 8.89333 10.978C8.89244 10.9819 8.89242 10.9859 8.89328 10.9897L9.33345 11.6311C9.34244 11.6362 9.35265 11.6388 9.36297 11.6384C9.37329 11.6381 9.38333 11.635 9.39199 11.6294L9.77246 10.9985C9.77461 10.9935 9.77563 10.9882 9.77545 10.9828C9.77528 10.9774 9.77391 10.9721 9.77145 10.9673C9.76899 10.9624 9.76549 10.9582 9.76122 10.9549C9.75694 10.9516 9.75198 10.9493 9.7467 10.9482C9.44818 11.0514 8.94947 10.9576 8.91903 10.9576Z" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_342_3911" x="0.325119" y="0.184006" width="21.341" height="21.341" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.33524"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.388235 0 0 0 0 0.640523 0 0 0 0 0.807843 0 0 0 0.51 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_342_3911"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3911" result="shape"/>
</filter>
<filter id="filter1_d_342_3911" x="5.99585" y="5.85254" width="9.9082" height="10.3533" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.445081"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.150327 0 0 0 0 0.247059 0 0 0 0 0.145098 0 0 0 0.75 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_342_3911"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3911" result="shape"/>
</filter>
<radialGradient id="paint0_radial_342_3911" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(10.9956 10.8545) scale(8)">
<stop stop-color="#D3D3D3"/>
<stop offset="0.03" stop-color="#D3D3D3" stop-opacity="0.9216"/>
<stop offset="0.13" stop-color="#D3D3D3" stop-opacity="0.7056"/>
<stop offset="0.23" stop-color="#D3D3D3" stop-opacity="0.5329"/>
<stop offset="0.35" stop-color="#D3D3D3" stop-opacity="0.4225"/>
<stop offset="0.47" stop-color="#D3D3D3" stop-opacity="0.3364"/>
<stop offset="0.6" stop-color="#D3D3D3" stop-opacity="0.2916"/>
<stop offset="0.76" stop-color="#D3D3D3" stop-opacity="0.2601"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.5"/>
</radialGradient>
<linearGradient id="paint1_linear_342_3911" x1="10.9956" y1="18.8545" x2="10.9956" y2="2.85449" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<radialGradient id="paint2_radial_342_3911" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(10.9956 10.8543) scale(7.11111)">
<stop stop-color="#D3D3D3"/>
<stop offset="1" stop-color="#0B103B" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint3_linear_342_3911" x1="10.9956" y1="17.9654" x2="10.9956" y2="3.74316" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<clipPath id="clip0_342_3911">
<rect width="9.90826" height="9.90826" fill="white" transform="translate(5.99585 5.85254)"/>
</clipPath>
</defs>
</svg>

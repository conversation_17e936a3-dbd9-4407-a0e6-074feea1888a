<template>
  <TrafficCard title="实时交通流量">
    <div class="real-time-flow">
      <RealTimeFlowChart :data="data" />
    </div>
  </TrafficCard>
</template>

<script setup>
import request from "@/utils/request";
import RealTimeFlowChart from "./components/RealTimeFlowChart.vue";
import emitter from "@/utils/emitter";

const directionList = [1, 2];
const dataMap = ref(new Map());
const currentDepartmentId = ref(null);

onMounted(() => {
  // 监听区域双击事件
  emitter.$on("division-area-dblclick", handleAreaChange);
  getData();
});

onBeforeUnmount(() => {
  emitter.$off("division-area-dblclick", handleAreaChange);
});

const handleAreaChange = (eventData) => {
  console.log("实时交通流量 - 接收到区域双击事件:", eventData);
  currentDepartmentId.value = eventData.properties.id;
  getData();
};

const data = computed(() => {
  return Array.from(unref(dataMap)).map(([key, value]) => {
    return { label: key, ...value };
  });
});

const getData = () => {
  getSpeedData();
  getFlowData();
};

const getSpeedData = () => {
  const map = unref(dataMap);
  const baseParams = currentDepartmentId.value ? { departmentId: currentDepartmentId.value } : {};
  directionList.forEach((direction) => {
    const params = { ...baseParams, direction };
    request.get("/api/screen/baotong/traffic/flow/speed", params).then((res) => {
      if (res.code == 200) {
        (res.data || []).forEach(({ name = "", strNum = 0 }) => {
          const obj = map.get(name) || {};
          obj[`speed${direction}`] = strNum;
          map.set(name, obj);
        });
      }
    });
  });
};

const getFlowData = () => {
  const map = unref(dataMap);
  const baseParams = currentDepartmentId.value ? { departmentId: currentDepartmentId.value } : {};
  directionList.forEach((direction) => {
    const params = { ...baseParams, direction };
    request.get("/api/screen/baotong/traffic/flow/car", params).then((res) => {
      if (res.code == 200) {
        (res.data || []).forEach(({ name = "", num = 0 }) => {
          const obj = map.get(name) || {};
          obj[`car${direction}`] = num;
          map.set(name, obj);
        });
      }
    });
  });
};
</script>

<style lang="scss" scoped>
.real-time-flow {
  height: 250px;
}
</style>

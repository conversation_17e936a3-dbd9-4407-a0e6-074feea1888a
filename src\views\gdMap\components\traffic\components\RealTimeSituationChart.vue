<template>
	<v-chart class="chart" :option="option" autoresize />
</template>

<script setup>
import * as echarts from "echarts";
import VChart from "vue-echarts";

const props = defineProps({
	leftData: {
		type: Array,
		default: [],
	},
	rightData: {
		type: Array,
		default: [],
	},
});

const option = ref(null);

onMounted(() => {
	setOptions();
});

watch(
	() => [props.leftData, props.rightData],
	() => setOptions(),
	{ deep: true }
);

const setOptions = () => {
	const leftLabelData = props.leftData.map(({ label }) => label);
	const leftValueData = props.leftData.map(({ value }) => value);

	const rightLabelData = props.rightData.map(({ label }) => label);
	const rightValueData = props.rightData.map(({ value }) => value);

	option.value = {
		grid: [
			{
				show: false,
				left: "2%",
				top: "8%",
				bottom: "0%",
				width: "14%",
			},
			{
				show: false,
				left: "16%",
				top: "8%",
				bottom: "0%",
				width: "32%",
			},
			{
				show: false,
				right: "16%",
				top: "8%",
				bottom: "0%",
				width: "32%",
			},
			{
				show: false,
				right: "2%",
				top: "8%",
				bottom: "0%",
				width: "14%",
			},
		],
		xAxis: [
			{
				gridIndex: 0,
				show: false,
			},
			{
				gridIndex: 1,
				inverse: true,
				show: false,
			},
			{
				gridIndex: 2,
				show: false,
			},
			{
				gridIndex: 3,
				show: false,
			},
		],
		yAxis: [
			{
				gridIndex: 0,
				type: "category",
				inverse: true,
				position: "right",
				name: "拥堵时间",
				nameGap: 0,
				nameLocation: "start",
				nameTextStyle: {
					fontFamily: "Alibaba PuHuiTi",
					fontSize: 12,
					color: "#C4E5FF",
					padding: [0, -54, 12, 0],
				},
				axisLine: {
					show: false,
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					show: true,
					align: "right",
					fontFamily: "Alibaba PuHuiTi",
					fontSize: 12,
					color: "rgba(196,229,255,0.7)",
					margin: -8,
					formatter: (value) => {
						return `{value|${value}} {unit|小时}`;
					},
					rich: {
						value: {
							fontFamily: "D-DIN-PRO",
							fontWeight: 500,
							fontSize: 16,
							color: "#fff",
						},
						unit: {
							fontFamily: "Alibaba PuHuiTi",
							fontSize: 12,
							color: "#fff",
						},
					},
				},
				data: leftValueData,
			},
			{
				gridIndex: 1,
				type: "category",
				inverse: true,
				name: "格尔木方向",
				nameGap: 0,
				nameLocation: "start",
				nameTextStyle: {
					fontFamily: "Alibaba PuHuiTi",
					fontSize: 16,
					color: "#FFAD29",
					padding: [0, 76, 16, 0],
				},
				axisLine: {
					show: false,
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					show: false,
				},
				data: leftLabelData,
			},
			{
				gridIndex: 2,
				type: "category",
				inverse: true,
				name: "那曲方向",
				nameGap: 0,
				nameLocation: "start",
				nameTextStyle: {
					fontFamily: "Alibaba PuHuiTi",
					fontSize: 16,
					color: "#38E6FD",
					padding: [0, 0, 16, 76],
				},
				axisLine: {
					show: false,
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					show: false,
				},
				data: rightLabelData,
			},
			{
				gridIndex: 3,
				type: "category",
				inverse: true,
				position: "right",
				name: "拥堵时间",
				nameGap: 0,
				nameLocation: "start",
				nameTextStyle: {
					fontFamily: "Alibaba PuHuiTi",
					fontSize: 12,
					color: "#C4E5FF",
					padding: [0, 0, 12, 68],
				},
				axisLine: {
					show: false,
				},
				axisTick: {
					show: false,
				},
				axisLabel: {
					show: true,
					align: "right",
					fontFamily: "Alibaba PuHuiTi",
					fontSize: 12,
					color: "rgba(196,229,255,0.7)",
					margin: 0,
					formatter: (value) => {
						return `{value|${value}} {unit|小时}`;
					},
					rich: {
						value: {
							fontFamily: "D-DIN-PRO",
							fontWeight: 500,
							fontSize: 16,
							color: "#fff",
						},
						unit: {
							fontFamily: "Alibaba PuHuiTi",
							fontSize: 12,
							color: "#fff",
						},
					},
				},
				data: rightValueData,
			},
		],
		series: [
			{
				xAxisIndex: 1,
				yAxisIndex: 1,
				type: "bar",
				barWidth: 4,
				showBackground: true,
				backgroundStyle: {
					color: "#0F304E",
				},
				label: {
					show: true,
					position: "right",
					padding: [0, 0, 24, -64],
					formatter: ({ name }) => {
						return `{name|${name} } {dot|}`;
					},
					rich: {
						dot: {
							width: 3,
							height: 3,
							borderRadius: 6,
							backgroundColor: "#FFAD29",
						},
						name: {
							fontFamily: "Alibaba PuHuiTi",
							fontSize: 12,
							color: "#FFFFFF",
							padding: [0, 0, 0, 4],
						},
					},
				},
				itemStyle: {
					color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
						{ offset: 0, color: "#FFAD29" },
						{ offset: 1, color: "#845200" },
					]),
				},
				data: leftValueData,
			},
			{
				xAxisIndex: 2,
				yAxisIndex: 2,
				type: "bar",
				barWidth: 4,
				showBackground: true,
				backgroundStyle: {
					color: "#0F304E",
				},
				label: {
					show: true,
					position: "left",
					padding: [0, -64, 24, 0],
					formatter: ({ name }) => {
						return `{dot|} {name|${name} }`;
					},
					rich: {
						dot: {
							width: 3,
							height: 3,
							borderRadius: 6,
							backgroundColor: "#38E6FD",
						},
						name: {
							fontFamily: "Alibaba PuHuiTi",
							fontSize: 12,
							color: "#FFFFFF",
							padding: [0, 0, 0, 4],
						},
					},
				},
				itemStyle: {
					color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
						{ offset: 0, color: "rgba(0, 55, 40, 1)" },
						{ offset: 1, color: "rgba(56, 230, 253, 1)" },
					]),
				},
				data: rightValueData,
			},
		],
	};
};
</script>

<style lang="scss" scoped>
.chart {
	width: 100%;
	height: 100%;
}
</style>

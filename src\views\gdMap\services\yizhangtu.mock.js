// 绿色低碳数据服务
import axios from 'axios';

// 碳类型
const carbonTypes = [
  '生态降碳',
  '减污降碳',
  '循环利用降碳',
  '气候韧性降碳',
  '源头降碳',
  '过程降碳',
  '末端降碳',
];

// 存储加载的数据
let mockData = {
  greenCarbon: null,
  personnel: null,
  equipment: null,
  projectProgress: null,
  liveTraffic: null, // 添加实时交通数据缓存
};

// 从JSON文件加载数据
async function loadDataFromJson() {
  if (mockData.greenCarbon) {
    return mockData.greenCarbon;
  }

  try {
    // 从public目录下的JSON文件加载数据
    const response = await axios.get('/mock/yizhangtu/greenCarbon.data.json');
    const jsonData = response.data;

    // 处理JSON数据
    mockData.greenCarbon = processJsonData(jsonData);
    return mockData.greenCarbon;
  } catch (error) {
    console.error('加载绿色低碳数据失败:', error);
    // 出错时返回空数据
    return {
      all: {
        areaName: '全览',
        items: [],
        summary: { totalCurrent: 0, totalTarget: 0, completionRate: 0 },
      },
      areas: {},
    };
  }
}

// 处理JSON数据，计算进度和汇总信息
function processJsonData(jsonData) {
  const areas = jsonData.areas || [];

  // 处理每个分部的数据
  const processedAreas = areas.map(area => {
    const items = area.items
      .map(item => {
        // 防御性编程：确保item存在且有必要的字段
        if (!item) return null;

        // 使用JSON中已有的progress值
        return item;
      })
      .filter(Boolean); // 过滤掉可能的null值

    // 使用JSON中提供的数据
    const totalCurrent = items.reduce((sum, item) => sum + item.current, 0);

    return {
      areaName: area.areaName,
      key: area.key, // 直接使用area.key
      items,
      summary: {
        totalCurrent,
        totalTarget: area.totalTarget || 0,
        completionRate: area.actualProgress || 0,
        targetProgress: area.targetProgress || 0,
      },
    };
  });

  // 找到key为"all"的区域作为总体数据
  const allArea = processedAreas.find(area => area.key === 'all');

  // 构建最终数据结构
  const result = {
    all: allArea || {
      areaName: '全览',
      items: [],
      summary: { totalCurrent: 0, totalTarget: 0, completionRate: 0, targetProgress: 0 },
    },
    areas: {},
  };

  // 添加各个分部的数据
  processedAreas.forEach(areaData => {
    if (areaData.key !== 'all') {
      // 排除all区域，因为它已经作为总体数据使用了
      result.areas[areaData.key] = areaData; // 直接使用area.key作为键
    }
  });

  return result;
}

// 删除不再需要的pinyin函数

// 获取绿色低碳数据
export async function getGreenCarbonData(areaKey = 'all') {
  const data = await loadDataFromJson();

  if (areaKey === 'all') {
    return data.all.items;
  } else if (data.areas[areaKey]) {
    return data.areas[areaKey].items;
  }
  // 默认返回全部数据
  return data.all.items;
}

// 获取绿色低碳目标数据
export async function getGreenCarbonTarget(areaKey = 'all') {
  const data = await loadDataFromJson();

  if (areaKey === 'all') {
    return {
      targetCarbon: data.all.summary.totalTarget,
      completionRate: data.all.summary.completionRate,
      targetProgress: data.all.summary.targetProgress || 0,
    };
  } else if (data.areas[areaKey]) {
    return {
      targetCarbon: data.areas[areaKey].summary.totalTarget,
      completionRate: data.areas[areaKey].summary.completionRate,
      targetProgress: data.areas[areaKey].summary.targetProgress || 0,
    };
  }
  // 默认返回全部数据
  return {
    targetCarbon: data.all.summary.totalTarget,
    completionRate: data.all.summary.completionRate,
    targetProgress: data.all.summary.targetProgress || 0,
  };
}

// 获取所有区域列表
export async function getAreaList() {
  const data = await loadDataFromJson();

  return [
    { key: 'all', name: '全部区域' },
    ...Object.keys(data.areas).map(key => ({
      key,
      name: data.areas[key].areaName,
    })),
  ];
}

// 人员管理模块
// 从JSON文件加载人员管理数据
async function loadPersonnelData() {
  if (mockData.personnel) {
    return mockData.personnel;
  }

  try {
    // 从public目录下的JSON文件加载数据
    const response = await axios.get('/mock/yizhangtu/personnelManagement.data.json');
    const jsonData = response.data;

    // 存储数据
    mockData.personnel = jsonData;
    return mockData.personnel;
  } catch (error) {
    console.error('加载人员管理数据失败:', error);
    // 出错时返回空数据
    return {
      data: [],
    };
  }
}

// 根据工区key获取对应的数据
function getAreaDataByKey(data, areaKey = 'all') {
  if (!data || !data.data || !Array.isArray(data.data)) {
    return null;
  }

  // 查找匹配的工区数据
  const areaData = data.data.find(item => item.key === areaKey);
  return areaData ? areaData.data : null;
}

// 获取人员管理数据
export async function getPersonnelData(areaKey = 'all') {
  const data = await loadPersonnelData();
  let areaData = getAreaDataByKey(data, areaKey);

  // 如果指定区域没有数据，并且请求的不是全局数据，则尝试获取全局数据
  if ((!areaData || !areaData.personnelManagement || !areaData.personnelManagement.personnelData) && areaKey !== 'all') {
    const allAreaData = getAreaDataByKey(data, 'all');
    
    // 如果全局数据存在，则使用全局数据
    if (allAreaData && allAreaData.personnelManagement && allAreaData.personnelManagement.personnelData) {
      return allAreaData.personnelManagement.personnelData;
    }
  }

  // 如果没有获取到任何数据，返回默认值
  if (!areaData || !areaData.personnelManagement || !areaData.personnelManagement.personnelData) {
    return {
      managerCount: 0,
      workerCount: 0,
      totalCount: 0,
    };
  }

  return areaData.personnelManagement.personnelData;
}

// 获取人员预警数据
export async function getPersonnelWarningData(days = 7, areaKey = 'all') {
  const data = await loadPersonnelData();
  let areaData = getAreaDataByKey(data, areaKey);

  // 将days转为字符串，以匹配JSON中的键
  const daysKey = days.toString();

  // 如果指定区域没有数据或者该区域没有预警数据，尝试获取全局数据
  if (
    !areaData ||
    !areaData.personnelWarning ||
    !areaData.personnelWarning.warningData ||
    !areaData.personnelWarning.warningData[daysKey]
  ) {
    // 如果当前请求的不是全局数据，则尝试获取全局数据
    if (areaKey !== 'all') {
      const allAreaData = getAreaDataByKey(data, 'all');

      // 如果全局数据存在且有预警数据，则使用全局数据
      if (
        allAreaData &&
        allAreaData.personnelWarning &&
        allAreaData.personnelWarning.warningData &&
        allAreaData.personnelWarning.warningData[daysKey]
      ) {
        return allAreaData.personnelWarning.warningData[daysKey];
      }
    }

    // 如果全局数据也不存在，则返回空数组
    return [];
  }

  return areaData.personnelWarning.warningData[daysKey];
}

// 设备管理
// 从JSON文件加载设备管理数据
async function loadEquipmentData() {
  if (mockData.equipment) {
    return mockData.equipment;
  }

  try {
    // 从public目录下的JSON文件加载数据
    const response = await axios.get('/mock/yizhangtu/equipmentManagement.data.json');
    const jsonData = response.data;

    // 存储数据
    mockData.equipment = jsonData;
    return mockData.equipment;
  } catch (error) {
    console.error('加载设备管理数据失败:', error);
    // 出错时返回空数据
    return {
      data: [],
    };
  }
}

// 获取设备管理数据
export async function getEquipmentData(days = 7, areaKey = 'all') {
  const data = await loadEquipmentData();

  if (!data || !data.data || !Array.isArray(data.data)) {
    return {
      selfOwned: { online: 0, total: 0, rate: 0 },
      subcontracted: { online: 0, total: 0, rate: 0 },
      leased: { online: 0, total: 0, rate: 0 },
      idle: { online: 0, total: 0, rate: 0 },
      transport: { online: 0, total: 0, rate: 0 },
      total: { online: 0, total: 0, rate: 0 },
    };
  }

  // 查找匹配的工区数据
  const areaData = data.data.find(item => item.key === areaKey);
  
  // 如果没有找到指定区域的数据，并且请求的不是全局数据，则尝试获取全局数据
  if ((!areaData || !areaData.data) && areaKey !== 'all') {
    const allAreaData = data.data.find(item => item.key === 'all');
    if (allAreaData && allAreaData.data) {
      // 使用全局数据
      const daysKey = days.toString();
      const allEquipmentData = allAreaData.data[daysKey];
      
      if (allEquipmentData) {
        // 计算各类设备的在线率
        const result = {
          selfOwned: calculateRate(allEquipmentData.selfOwned),
          subcontracted: calculateRate(allEquipmentData.subcontracted),
          leased: calculateRate(allEquipmentData.leased),
          idle: calculateRate(allEquipmentData.idle),
          transport: calculateRate(allEquipmentData.transport),
        };

        // 计算总体在线率
        const totalOnline =
          result.selfOwned.online +
          result.subcontracted.online +
          result.leased.online +
          result.idle.online +
          result.transport.online;
        const totalCount =
          result.selfOwned.total +
          result.subcontracted.total +
          result.leased.total +
          result.idle.total +
          result.transport.total;

        result.total = {
          online: totalOnline,
          total: totalCount,
          rate: totalCount > 0 ? Math.round((totalOnline / totalCount) * 100) : 0,
        };

        return result;
      }
    }
  }

  // 如果没有找到数据或者全局数据也没有，则返回默认值
  if (!areaData || !areaData.data) {
    return {
      selfOwned: { online: 0, total: 0, rate: 0 },
      subcontracted: { online: 0, total: 0, rate: 0 },
      leased: { online: 0, total: 0, rate: 0 },
      idle: { online: 0, total: 0, rate: 0 },
      transport: { online: 0, total: 0, rate: 0 },
      total: { online: 0, total: 0, rate: 0 },
    };
  }

  // 将days转为字符串，以匹配JSON中的键
  const daysKey = days.toString();

  // 获取指定天数的数据
  const equipmentData = areaData.data[daysKey];

  // 如果指定天数没有数据，并且请求的不是全局数据，则尝试获取全局数据的对应天数
  if (!equipmentData && areaKey !== 'all') {
    const allAreaData = data.data.find(item => item.key === 'all');
    if (allAreaData && allAreaData.data && allAreaData.data[daysKey]) {
      const allEquipmentData = allAreaData.data[daysKey];
      
      // 计算各类设备的在线率
      const result = {
        selfOwned: calculateRate(allEquipmentData.selfOwned),
        subcontracted: calculateRate(allEquipmentData.subcontracted),
        leased: calculateRate(allEquipmentData.leased),
        idle: calculateRate(allEquipmentData.idle),
        transport: calculateRate(allEquipmentData.transport),
      };

      // 计算总体在线率
      const totalOnline =
        result.selfOwned.online +
        result.subcontracted.online +
        result.leased.online +
        result.idle.online +
        result.transport.online;
      const totalCount =
        result.selfOwned.total +
        result.subcontracted.total +
        result.leased.total +
        result.idle.total +
        result.transport.total;

      result.total = {
        online: totalOnline,
        total: totalCount,
        rate: totalCount > 0 ? Math.round((totalOnline / totalCount) * 100) : 0,
      };

      return result;
    }
    
    // 如果全局数据也没有指定天数的数据，返回默认值
    return {
      selfOwned: { online: 0, total: 0, rate: 0 },
      subcontracted: { online: 0, total: 0, rate: 0 },
      leased: { online: 0, total: 0, rate: 0 },
      idle: { online: 0, total: 0, rate: 0 },
      transport: { online: 0, total: 0, rate: 0 },
      total: { online: 0, total: 0, rate: 0 },
    };
  }

  if (!equipmentData) {
    return {
      selfOwned: { online: 0, total: 0, rate: 0 },
      subcontracted: { online: 0, total: 0, rate: 0 },
      leased: { online: 0, total: 0, rate: 0 },
      idle: { online: 0, total: 0, rate: 0 },
      transport: { online: 0, total: 0, rate: 0 },
      total: { online: 0, total: 0, rate: 0 },
    };
  }

  // 计算各类设备的在线率
  const result = {
    selfOwned: calculateRate(equipmentData.selfOwned),
    subcontracted: calculateRate(equipmentData.subcontracted),
    leased: calculateRate(equipmentData.leased),
    idle: calculateRate(equipmentData.idle),
    transport: calculateRate(equipmentData.transport),
  };

  // 计算总体在线率
  const totalOnline =
    result.selfOwned.online +
    result.subcontracted.online +
    result.leased.online +
    result.idle.online +
    result.transport.online;
  const totalCount =
    result.selfOwned.total +
    result.subcontracted.total +
    result.leased.total +
    result.idle.total +
    result.transport.total;

  result.total = {
    online: totalOnline,
    total: totalCount,
    rate: totalCount > 0 ? Math.round((totalOnline / totalCount) * 100) : 0,
  };

  return result;
}

// 计算在线率
function calculateRate(data) {
  if (!data) {
    return { online: 0, total: 0, rate: 0 };
  }

  const online = data.online || 0;
  const total = data.total || 0;
  const rate = total > 0 ? Math.round((online / total) * 100) : 0;

  return { online, total, rate };
}

// 项目进度数据
// 从JSON文件加载项目进度数据
async function loadProjectProgressData() {
  if (mockData.projectProgress) {
    return mockData.projectProgress;
  }

  try {
    // 从public目录下的JSON文件加载数据
    const response = await axios.get('/mock/yizhangtu/projectProgress.data.json');
    const jsonData = response.data;

    // 存储数据
    mockData.projectProgress = jsonData;
    return mockData.projectProgress;
  } catch (error) {
    console.error('加载项目进度数据失败:', error);
    // 出错时返回空数据
    return {
      data: [],
    };
  }
}

// 获取项目进度数据
export async function getProjectProgressData(areaKey = 'all') {
  const data = await loadProjectProgressData();

  if (!data || !data.data || !Array.isArray(data.data)) {
    return {
      totalMileage: 0,
      completedMileage: 0,
      plannedDuration: 0,
      actualDuration: 0,
      startDate: '',
      endDate: '',
      overCompletion: '0',
    };
  }

  // 查找匹配的工区数据
  const areaData = data.data.find(item => item.key === areaKey);

  // 如果没有找到指定区域的数据，并且请求的不是全局数据，则尝试获取全局数据
  if ((!areaData || !areaData.data) && areaKey !== 'all') {
    const allAreaData = data.data.find(item => item.key === 'all');
    if (allAreaData && allAreaData.data) {
      return allAreaData.data;
    }
  }

  // 如果没有找到数据或者全局数据也没有，则返回默认值
  if (!areaData || !areaData.data) {
    return {
      totalMileage: 0,
      completedMileage: 0,
      plannedDuration: 0,
      actualDuration: 0,
      startDate: '',
      endDate: '',
      overCompletion: '0',
    };
  }

  return areaData.data;
}

// 获取时间轴数据
export async function getTimelineMilestones(areaKey = 'all') {
  const data = await loadProjectProgressData();

  if (!data || !data.data || !Array.isArray(data.data)) {
    return [];
  }

  // 查找匹配的工区数据
  const areaData = data.data.find(item => item.key === areaKey);

  if (!areaData || !areaData.milestones) {
    // 如果没有找到指定区域的数据，并且请求的不是全览数据，则尝试获取全览数据
    if (areaKey !== 'all') {
      const allAreaData = data.data.find(item => item.key === 'all');
      if (allAreaData && allAreaData.milestones) {
        return allAreaData.milestones;
      }
    }
    return [];
  }

  return areaData.milestones;
}

// 实时交通数据
// 从JSON文件加载实时交通数据
async function loadLiveTrafficData() {
  if (mockData.liveTraffic) {
    return mockData.liveTraffic;
  }

  try {
    // 从public目录下的JSON文件加载数据
    const response = await axios.get('/mock/yizhangtu/liveTraffic.data.json');
    const jsonData = response.data;

    // 存储数据
    mockData.liveTraffic = jsonData;
    return mockData.liveTraffic;
  } catch (error) {
    console.error('加载实时交通数据失败:', error);
    // 出错时返回空数据
    return {
      areas: [],
    };
  }
}

// 获取实时交通概览数据
export async function getLiveTrafficOverview(areaKey = 'all') {
  const data = await loadLiveTrafficData();

  if (!data || !data.areas || !Array.isArray(data.areas)) {
    return {
      totalCongestionIndex: 0,
      totalCongestionDistance: 0,
    };
  }

  // 查找匹配的区域数据
  const areaData = data.areas.find(item => item.key === areaKey);

  // 如果没有找到指定区域的数据，并且请求的不是全览数据，则尝试获取全览数据
  if (!areaData && areaKey !== 'all') {
    const allAreaData = data.areas.find(item => item.key === 'all');
    if (allAreaData) {
      return {
        totalCongestionIndex: allAreaData.totalCongestionIndex || 0,
        totalCongestionDistance: allAreaData.totalCongestionDistance || 0,
      };
    }
  }

  if (!areaData) {
    return {
      totalCongestionIndex: 0,
      totalCongestionDistance: 0,
    };
  }

  return {
    totalCongestionIndex: areaData.totalCongestionIndex || 0,
    totalCongestionDistance: areaData.totalCongestionDistance || 0,
  };
}

// 获取实时交通拥堵数据
export async function getLiveTrafficCongestionData(areaKey = 'all') {
  const data = await loadLiveTrafficData();

  if (!data || !data.areas || !Array.isArray(data.areas)) {
    return [];
  }

  // 查找匹配的区域数据
  const areaData = data.areas.find(item => item.key === areaKey);

  // 如果没有找到指定区域的数据，并且请求的不是全览数据，则尝试获取全览数据
  if ((!areaData || !areaData.congestionData) && areaKey !== 'all') {
    const allAreaData = data.areas.find(item => item.key === 'all');
    if (allAreaData && allAreaData.congestionData) {
      return allAreaData.congestionData;
    }
  }

  if (!areaData || !areaData.congestionData) {
    return [];
  }

  return areaData.congestionData;
}

// 获取实时交通速度数据
export async function getLiveTrafficSpeedData(areaKey = 'all') {
  const data = await loadLiveTrafficData();

  if (!data || !data.areas || !Array.isArray(data.areas)) {
    return [];
  }

  // 查找匹配的区域数据
  const areaData = data.areas.find(item => item.key === areaKey);

  // 如果没有找到指定区域的数据，并且请求的不是全览数据，则尝试获取全览数据
  if (!areaData && areaKey !== 'all') {
    const allAreaData = data.areas.find(item => item.key === 'all');
    if (allAreaData && allAreaData.congestionData) {
      // 从全览数据的拥堵数据中提取速度信息
      return allAreaData.congestionData.map(item => ({
        name: item.area,
        section: item.section,
        limitSpeed: item.limitSpeed || 0,
        avgSpeed: item.averageSpeed || 0,
      }));
    }
  }

  if (!areaData || !areaData.congestionData) {
    return [];
  }

  // 从当前区域的拥堵数据中提取速度信息
  return areaData.congestionData.map(item => ({
    name: item.area,
    section: item.section,
    limitSpeed: item.limitSpeed || 0,
    avgSpeed: item.averageSpeed || 0,
  }));
}

// 冻土监测数据
// 从JSON文件加载冻土监测数据
async function loadPermafrostMonitoringData() {
  if (mockData.permafrostMonitoring) {
    return mockData.permafrostMonitoring;
  }

  try {
    // 从public目录下的JSON文件加载数据
    const response = await axios.get('/mock/yizhangtu/permafrostMonitoring.data.json');
    const jsonData = response.data;

    // 存储数据
    mockData.permafrostMonitoring = jsonData;
    return mockData.permafrostMonitoring;
  } catch (error) {
    console.error('加载冻土监测数据失败:', error);
    // 出错时返回空数据
    return {
      data: [],
    };
  }
}

// 获取冻土监测数据
export async function getPermafrostMonitoringData(areaKey = 'all') {
  const data = await loadPermafrostMonitoringData();
  let areaData = getAreaDataByKey(data, areaKey);

  // 如果指定区域没有数据，并且请求的不是全局数据，则尝试获取全局数据
  if ((!areaData || !areaData.monitoringData) && areaKey !== 'all') {
    const allAreaData = getAreaDataByKey(data, 'all');
    
    // 如果全局数据存在，则使用全局数据
    if (allAreaData && allAreaData.monitoringData) {
      return allAreaData.monitoringData;
    }
  }

  // 如果没有获取到任何数据，返回默认值
  if (!areaData || !areaData.monitoringData) {
    return {
      totalPoints: 0,
      alarmCount: 0,
      processedCount: 0,
      processingRate: 0,
    };
  }

  return areaData.monitoringData;
}

// 获取冻土监测预警数据
export async function getPermafrostWarningData(days = 7, areaKey = 'all') {
  const data = await loadPermafrostMonitoringData();
  let areaData = getAreaDataByKey(data, areaKey);

  // 将days转为字符串，以匹配JSON中的键
  const daysKey = days.toString();

  // 如果指定区域没有数据或者该区域没有预警数据，尝试获取全局数据
  if (
    !areaData ||
    !areaData.monitoringWarning ||
    !areaData.monitoringWarning.warningData ||
    !areaData.monitoringWarning.warningData[daysKey]
  ) {
    // 如果当前请求的不是全局数据，则尝试获取全局数据
    if (areaKey !== 'all') {
      const allAreaData = getAreaDataByKey(data, 'all');

      // 如果全局数据存在且有预警数据，则使用全局数据
      if (
        allAreaData &&
        allAreaData.monitoringWarning &&
        allAreaData.monitoringWarning.warningData &&
        allAreaData.monitoringWarning.warningData[daysKey]
      ) {
        return allAreaData.monitoringWarning.warningData[daysKey];
      }
    }

    // 如果全局数据也不存在，则返回空数组
    return [];
  }

  return areaData.monitoringWarning.warningData[daysKey];
}

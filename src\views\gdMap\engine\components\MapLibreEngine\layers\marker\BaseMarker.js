/**
 * 标记渲染器基类
 * 所有具体的标记渲染器都应该继承这个类
 */
export class BaseMarker {
  /**
   * 渲染标记到地图
   * @param {Object} map - MapLibre地图实例
   * @param {String} sourceId - 数据源ID
   * @param {Object} options - 渲染选项
   * @returns {Object} 渲染结果
   */
  static render(map, sourceId, options = {}) {
    throw new Error('子类必须实现render方法');
  }

  /**
   * 移除标记
   * @param {Object} map - MapLibre地图实例
   * @param {String} sourceId - 数据源ID
   */
  static remove(map, sourceId) {
    const layerId = `${sourceId}-layer`;
    if (map.getLayer(layerId)) {
      map.removeLayer(layerId);
    }
    if (map.getSource(sourceId)) {
      map.removeSource(sourceId);
    }
  }

  /**
   * 更新标记数据
   * @param {Object} map - MapLibre地图实例
   * @param {String} sourceId - 数据源ID
   * @param {Object} data - GeoJSON数据
   */
  static updateData(map, sourceId, data) {
    if (map.getSource(sourceId)) {
      map.getSource(sourceId).setData(data);
    }
  }

  /**
   * 设置标记可见性
   * @param {Object} map - MapLibre地图实例
   * @param {String} sourceId - 数据源ID
   * @param {Boolean} visible - 是否可见
   */
  static setVisibility(map, sourceId, visible) {
    const layerId = `${sourceId}-layer`;
    if (map.getLayer(layerId)) {
      map.setLayoutProperty(
        layerId,
        'visibility',
        visible ? 'visible' : 'none'
      );
    }
  }
}
<template>
	<div
		ref="markerRef"
		:id="markerId"
		class="project-marker"
		:class="{ 'marker-right': position === 'right' }"
	>
		<div class="marker-content">
			<template v-if="true">
				<div
					v-if="!isExpanded"
					class="marker-button"
					:style="buttonBgStyle"
					@click.stop="handleButtonClick"
				>
					<!-- <div class="marker-button-icon">
						<img :src="iconSrc" />
					</div> -->
					<div class="marker-button-label">{{ label }}分部</div>
					<icon-double-down />
				</div>
				<div class="marker-line"></div>
			</template>

			<MarkerPanel v-if="isExpanded" :title="label + '分部'">
				<template #header-extra>
					<div class="header-btn">
						<div class="header-btn-item" @click.stop="toggleExpand">
							<icon-double-up />
						</div>
					</div>
				</template>
				<template #content>
					<div class="data-list" v-if="formData && formData.length">
						<div v-for="(item, index) in formData" :key="index" class="data-item">
							<span class="data-key">{{ item.label }}:</span>
							<span class="data-value">{{ item.value }} {{ item.unit }}</span>
						</div>
					</div>
					<div v-else class="no-data">暂无数据</div>
				</template>
			</MarkerPanel>
		</div>
	</div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import request from "@/utils/request";
import emitter from "@/utils/emitter"; // 引入事件总线
import MarkerPanel from "./MarkerPanel.vue";

const props = defineProps({
	label: {
		type: String,
		default: "项目部",
	},
	properties: {
		type: Object,
		default: () => ({}),
	},
	position: {
		type: String,
		default: "left", // 默认左侧显示
	},
	lineHeight: {
		type: [Number, String],
		default: 40, // 默认线条高度为40px
	},
	// 新增图标配置
	iconConfig: {
		type: Object,
		default: () => ({}),
	},
});

const formData = ref([
	{ key: "roadLength", label: "公路里程", unit: "km", value: "" },
	{ key: "companyName", label: "所属单位", value: "" },
	{ key: "constructionTime", label: "施工时间", value: "" },
	{ key: "constructionProgress", label: "施工进度", value: "" },
	// { key: "progressDeviation", label: "进度偏差", value: "" },
	{ key: "managerNumber", label: "生产经理", unit: "人", value: "" },
	{ key: "workAreaNumber", label: "包含工区", unit: "个", value: "" },
]);

// // 从properties中提取formData
// const formData = computed(() => {
// 	return props.properties?.formData || [];
// });

// 判断是否可以展开
const isExpandable = computed(() => {
	return props.properties?.expandable !== false; // 默认为true
});

// 判断是否可以使用BIM引擎
const canUseBimEngine = computed(() => {
	return props.properties?.engines?.bim === true;
});

// 判断是否可以使用虚幻引擎
const canUseUnrealEngine = computed(() => {
	return props.properties?.engines?.unreal === true;
});

// 计算图标路径
const iconSrc = computed(() => {
	// 优先使用properties.style.leftIcon
	const iconName = props.properties?.style?.leftIcon || "area-left-icon";

	// 如果是完整路径，直接使用
	if (iconName.startsWith("/") || iconName.startsWith("http")) {
		return iconName;
	}

	// 否则拼接路径
	return `/assets/images/icons/map/${iconName}.svg`;
});

// 计算线条样式
const lineStyle = computed(() => {
	const style = {};

	// 设置线条高度
	style.height = `${props.lineHeight || props.properties?.style?.lineHeight || 40}px`;

	// 设置线条颜色
	if (props.properties?.style?.lineColor) {
		style.background = props.properties.style.lineColor;
	}

	return style;
});

// 计算
const buttonBgStyle = computed(() => {
	let style = {};

	// 设置面板头部背景色
	if (props.properties?.style?.buttonBgStyle) {
		style = props.properties?.style?.buttonBgStyle;
	}

	return style;
});

const isExpanded = ref(false);
const markerRef = ref(null);

// 生成唯一ID用于标识当前实例
const markerId = ref(`marker-${Date.now()}-${Math.floor(Math.random() * 1000)}`);

watch(
	() => isExpanded.value,
	(val) => {
		const parent = markerRef.value.parentElement;
		parent.style["z-index"] = val ? 1 : 0;
	}
);

// 处理按钮点击事件
const handleButtonClick = () => {
	if (isExpandable.value) {
		// 如果当前未展开，先通知其他标记关闭
		if (!isExpanded.value) {
			// 发送关闭其他标记的事件，传递当前标记ID
			emitter.$emit("marker-expand", markerId.value);
		}
		toggleExpand();
	}
};

// 处理虚幻引擎点击事件
const handleUnrealEngineClick = () => {
	if (canUseUnrealEngine.value) {
		// console.log('打开虚幻引擎', props.properties);
	}
};

// 处理BIM引擎点击事件
const handleBimEngineClick = () => {
	if (canUseBimEngine.value) {
		// console.log('打开BIM引擎', props.properties);
	}
};

const toggleExpand = () => {
	isExpanded.value = !isExpanded.value;
};

// 处理点击外部事件
const handleClickOutside = (event) => {
	if (isExpanded.value && markerRef.value && !markerRef.value.contains(event.target)) {
		isExpanded.value = false;
	}
};

// 监听其他标记展开事件
const handleOtherMarkerExpand = (id) => {
	// 如果不是当前标记且当前标记是展开状态，则关闭
	if (id !== markerId.value && isExpanded.value) {
		isExpanded.value = false;
	}
};

const getDetail = () => {
	request
		.get("/api/screen/staff/center/department/detail", {
			departmentId: props.properties.departmentId,
		})
		.then((res) => {
			const data = res.data || {};
			unref(formData).forEach((item) => {
				item.value = data[item.key];
			});
		});
};

// 组件挂载时添加事件监听
onMounted(() => {
	document.addEventListener("click", handleClickOutside);
	// 监听标记展开事件
	emitter.$on("marker-expand", handleOtherMarkerExpand);

	getDetail();
});

// 组件卸载时移除事件监听
onUnmounted(() => {
	document.removeEventListener("click", handleClickOutside);
	// 移除标记展开事件监听
	emitter.$off("marker-expand", handleOtherMarkerExpand);
});
</script>

<style scoped lang="scss">
.project-marker {
	cursor: pointer;
}

/* 右侧标记的样式 */

.marker-content {
	display: flex;
	align-items: center;
	// flex-direction: row-reverse;
	// transform-origin: right center;
}

.marker-line {
	// margin-left: 9px;
	width: 68px;
	// height: v-bind("`${props.lineHeight}px`");
	// background: #0783fa;
	height: 2px;
	background-color: #ffffff;
	position: relative;

	&::after {
		content: "";
		position: absolute;
		right: -6px;
		top: 50%;
		transform: translateY(-50%);
		width: 6px;
		height: 6px;
		border: 2px solid #ffffff;
		box-sizing: border-box;
	}
}

.marker-content {
	position: relative;
	transform-origin: left center;
}

.marker-button {
	position: relative; /* 添加相对定位 */
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 102px;
	height: 31px;
	padding: 0 12px;
	color: #d9e5f5;
	// background: linear-gradient(180deg, #5aaeff 0%, #0783fa 100%);
	// border-radius: 33px;
	// border: 1px solid #b2ccff;
	background-image: url("@/assets/modules/maplibre/icon/marker-area-button-bg.svg");
	background-size: 100% 100%;
	background-repeat: no-repeat;
	box-sizing: border-box;

	&-icon {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 23px;
		height: 23px;
		background: #ffffff;
		border-radius: 50%;
		img {
			width: 19px;
			height: 19px;
		}
	}

	&-label {
		font-family: Alibaba PuHuiTi;
		font-size: 14px;
		color: #ffffff;
		white-space: nowrap;
	}
}

.marker-arrow {
	width: 14px;
	height: 14px;
	transform: rotate(-90deg);

	img {
		width: 100%;
		height: 100%;
	}
}

.expanded .marker-arrow {
	transform: rotate(90deg);
}

.header-btn {
	display: flex;
	align-items: center;
	gap: 8px;
	&-item {
		height: 14px;
		width: 14px;
		color: #d9e5f5;
		cursor: pointer;
		img {
			height: 100%;
			width: 100%;
		}

		&.disabled {
			opacity: 0.4;
			cursor: not-allowed;
			filter: grayscale(100%);
		}
	}
}

.close-btn {
	cursor: pointer;
	color: #fff;
	font-size: 14px;
	font-weight: bold;
}

.close-btn:hover {
	opacity: 0.8;
}
</style>

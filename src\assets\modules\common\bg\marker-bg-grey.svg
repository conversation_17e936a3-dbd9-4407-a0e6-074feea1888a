<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="icon-&#229;&#186;&#149;&#229;&#156;&#134;-&#231;&#129;&#176;&#232;&#137;&#178;">
<circle id="Ellipse 528" cx="32.0001" cy="32.0001" r="15.3333" fill="black"/>
<g id="Group 1250">
<g id="Vector" filter="url(#filter0_d_2182_4445)">
<path d="M32 49.9997C41.9411 49.9997 50 41.9409 50 31.9999C50 22.0588 41.9411 14 32 14C22.0589 14 14 22.0588 14 31.9999C14 41.9409 22.0589 49.9997 32 49.9997Z" stroke="#989898" stroke-width="0.929461" stroke-miterlimit="10"/>
<path d="M32 49.9997C41.9411 49.9997 50 41.9409 50 31.9999C50 22.0588 41.9411 14 32 14C22.0589 14 14 22.0588 14 31.9999C14 41.9409 22.0589 49.9997 32 49.9997Z" stroke="url(#paint0_linear_2182_4445)" stroke-opacity="0.93" stroke-width="0.929461" stroke-miterlimit="10"/>
</g>
<g id="Vector_2" filter="url(#filter1_dd_2182_4445)">
<path d="M32.0003 47.1502C40.3676 47.1502 47.1507 40.3672 47.1507 31.9999C47.1507 23.6326 40.3676 16.8496 32.0003 16.8496C23.6329 16.8496 16.8499 23.6326 16.8499 31.9999C16.8499 40.3672 23.6329 47.1502 32.0003 47.1502Z" stroke="#8E8E8E" stroke-width="0.465442" stroke-miterlimit="10" shape-rendering="crispEdges"/>
<path d="M32.0003 47.1502C40.3676 47.1502 47.1507 40.3672 47.1507 31.9999C47.1507 23.6326 40.3676 16.8496 32.0003 16.8496C23.6329 16.8496 16.8499 23.6326 16.8499 31.9999C16.8499 40.3672 23.6329 47.1502 32.0003 47.1502Z" stroke="url(#paint1_linear_2182_4445)" stroke-opacity="0.93" stroke-width="0.465442" stroke-miterlimit="10" shape-rendering="crispEdges"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_2182_4445" x="-13.1314" y="-13.1314" width="90.2628" height="90.2625" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="13.3333"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.647059 0 0 0 0 0.647059 0 0 0 0 0.647059 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2182_4445"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2182_4445" result="shape"/>
</filter>
<filter id="filter1_dd_2182_4445" x="-30.0495" y="-30.0497" width="124.1" height="124.099" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="23.3333"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.034837 0 0 0 0 0.034837 0 0 0 0 0.034837 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2182_4445"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="13.3333"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.231427 0 0 0 0 0.231427 0 0 0 0 0.231427 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_2182_4445" result="effect2_dropShadow_2182_4445"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_2182_4445" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2182_4445" x1="45.6667" y1="19.3333" x2="19.6667" y2="46" gradientUnits="userSpaceOnUse">
<stop stop-color="#EAF6FF" stop-opacity="0"/>
<stop offset="1" stop-color="#535353"/>
</linearGradient>
<linearGradient id="paint1_linear_2182_4445" x1="43.5033" y1="21.3386" x2="21.6194" y2="43.7837" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="#A2A2A2"/>
</linearGradient>
</defs>
</svg>

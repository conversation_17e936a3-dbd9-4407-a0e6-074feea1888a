import { BaseMarker } from "../BaseMarker";
import maplibregl from "maplibre-gl";
import request from "@/utils/request";
import StaffMarkerVue from "@/views/gdMap/engine/components/MapLibreEngine/components/StaffMarker.vue";
import mapConfig from "@/config/engine/maplibre/map.config.js";

const clusterZoom = 9;

export class StaffMarker extends BaseMarker {
	static MAP_KEY = `${StaffMarker.name}Map`;
	static HANDLER_KEY = `_${StaffMarker.name}ZoomHandler`;

	static render(map, sourceId, options = {}) {
		const { data } = options;

		const markersData = this.preprocessData(data);

		this.renderMarkers(map, markersData, options);

		this.setupZoomHandler(map, sourceId);
	}

	static preprocessData(data) {
		return data.features
			.map((item) => item)
			.filter((item) => {
				const {
					geometry: { coordinates },
				} = item;
				return coordinates && coordinates.length === 2;
			});
	}

	static renderMarkers(map, markersData, options) {
		// this.removeMarkers(map);

		markersData.forEach((markerData) => {
			this.createMarker(map, markerData, options);
		});
	}

	static createMarker(map, markerData, options) {
		const {
			geometry: { coordinates },
			properties,
		} = markerData;
		const { type } = options;

		const el = document.createElement("div");

		const app = createApp(StaffMarkerVue, {
			properties,
			type,
		});
		app.mount(el);

		const marker = new maplibregl.Marker({
			element: el,
		})
			.setLngLat(coordinates)
			.addTo(map);
		marker._data = markerData;

		if (!map[this.MAP_KEY]) {
			map[this.MAP_KEY] = new Map();
		}
		const id = `${StaffMarker.name}-${type}-${
			markerData.properties.staffId || markerData.properties.departmentId
		}-${Date.now()}`;
		map[this.MAP_KEY].set(id, marker);

		this.updateElementSize(marker, mapConfig.minzoom * 7);

		map.on("zoom", () => {
			const zoom = map.getZoom();
			// 根据 circle-radius 的插值规则设置 size
			let size;
			if (zoom <= 5) {
				size = 6;
			} else if (zoom <= 9) {
				// 5~9: 6~14
				size = 6 + ((zoom - 5) / (9 - 5)) * (14 - 6);
			} else if (zoom <= 13) {
				// 9~13: 14~44
				size = 14 + ((zoom - 9) / (13 - 9)) * (44 - 14);
			} else {
				size = 44;
			}
			this.updateElementSize(marker, size * 7);
		});
	}

	static updateElementSize(marker, size) {
		const element = marker.getElement();
		element.style.width = size + "px";
		element.style.height = size + "px";
		element.style.fontSize = size + "px";
	}

	static removeMarkers(map) {
		const markers = this.getMarkers(map);
		markers.forEach((marker) => {
			marker.remove();
		});
		if (map[this.MAP_KEY]) {
			map[this.MAP_KEY].clear();
		}
	}

	static getMarkers(map) {
		if (map[this.MAP_KEY]) {
			const markers = Array.from(map[this.MAP_KEY].values());
			return markers;
		}
		return [];
	}

	static setVisibility(map, isVisible) {
		const markers = this.getMarkers(map);
		markers.forEach((marker) => {
			marker._element.style.visibility = isVisible ? "visible" : "hidden";
		});
	}

	static setupZoomHandler(map, sourceId) {
		if (!map[this.HANDLER_KEY]) {
			let lastZoom = map.getZoom(); // 记录上次zoom级别

			map[this.HANDLER_KEY] = () => {
				const zoom = map.getZoom();
				// 处理所有StaffMarker标记的可见性，不依赖特定的sourceId
				this.setVisibilityByType(map, zoom < clusterZoom);

				if (lastZoom < clusterZoom && zoom >= clusterZoom) {
					[
						StaffPeopleMarker,
						// StaffFenceMarker
					].forEach((MarkerClass) => {
						MarkerClass.render(map, {
							type: MarkerClass.markerType,
						});
					});
				}

				lastZoom = zoom; // 更新记录的zoom级别
			};

			map.on("zoomend", map[this.HANDLER_KEY]);
		}
	}

	static setVisibilityByType(map, isVisible) {
		const markers = this.getMarkers(map);

		markers.forEach((marker) => {
			// 所有StaffMarker类型的标记都按照相同的缩放级别控制显示
			marker._element.style.visibility = isVisible ? "visible" : "hidden";
		});
	}

	static remove(map, sourceId) {
		// 如果指定了sourceId，只移除特定sourceId的标记
		if (sourceId) {
			this.removeMarkersBySourceId(map, sourceId);
		} else {
			// 如果没有指定sourceId，移除所有标记并清理事件监听器
			map.off("zoomend", map[this.HANDLER_KEY]);
			this.removeMarkers(map);
		}
	}

	static removeMarkersBySourceId(map, sourceId) {
		if (map[this.MAP_KEY]) {
			const markersToRemove = [];

			map[this.MAP_KEY].forEach((marker, id) => {
				// 根据ID中包含的sourceId信息来判断是否需要移除
				// 对于mechanical-fence sourceId，ID格式为: StaffMarker-fence-departmentId-timestamp
				if (sourceId === "mechanical-fence" && id.includes("-fence-")) {
					markersToRemove.push(id);
				} else if (sourceId === "" && !id.includes("-fence-")) {
					// 对于空sourceId，移除不包含fence的标记（车辆标记）
					markersToRemove.push(id);
				}
			});

			// 移除匹配的标记
			markersToRemove.forEach((id) => {
				const marker = map[this.MAP_KEY].get(id);
				if (marker) {
					marker.remove();
					map[this.MAP_KEY].delete(id);
				}
			});
		}
	}
}

class StaffActualMarker extends StaffMarker {
	static async render(map, options) {
		const geojson = await this.loadData();

		const markersData = this.preprocessData(geojson);

		this.renderMarkers(map, markersData, options);

		this.setupZoomHandler(map);
	}

	static async loadData() {}

	static createMarker(map, markerData, options) {
		const {
			geometry: { coordinates },
			properties,
		} = markerData;

		const { type } = options;

		const el = document.createElement("div");

		const app = createApp(StaffMarkerVue, {
			properties,
			type,
		});
		app.mount(el);

		const marker = new maplibregl.Marker({
			element: el,
		})
			.setLngLat(coordinates)
			.addTo(map);
		marker._data = markerData;

		if (!map[this.MAP_KEY]) {
			map[this.MAP_KEY] = new Map();
		}
		const id = `${type}-${markerData.properties.staffId}-${Date.now()}`;
		map[this.MAP_KEY].set(id, marker);

		this.updateElementSize(marker, 32 * 3);
	}

	static setupZoomHandler(map) {
		if (!map[this.HANDLER_KEY]) {
			map[this.HANDLER_KEY] = () => {
				const zoom = map.getZoom();
				if (zoom < clusterZoom) {
					this.removeMarkers(map);
				}
				// this.setVisibility(map, zoom >= clusterZoom);
			};

			map.on("zoomend", map[this.HANDLER_KEY]);
		}
	}

	static remove(map, sourceId) {
		// 如果指定了sourceId，只移除特定sourceId的标记
		if (sourceId) {
			this.removeMarkersBySourceId(map, sourceId);
		} else {
			// 如果没有指定sourceId，移除所有标记并清理事件监听器
			map.off("zoomend", map[this.HANDLER_KEY]);
			this.removeMarkers(map);
		}
	}
}

class StaffPeopleMarker extends StaffActualMarker {
	static MAP_KEY = `${StaffPeopleMarker.name}Map`;
	static HANDLER_KEY = `_${StaffPeopleMarker.name}ZoomHandler`;
	static markerType = "people-actual";

	static async loadData() {
		const { data = [] } = await request.get("/api/screen/staff/center/staff/list");

		return {
			type: "FeatureCollection",
			features: data.map((item) => ({
				type: "Feature",
				geometry: {
					type: "Point",
					coordinates: item.coordinates,
				},
				properties: item,
			})),
		};
	}
}

class StaffFenceMarker extends StaffActualMarker {}

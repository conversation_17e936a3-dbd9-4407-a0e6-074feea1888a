<template>
  <div class="left-card">
    <m-card title="工区拥堵统计-格尔木方向" :height="330" :isRight="true">
      <div class="traffic-jam-g">
        <div class="overview">
          <div class="overview-item" style="margin-right: 10px">
            <div class="icon"><img src="@/assets/images/baotong/g-number.png" alt="工区数" /></div>
            <div class="content">当前拥堵工区数</div>
            <div class="value">6</div>
          </div>
          <div class="overview-item">
            <div class="icon"><img src="@/assets/images/baotong/g-percent.png" alt="工区率" /></div>
            <div class="content">当前拥堵工区率</div>
            <div class="value">11%</div>
          </div>
        </div>
        <div class="rank-list">
          <div ref="containerRef" class="scroll">
            <div class="scroll-content">
              <div class="rank-item" v-for="(item, index) in listData" :key="index">
                <div class="ranking">
                  <template v-if="index < 3">
                    <img :src="ranking[index]" alt="" />
                  </template>
                  <div v-else class="medal">{{ index + 1 }}</div>
                </div>
                <div class="r-content">
                  <div class="r-name">{{ item.name }}</div>
                  <div class="r-bar">
                    <div class="r-bar-inner" :style="{ width: formatPercent(item.value) }"></div>
                  </div>
                </div>
                <div class="r-value">
                  <span class="r-num">{{ item.value }}</span>
                  <span class="r-unit">小时</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </m-card>
  </div>
</template>
<script setup>
import { ref, onMounted, watch } from 'vue';
import mCard from '@/components/mCard/index.vue';
import medal1 from '@/assets/images/baotong/medal-1.png';
import medal2 from '@/assets/images/baotong/medal-2.png';
import medal3 from '@/assets/images/baotong/medal-3.png';
import { useMarquee } from '@/hooks/useMarquee';
import { getTrafficJam } from '@/views/gdMap/services/baotong.mock.js';

const ranking = ref([medal1, medal2, medal3]);

const listData = ref();
const { containerRef, resetScroll } = useMarquee({
  speed: 10, // 滚动速度
  delay: 3000, // 滚动到底部后停顿时间
  step: 1, // 每次滚动的像素
});
onMounted(async () => {
  const defaultData = await getTrafficJam();
  listData.value = defaultData.golmud;
});

// 当数据变化时，重置滚动
watch(
  listData,
  () => {
    setTimeout(resetScroll, 300); // 延迟一点时间确保DOM已更新
  },
  { deep: true }
);

const formatPercent = num => {
  return ((num / 2) * 100).toFixed(2) + '%';
};
</script>
<style lang="scss">
.traffic-jam-g {
  color: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
  max-height: 100%;
  .overview {
    display: flex;
    align-items: center;
    padding: 11px 8px 0 2px;
    .overview-item {
      display: flex;
      align-items: center;
      .icon {
        width: 42px;
        height: 38px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .content {
        font-size: 12px;
        color: #bfd1ff;
        margin-right: 4px;
      }
      .value {
        font-size: 20px;
        color: #48d6ff;
        margin-bottom: 5px;
        font-family: 'DDINPRO-Bold';
      }
    }
  }
  .rank-list {
    flex: 1;
    overflow: hidden;
    .scroll {
      height: 100%;
      overflow-y: hidden;
    }
    .rank-item {
      display: flex;
      padding: 0 10px;
      margin-bottom: 10px;
    }
    .ranking {
      width: 24px;
      height: 24px;
      margin-right: 12px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .medal {
      position: relative;
      background: #b2b2b2;
      width: 18px;
      height: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      color: rgba(123, 124, 124, 1);
      &::before {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 1px solid rgba(123, 124, 124, 1);
      }
    }
    .r-content {
      flex: 1;
      .r-name {
        font-size: 12px;
        line-height: 14px;
        margin-bottom: 6px;
      }
      .r-bar {
        border-radius: 3px;
        width: 100%;
        height: 6px;
        background: rgba(7, 131, 250, 0.2);
      }
      .r-bar-inner {
        border-radius: 3px;
        height: 100%;
        background: linear-gradient(270deg, #51e0ff 0%, #0085ff 100%);
      }
    }
    .r-value {
      margin-left: 8px;
      margin-top: 10px;
      width: 60px;
      text-align: right;
      .r-num {
        font-size: 20px;
        color: #48d6ff;
        margin-right: 2px;
        font-family: 'DDINPRO-Bold';
      }
      .r-unit {
        font-size: 12px;
        color: #bfd1ff;
      }
    }
  }
}
</style>

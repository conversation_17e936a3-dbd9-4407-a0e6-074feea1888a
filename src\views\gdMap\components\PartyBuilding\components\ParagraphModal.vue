<template>
	<Modal ref="ModalRef" title="文章详情" :mask="mask" size="large">
		<div class="rich-text">
			<div class="rich-text-title">{{ data.title }}</div>
			<div class="rich-text-desc">
				<div class="rich-text-desc-time">{{ data.publishDate }}</div>
				<div class="rich-text-desc-actor">发布者：{{ data.publishUser || "-" }}</div>
			</div>
			<!-- <div class="rich-text-pic">
				<img src="@/assets/modules/common/bg/home-bg.webp" alt="" />
			</div> -->
			<div class="rich-text-content">
				<p v-for="content in formattedText(data.content)">
					{{ content }}
				</p>
			</div>
		</div>
	</Modal>
</template>

<script setup>
import request from "@/utils/request";
import Modal from "./Modal.vue";

const props = defineProps({
	mask: {
		type: Boolean,
		default: true,
	},
});
const ModalRef = ref(null);
const data = ref({});

const onOpen = (id) => {
	ModalRef.value?.open();
	getData(id);
};

const getData = (id) => {
	request.get("/api/screen/dangjian/article/detail", { id }).then((res) => {
		if (res && res.code == 200) {
			data.value = res.data;
		}
	});
};

const formattedText = (text = "") => {
	return text.split(/\n/);
};

defineExpose({
	open: onOpen,
});
</script>

<style lang="scss" scoped>
.rich-text {
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	text-align: center;
	font-family: Alibaba PuHuiTi;

	&-title {
		margin: 16px 0;
		font-size: 24px;
		background-image: linear-gradient(180deg, #fff9de 40%, #ff6600 76%);
		background-clip: text;
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
	}

	&-desc {
		font-size: 18px;
		color: rgba(255, 255, 255, 0.7);
		display: inline-flex;
		align-items: center;
		justify-content: center;
		column-gap: 28px;
	}

	&-pic {
		margin-top: 24px;
		width: 100%;
		height: 240px;

		img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}

	&-content {
		flex: 1;
		margin-top: 24px;
		font-family: Alibaba PuHuiTi;
		font-size: 18px;
		color: #ffffff;
		text-align: justify;
		white-space: pre-line;
		overflow: auto;

		p {
			text-indent: 2em;
		}
	}
}
</style>

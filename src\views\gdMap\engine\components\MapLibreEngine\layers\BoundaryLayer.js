import maplibregl from "maplibre-gl";
import bloom from "@/assets/modules/common/bg/bloom.webp";
/**
 * 青藏省界图层
 */
export class BoundaryLayer {
	constructor(map) {
		this.map = map;
		this.baseSourceId = "boundary-base-source"; // 数据源ID
		this.baseLayerId = "boundary-base-layer"; // 基础图层ID
		this.glowSourceId = "boundary-glow-source"; // 发光效果数据源ID
		this.glowLayerId = "boundary-glow-layer"; // 发光效果图层ID
		this.flowSourceId = "boundary-flow-source"; // 流光数据源ID
		this.flowLayerId = "boundary-flow-layer"; // 流光图层ID
		this.overlaySourceId = "boundary-overlay-source";
		this.overlayLayerId = "boundary-overlay-layer";
		this.animationFrame = null; // 动画帧ID
	}

	async init() {
		try {
			// // 加载省份边界线数据
			// const [response1, response2, response3] = await Promise.all([
			// 	fetch("/config/engine/map/json/boundary/Qinghai.json"), //青海省边界线（不包含交界线）
			// 	fetch("/config/engine/map/json/boundary/Xizang.json"), //西藏省边界线（不包含交界线）
			// 	fetch("/config/engine/map/json/boundary/Border.json"), //青藏交界线
			// ]);
			// const geoJSON1 = await response1.json();
			// const geoJSON2 = await response2.json();
			// const geoJSON3 = await response3.json();

			// // 合并边界线和交界线数据
			// const LineStringGeoJSON = {
			// 	type: "FeatureCollection",
			// 	features: [
			// 		...geoJSON1.features,
			// 		...geoJSON2.features,
			// 		//   ...geoJSON3.features
			// 	],
			// };

			// // 加载县级边界线数据
			// const [response1, response2] = await Promise.all([
			// 	fetch("/config/engine/map/json/boundary/Qinghai_county.json"),
			// 	fetch("/config/engine/map/json/boundary/Xizang_county.json"),
			// ]);
			// const geoJSON1 = await response1.json();
			// const geoJSON2 = await response2.json();
			// const features = [...geoJSON1.features, ...geoJSON2.features];

			// features.forEach((feature, index) => {
			// 	const sourceId = this.baseSourceId + "-" + index;
			// 	const layerId = this.baseLayerId + "-" + index;

			// 	this.map.addSource(sourceId, {
			// 		type: "geojson",
			// 		data: feature,
			// 		lineMetrics: true,
			// 	});

			// 	this.map.addLayer({
			// 		id: layerId,
			// 		type: "line",
			// 		source: sourceId,
			// 		paint: {
			// 			"line-color": "#5F9BC1",
			// 			"line-width": 2,
			// 			"line-blur": 4,
			// 			"line-opacity": 0.25,
			// 		},
			// 		layout: {
			// 			"line-cap": "round",
			// 			"line-join": "round",
			// 		},
			// 	});
			// });

			const response = await fetch("/config/engine/map/json/boundary/Polygon.json");
			const PolygonGeoJson = await response.json();

			const coordinates = [...PolygonGeoJson.features[0].geometry.coordinates[0][0]];

			const bounds = new maplibregl.LngLatBounds();
			coordinates.forEach((coord) => bounds.extend(coord));
			const northWest = [bounds.getWest(), bounds.getNorth()];
			const northEast = [bounds.getEast(), bounds.getNorth()];
			const southWest = [bounds.getWest(), bounds.getSouth()];
			const southEast = [bounds.getEast(), bounds.getSouth()];

			this.map.addSource(this.overlaySourceId, {
				type: "image",
				url: bloom,
				coordinates: [
					northWest, // top-left
					northEast, // top-right
					southEast, // bottom-right
					southWest, // bottom-left
				],
			});

			this.map.addLayer({
				id: this.overlayLayerId,
				type: "raster",
				source: this.overlaySourceId,
				paint: {
					"raster-opacity": 1.0,
				},
			});

			// this.map.addSource(this.glowSourceId, {
			// 	type: "geojson",
			// 	data: PolygonGeoJson,
			// });

			// this.map.addLayer({
			// 	id: this.glowLayerId,
			// 	type: "fill",
			// 	source: this.glowSourceId,
			// 	layout: {},
			// 	paint: {
			// 		"fill-color": "rgba(95,155,193,0.6)", // 填充颜色
			// 		"fill-opacity": 0.5,
			// 	},
			// });

			this.map.on("click", (e) => {
				console.log(e);
				const { lngLat } = e;
				const { lng, lat } = lngLat;
				console.log(lng + "," + lat);
			});

			// 合并边界线和交界线数据
			const BoundaryGeoJSON = () => {
				const coordinates = [...PolygonGeoJson.features[0].geometry.coordinates[0][0]];
				const doubleCoordinates = coordinates.concat(coordinates); // 拼接两圈
				return {
					type: "FeatureCollection",
					features: [
						{
							type: "Feature",
							geometry: {
								type: "LineString",
								coordinates: doubleCoordinates,
							},
						},
					],
				};
			};
			// 添加数据源
			this.map.addSource(this.flowSourceId, {
				type: "geojson",
				data: BoundaryGeoJSON(),
				lineMetrics: true,
			});

			// 添加流光边界线图层
			this.map.addLayer({
				id: this.flowLayerId,
				type: "line",
				source: this.flowSourceId,
				paint: {
					"line-color": "transparent",
					"line-width": 4,
				},
				layout: {
					"line-join": "round",
					"line-cap": "round",
				},
			});

			// 启动流光动画
			this.startFlowLightAnimation();
		} catch (error) {
			console.error("加载试验段数据失败:", error);
			return;
		}
	}

	startFlowLightAnimation() {
		if (this.animationFrame) {
			cancelAnimationFrame(this.animationFrame);
			this.animationFrame = null;
		}

		let startTime = performance.now();
		const duration = 16000; // 流光完整周期时间

		// 创建多个流光点，确保连贯性
		const flowPoints = 2; // 同时存在的流光点数量

		const animate = () => {
			const currentTime = performance.now();
			const elapsed = currentTime - startTime;

			// 更新流光图层的渐变位置
			if (this.map && this.map.getLayer(this.flowLayerId)) {
				try {
					// 使用简化的方法创建渐变
					// 避免复杂的点集合和排序，直接使用固定的渐变模式
					const t = (elapsed / duration) % 1; // 当前动画进度 (0-1)

					// 创建基础渐变
					const gradientStops = [
						"interpolate",
						["linear"],
						["line-progress"],
						0,
						"rgba(255,142,72,0)", // 起始点始终透明
					];

					// 为每个流光点添加固定间隔的渐变点
					for (let i = 0; i < flowPoints; i++) {
						// 计算当前流光点的位置 (0-1范围内)
						const pointPos = (t + i / flowPoints) % 1;

						// 计算流光前后的位置，确保在0-1范围内
						const beforePos = Math.max(0, pointPos - 0.1);
						const startPos = Math.max(0, pointPos - 0.05);
						const endPos = Math.min(1, pointPos + 0.05);
						const afterPos = Math.min(1, pointPos + 0.1);

						// 确保位置值严格递增且不重复
						// 只有当位置值大于前一个位置时才添加
						if (beforePos > gradientStops[gradientStops.length - 2]) {
							gradientStops.push(
								beforePos,
								"rgba(255,142,72,0)" // 流光前透明
							);
						}

						if (startPos > gradientStops[gradientStops.length - 2]) {
							gradientStops.push(
								startPos,
								"rgba(0, 129, 222, 1) " // 流光开始过渡
							);
						}

						if (pointPos > gradientStops[gradientStops.length - 2]) {
							gradientStops.push(
								pointPos,
								"rgba(7, 98, 232, 1)" // 流光中心点
							);
						}

						if (endPos > gradientStops[gradientStops.length - 2]) {
							gradientStops.push(
								endPos,
								"rgba(0, 240, 255, 1)" // 流光结束过渡
							);
						}

						if (afterPos > gradientStops[gradientStops.length - 2]) {
							gradientStops.push(
								afterPos,
								"rgba(255,142,72,0)" // 流光后透明
							);
						}
					}

					// 确保结束点存在且值为1
					if (gradientStops[gradientStops.length - 2] < 1) {
						gradientStops.push(
							1,
							"rgba(255,142,72,0)" // 结束点透明
						);
					}

					// 应用渐变
					this.map.setPaintProperty(this.flowLayerId, "line-gradient", gradientStops);

					// 继续下一帧动画
					this.animationFrame = requestAnimationFrame(animate);
				} catch (error) {
					console.warn("流光动画更新失败:", error);
					// 出错时尝试重新启动动画
					setTimeout(() => this.startFlowLightAnimation(), 1000);
				}
			} else if (this.visible) {
				// 如果图层不存在但应该可见，尝试重新创建
				console.warn("流光图层不存在，尝试重新初始化");
				setTimeout(() => this.startFlowLightAnimation(), 1000);
			}
		};

		// 开始动画循环
		this.animationFrame = requestAnimationFrame(animate);
	}

	destroy() {
		if (this.animationFrame) {
			cancelAnimationFrame(this.animationFrame);
			this.animationFrame = null;
		}

		if (this.map.getLayer(this.flowLayerId)) {
			this.map.removeLayer(this.flowLayerId);
		}

		if (this.map.getLayer(this.glowLayerId)) {
			this.map.removeLayer(this.glowLayerId);
		}

		if (this.map.getLayer(this.baseLayerId)) {
			this.map.removeLayer(this.baseLayerId);
		}

		if (this.map.getSource(this.flowSourceId)) {
			this.map.removeSource(this.flowSourceId);
		}

		if (this.map.getSource(this.glowSourceId)) {
			this.map.removeSource(this.glowSourceId);
		}

		if (this.map.getSource(this.baseSourceId)) {
			this.map.removeSource(this.baseSourceId);
		}
	}
}

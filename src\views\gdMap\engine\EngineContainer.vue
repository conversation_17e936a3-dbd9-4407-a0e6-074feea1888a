<template>
	<div class="engine-container">
		<component
			v-for="engine in engineComponents"
			:key="engine.type"
			:is="engine.component"
			v-show="activeEngineType === engine.type"
			:options="options"
			:ref="setEngineRef(engine.type)"
			@engine-ready="handleEngineReady(engine.type, $event)"
			@engine-error="handleEngineError(engine.type, $event)"
			class="engine-instance"
			:class="{ active: activeEngineType === engine.type }"
		/>
	</div>
</template>

<script setup>
import { ref, shallowRef, onMounted, onBeforeUnmount, watch, defineAsyncComponent } from "vue";
import emitter from "@/utils/emitter"; // 添加emitter导入

// 使用异步组件懒加载引擎
const MapLibreEngine = defineAsyncComponent(() => import("./components/MapLibreEngine/index.vue"));
const UnrealEngine = defineAsyncComponent(() => import("./components/UnrealEngine/index.vue"));
const BIMFaceEngine = defineAsyncComponent(() => import("./components/BIMFaceEngine/index.vue"));

const props = defineProps({
	// 初始引擎类型
	initialType: {
		type: String,
		default: "maplibre",
		validator: (value) => ["maplibre", "unreal", "bimface"].includes(value),
	},
	// 引擎初始化参数
	options: {
		type: Object,
		default: () => ({}),
	},
	// 是否预加载所有引擎
	preloadAll: {
		type: Boolean,
		default: false,
	},
});

// 引擎组件映射
const engineComponents = shallowRef([
	{ type: "maplibre", component: MapLibreEngine },
	{ type: "unreal", component: UnrealEngine },
	{ type: "bimface", component: BIMFaceEngine },
]);

// 当前活跃的引擎类型
const activeEngineType = ref(null);
// 引擎实例引用
const engineRefs = ref({});
// 引擎状态
const engineStatus = ref({
	maplibre: { initialized: false, loading: false },
	unreal: { initialized: false, loading: false },
	bimface: { initialized: false, loading: false },
});
// 引擎历史记录，用于返回上一个引擎
const engineHistory = ref([]);

// 处理引擎就绪事件
const handleEngineReady = (type, engine) => {
	engineStatus.value[type].initialized = true;
	engineStatus.value[type].loading = false;
	// console.log(`引擎 ${type} 已就绪`);
};

// 处理引擎错误事件
const handleEngineError = (type, error) => {
	engineStatus.value[type].loading = false;
	// console.error(`引擎 ${type} 初始化失败:`, error);
};

// 初始化指定类型的引擎
// 添加设置引擎引用的方法
const setEngineRef = (type) => {
	return (el) => {
		if (el) {
			engineRefs.value[type] = el;
			// console.log(`引擎 ${type} ref 已设置:`, el);
		}
	};
};

// 修改初始化引擎方法
const initEngine = async (type) => {
	if (!type || engineStatus.value[type].loading) return null;

	// 检查引擎是否已初始化，如果已初始化则直接激活
	if (engineStatus.value[type].initialized) {
		console.log(`引擎 ${type} 已初始化，直接激活`);
		// 更新活跃引擎和历史记录
		if (activeEngineType.value && activeEngineType.value !== type) {
			// 暂停当前活跃引擎
			const currentEngineRef = engineRefs.value[activeEngineType.value];
			if (currentEngineRef && typeof currentEngineRef.pause === "function") {
				currentEngineRef.pause();
			}

			// 添加到历史记录
			if (!engineHistory.value.includes(activeEngineType.value)) {
				engineHistory.value.push(activeEngineType.value);
			}
		}

		// 激活新引擎
		activeEngineType.value = type;
		const engineRef = engineRefs.value[type];
		if (engineRef && typeof engineRef.resume === "function") {
			engineRef.resume();
		}

		return engineRef;
	}

	// 如果引擎未初始化，则进行初始化
	engineStatus.value[type].loading = true;

	// 等待引擎实例准备就绪
	let retryCount = 0;
	const maxRetries = 15;

	while (!engineRefs.value[type] && retryCount < maxRetries) {
		await new Promise((resolve) => setTimeout(resolve, 100));
		retryCount++;
	}

	const engineRef = engineRefs.value[type];
	if (!engineRef) {
		console.error(`引擎 ${type} 实例未找到`);
		engineStatus.value[type].loading = false;
		return null;
	}

	try {
		if (typeof engineRef.init === "function") {
			await engineRef.init(props.options);
		}
	} catch (error) {
		console.e;
		return null;
	}

	engineStatus.value[type].loading = false;

	// 更新活跃引擎和历史记录
	if (activeEngineType.value && activeEngineType.value !== type) {
		// 暂停当前活跃引擎
		const currentEngineRef = engineRefs.value[activeEngineType.value];
		if (currentEngineRef && typeof currentEngineRef.pause === "function") {
			currentEngineRef.pause();
		}

		// 添加到历史记录
		if (!engineHistory.value.includes(activeEngineType.value)) {
			engineHistory.value.push(activeEngineType.value);
		}
	}

	// 激活新引擎
	activeEngineType.value = type;
	if (engineRef && typeof engineRef.resume === "function") {
		engineRef.resume();
	}

	return engineRef;
};

// 切换到指定类型的引擎
const switchToEngine = async (type) => {
	if (!Object.keys(engineStatus.value).includes(type)) {
		console.error(`不支持的引擎类型: ${type}`);
		return null;
	}

	console.log(`切换到引擎: ${type}, 当前引擎: ${activeEngineType.value}`);

	// 如果已经是当前引擎，则不需要切换
	if (type === activeEngineType.value) {
		console.log(`已经是 ${type} 引擎，无需切换`);
		return engineRefs.value[type];
	}

	let result = null;

	// 检查引擎是否已初始化
	if (engineStatus.value[type].initialized) {
		console.log(`引擎 ${type} 已初始化，直接激活`);

		// 暂停当前活跃引擎
		if (activeEngineType.value) {
			const currentEngineRef = engineRefs.value[activeEngineType.value];
			if (currentEngineRef) {
				// 检查pause方法是否存在
				if (typeof currentEngineRef.pause === "function") {
					try {
						currentEngineRef.pause();
					} catch (error) {
						console.warn(`引擎 ${activeEngineType.value} 暂停失败:`, error);
					}
				} else {
					console.log(`引擎 ${activeEngineType.value} 没有提供pause方法`);
				}
			}

			// 添加到历史记录
			if (!engineHistory.value.includes(activeEngineType.value)) {
				engineHistory.value.push(activeEngineType.value);
			}
		}

		// 激活目标引擎
		activeEngineType.value = type;
		const engineRef = engineRefs.value[type];
		if (engineRef) {
			// 检查resume方法是否存在
			if (typeof engineRef.resume === "function") {
				try {
					engineRef.resume();
				} catch (error) {
					console.warn(`引擎 ${type} 恢复失败:`, error);
				}
			} else {
				console.log(`引擎 ${type} 没有提供resume方法`);
			}
		}

		result = engineRef;
	} else {
		// 如果引擎未初始化，则进行初始化
		result = await initEngine(type);
	}

	// 发出引擎切换事件，方便其他组件感知
	if (result) {
		console.log(`成功切换到 ${type} 引擎`);
		// 添加引擎加载完成事件
		emitter.$emit("engineOnLoad", {
			type: type,
			instance: result,
			status: engineStatus.value[type],
		});
	}

	return result;
};

// 获取当前活跃的引擎实例
const getActiveEngine = () => {
	if (!activeEngineType.value) return null;
	return engineRefs.value[activeEngineType.value];
};

// 优化性能的方法
const optimizePerformance = () => {
	// 对非活跃引擎进行资源释放或降低更新频率
	Object.keys(engineRefs.value).forEach((type) => {
		const engineRef = engineRefs.value[type];
		if (type !== activeEngineType.value && engineRef) {
			// 如果引擎提供了降低性能的方法，则调用
			if (typeof engineRef.lowerPerformance === "function") {
				engineRef.lowerPerformance();
			}
		}
	});
};

// 组件挂载时初始化默认引擎
onMounted(async () => {
	// 如果设置了预加载所有引擎
	if (props.preloadAll) {
		// 预加载但不初始化其他引擎
		Object.keys(engineStatus.value).forEach((type) => {
			if (type !== props.initialType) {
				// 标记为预加载状态
				engineStatus.value[type].preloaded = true;
			}
		});
	}

	// 初始化默认引擎
	await initEngine(props.initialType);

	// 添加重置视角事件监听
	emitter.$on("resetMapView", handleResetView);
});

// 组件卸载前清理资源
onBeforeUnmount(() => {
	// 销毁所有引擎实例
	Object.keys(engineRefs.value).forEach((type) => {
		const engineRef = engineRefs.value[type];
		if (engineRef && typeof engineRef.destroy === "function") {
			engineRef.destroy();
		}
	});

	// 移除事件监听
	emitter.$off("resetMapView", handleResetView);
});

// 处理重置视角事件
const handleResetView = () => {
	const activeEngine = getActiveEngine();
	if (!activeEngine) {
		console.warn("没有活跃的引擎实例，无法重置视角");
		return;
	}

	// 根据当前引擎类型调用相应的重置视角方法
	if (activeEngineType.value === "maplibre") {
		if (typeof activeEngine.resetMapView === "function") {
			activeEngine.resetMapView();
		} else {
			console.warn("当前MapLibre引擎实例没有提供resetMapView方法");
		}
	} else if (activeEngineType.value === "unreal") {
		if (typeof activeEngine.resetCamera === "function") {
			activeEngine.resetCamera();
		} else {
			console.warn("当前Unreal引擎实例没有提供resetCamera方法");
		}
	} else if (activeEngineType.value === "bimface") {
		if (typeof activeEngine.resetView === "function") {
			activeEngine.resetView();
		} else {
			console.warn("当前BIMFace引擎实例没有提供resetView方法");
		}
	}

	console.log(`已重置${activeEngineType.value}引擎视角`);
};

// 监听props变化，切换引擎
watch(
	() => props.initialType,
	async (newType) => {
		if (newType && newType !== activeEngineType.value) {
			await switchToEngine(newType);
		}
	}
);

// 监听活跃引擎变化，优化性能
watch(
	() => activeEngineType.value,
	() => {
		optimizePerformance();
	}
);

// 对外暴露方法
defineExpose({
	initEngine,
	switchToEngine,
	getActiveEngine,
	engineStatus,
	activeEngineType,
});
</script>

<style scoped>
.engine-container {
	position: relative;
	width: 100%;
	height: 100%;
	overflow: hidden;
}

.engine-instance {
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
}
</style>

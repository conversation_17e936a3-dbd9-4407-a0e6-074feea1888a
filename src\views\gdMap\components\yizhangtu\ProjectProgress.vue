<template>
	<div class="project-progress">
		<a-spin class="card-loading" dot :loading="loading">
			<BasicCard title="工程进度">
				<div class="card-content">
					<!-- 顶部指标区域 -->
					<div class="progress-indicators">
						<div class="indicator-item">
							<div class="indicator-icon">
								<img src="@/assets/modules/yizhangtu/icons/total-mileage.svg" alt="总里程" />
							</div>
							<div class="indicator-content">
								<div class="indicator-value">
									{{ progressData.totalMileage }}<span class="unit">km</span>
								</div>
								<div class="indicator-label">总里程</div>
							</div>
						</div>
						<div class="indicator-item">
							<div class="indicator-icon">
								<img src="@/assets/modules/yizhangtu/icons/built-mileage.svg" alt="已建里程" />
							</div>
							<div class="indicator-content">
								<div class="indicator-value">
									{{ progressData.completedMileage }}<span class="unit">km</span>
								</div>
								<div class="indicator-label">已建里程</div>
							</div>
						</div>
						<div class="indicator-item">
							<div class="indicator-icon">
								<img src="@/assets/modules/yizhangtu/icons/project-progress.svg" alt="工程进度" />
							</div>
							<div class="indicator-content">
								<div class="indicator-value">
									{{ completionPercentage }}<span class="unit">%</span>
								</div>
								<div class="indicator-label">工程进度</div>
							</div>
						</div>
					</div>

					<!-- 工期信息 -->
					<div class="project-duration">
						<div class="project-duration-header">
							<div class="duration-info">
								<span class="days-count">{{ progressData.actualDuration }}</span>
								<span class="unit">天</span>
								<span class="days-separator">/</span>
								<span class="total-days">{{ progressData.plannedDuration }}</span>
								<span class="unit">天</span>
							</div>
							<div class="duration-tags">
								<span class="tag actual">实际进度</span>
								<span class="tag plan">计划进度</span>
							</div>
						</div>

						<div class="project-duration-content">
							<div class="progress-section">
								<div class="progress-status-wrapper">
									<div class="progress-status" v-if="progressData.overCompletion !== '0'">
										超额完成{{ progressData.overCompletion }}%
									</div>
									<div class="progress-status" v-else>进行中</div>
									<div class="date-picker-container">
										<div class="current-date">
											<div class="date-icon">
												<img src="@/assets/modules/yizhangtu/icons/calendar-today.svg" alt="日期" />
											</div>
											<div class="date-text">{{ currentDate }}</div>
										</div>
									</div>
								</div>

								<div class="progress-bar">
									<div class="progress-track"></div>
									<div
										class="progress-actual"
										:style="{ width: actualProgressPercentage + '%' }"
									></div>
									<div class="progress-plan" :style="{ width: planProgressPercentage + '%' }"></div>
									<div
										class="progress-current-date-marker"
										:style="{ left: currentDatePosition + '%' }"
									></div>
								</div>
								<div class="progress-dates">
									<div class="progress-dates-item">
										<div class="date-label">开工</div>
										<div class="date-content">
											{{ formatDate(progressData.startDate) }}
										</div>
									</div>
									<div class="progress-dates-item">
										<div class="date-label">竣工</div>
										<div class="date-content">
											{{ formatDate(progressData.endDate) }}
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- 关键节点 -->
					<div class="milestone-section">
						<div class="section-header">
							<div class="section-icon">
								<img src="@/assets/modules/common/icons/card-header-right-arrow.svg" />
							</div>
							<div class="section-title">关键节点</div>
						</div>

						<div class="time-line-wrap">
							<Timeline
								:items="milestones"
								:currentDate="timeLineCurrentDate"
								:visibleCount="4"
								@select="handleNodeSelect"
							/>
						</div>
					</div>
				</div>
			</BasicCard>
		</a-spin>
	</div>
</template>

<script setup>
import BasicCard from "@/components/BasicCard/index.vue";
import Timeline from "@/components/Timeline/index.vue";
import { ref, onMounted, onBeforeUnmount, computed } from "vue";
import emitter from "@/utils/emitter";
import {
	getProjectProgressData,
	getTimelineMilestones,
} from "@/views/gdMap/services/yizhangtu.mock.js";

// 当前日期
const currentDate = ref("");

// 项目进度数据
const progressData = ref({
	totalMileage: 0,
	completedMileage: 0,
	plannedDuration: 0,
	actualDuration: 0,
	startDate: "",
	endDate: "",
	overCompletion: "0",
});

// 当前选中的工区
const currentArea = ref("all");

// 计算完成百分比
const completionPercentage = computed(() => {
	if (progressData.value.totalMileage <= 0) return 0;
	return Math.round((progressData.value.completedMileage / progressData.value.totalMileage) * 100);
});

// 计算实际进度百分比
const actualProgressPercentage = computed(() => {
	if (progressData.value.plannedDuration <= 0) return 0;
	return Math.round((progressData.value.actualDuration / progressData.value.plannedDuration) * 100);
});

// 计算计划进度百分比（假设按照时间线性计算）
const planProgressPercentage = computed(() => {
	if (!progressData.value.startDate || !progressData.value.endDate) return 0;

	const start = new Date(formatDate(progressData.value.startDate)).getTime();
	const end = new Date(formatDate(progressData.value.endDate)).getTime();
	const now = new Date().getTime();

	if (now <= start) return 0;
	if (now >= end) return 100;

	return Math.round(((now - start) / (end - start)) * 100);
});

// 当前日期，可以是动态的
const timeLineCurrentDate = ref("2025-02-15");

// 时间轴数据
const milestones = ref([]);

// 加载时间轴数据
const loadMilestones = async (areaKey = "all") => {
	try {
		const data = await getTimelineMilestones(areaKey);

		// console.log('data', data);
		milestones.value = data;
	} catch (error) {
		console.error("加载时间轴数据失败:", error);
	}
};

// 当前选中的里程碑
const selectedMilestone = ref(null);

// 处理节点选择事件
const handleNodeSelect = (node) => {
	selectedMilestone.value = node;
	console.log("选中节点:", node);
};

// 计算当前日期在进度条上的位置（百分比）
const currentDatePosition = computed(() => {
	if (!currentDate.value || !progressData.value.startDate || !progressData.value.endDate) return 0;

	const start = new Date(formatDate(progressData.value.startDate)).getTime();
	const end = new Date(formatDate(progressData.value.endDate)).getTime();
	const current = new Date(currentDate.value).getTime();

	// 如果当前日期在开始日期之前，返回0
	if (current < start) return 0;
	// 如果当前日期在结束日期之后，返回100
	if (current > end) return 100;

	// 计算百分比位置
	return ((current - start) / (end - start)) * 100;
});

// 格式化日期 (将 2024/10/31 转换为 2024-10-31)
function formatDate(dateStr) {
	if (!dateStr) return "";
	return dateStr.replace(/\//g, "-");
}

// 更新日期的函数
const updateDate = () => {
	const now = new Date();
	const year = now.getFullYear();
	const month = String(now.getMonth() + 1).padStart(2, "0");
	const day = String(now.getDate()).padStart(2, "0");
	currentDate.value = `${year}-${month}-${day}`;
};

// 加载项目进度数据
const loadProjectProgress = async (areaKey = "all") => {
	loading.value = true;
	try {
		const data = await getProjectProgressData(areaKey);
		progressData.value = data;

		// 加载时间轴数据
		await loadMilestones(areaKey);
	} catch (error) {
		console.error("加载项目进度数据失败:", error);
	} finally {
		setTimeout(() => {
			loading.value = false;
		}, 1000); // 延迟一点时间确保DOM已更新
	}
};

// 监听工区切换事件
const handleAreaChange = (areaKey) => {
	currentArea.value = areaKey;
	loadProjectProgress(areaKey);
};

// 定时器
let timer = null;

const loading = ref(false);

onMounted(() => {
	// 初始化日期
	updateDate();

	// 加载项目进度数据
	loadProjectProgress();

	// 监听工区切换事件
	emitter.$on("pageNavChange", handleAreaChange);

	// 设置定时器，每天更新一次日期
	timer = setInterval(updateDate, 86400000); // 24小时 = 86400000毫秒
});

onBeforeUnmount(() => {
	// 清除定时器
	if (timer) {
		clearInterval(timer);
		timer = null;
	}
	// 移除事件监听
	emitter.$off("pageNavChange", handleAreaChange);
});
</script>

<style lang="scss" scoped>
.project-progress {
	height: 340px;
	display: flex;
	flex-direction: column;

	.card-content {
		display: flex;
		flex-direction: column;
		width: 100%;
		&-main {
			flex: 1;
			overflow: hidden;
		}
	}

	// 顶部指标区域
	.progress-indicators {
		height: 62px;
		display: flex;
		justify-content: space-between;
		align-items: center;

		.indicator-item {
			height: 38px;
			display: flex;
			align-items: center;
			.indicator-icon {
				margin-right: 4px;
				width: 44px;
				height: 38px;
				display: flex;
				justify-content: center;
				align-items: center;

				img {
					width: 100%;
					height: 100%;
				}
			}

			.indicator-content {
				.indicator-value {
					margin-bottom: 2px;
					font-family: D-DIN-PRO-Bold D-DIN-PRO;
					font-weight: bold;
					font-size: 20px;
					color: #0783fa;
					.unit {
						margin-left: 2px;
						font-size: 10px;
						color: #ffffff;
						opacity: 0.8;
					}
				}
				.indicator-label {
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-size: 12px;
					color: #ffffff;
					opacity: 0.8;
				}
			}
		}
	}

	// 工期信息
	.project-duration {
		margin-bottom: 8px;
		display: flex;
		flex-direction: column;
		height: 128px;
		background: rgba(10, 35, 75, 0.6);
		border: 1px solid rgba(23, 66, 114, 0.6);

		&-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 10px;
			height: 35px;
			border-bottom: 1px solid rgba(23, 66, 114, 0.6);
			.duration-info {
				.days-count {
					font-family: D-DIN-PRO-Bold, D-DIN-PRO;
					font-weight: bold;
					font-size: 16px;
					color: #06d239;
				}
				.unit {
					margin-left: 2px;
					font-family: D-DIN-PRO-Medium, D-DIN-PRO;
					font-weight: 500;
					font-size: 10px;
					color: #cdd0d4;
				}

				.days-separator {
					margin: 0 4px;
					font-family: D-DIN-PRO-Medium, D-DIN-PRO;
					font-weight: 500;
					font-size: 14px;
					color: #cdd0d4;
				}

				.total-days {
					font-family: D-DIN-PRO-Bold, D-DIN-PRO;
					font-weight: bold;
					font-size: 16px;
					color: #0783fa;
				}
			}

			.duration-tags {
				display: flex;
				gap: 6px;
				font-family: Roboto-Regular, Roboto;
				font-size: 10px;
				color: #ffffff;
				.tag {
					display: flex;
					align-items: center;

					&::before {
						content: "";
						display: inline-block;
						width: 6px;
						height: 6px;
						border-radius: 50%;
						margin-right: 4px;
					}
					&.actual {
						&::before {
							background-color: #06d239;
						}
					}
					&.plan {
						&::before {
							background-color: #0783fa;
						}
					}
				}
			}
		}

		&-content {
			//
		}
	}

	// 进度条
	.progress-section {
		margin-bottom: 10px;
		padding: 10px;

		.progress-status-wrapper {
			margin-bottom: 10px;
			display: flex;
			justify-content: space-between;
			align-items: center;
		}

		.progress-status {
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-size: 12px;
			color: #06d239;
		}

		.progress-bar {
			height: 6px;
			background: #393a4a;
			position: relative;
			margin-bottom: 7px;

			.progress-track {
				position: absolute;
				left: 0;
				top: 0;
				bottom: 0;
				width: 100%;
				background: rgba(7, 131, 250, 0.2);
				border-radius: 5px;
			}

			.progress-actual {
				position: absolute;
				left: 0;
				top: 0;
				bottom: 0;
				background: #06d239;
			}

			.progress-plan {
				position: absolute;
				left: 0;
				top: 0;
				bottom: 0;
				background: #0783fa;
			}

			.progress-current-date-marker {
				position: absolute;
				width: 2px;
				height: 12px;
				background: #ffffff;
				border-radius: 0px 0px 0px 0px;
				top: -3px; // 使标记垂直居中并向上延伸
				transform: translateX(-50%); // 使标记水平居中于其位置点
				z-index: 2; // 确保标记显示在最上层
				box-shadow: 0 0 2px rgba(255, 255, 255, 0.8); // 添加发光效果
			}
		}

		.progress-dates {
			display: flex;
			justify-content: space-between;
			font-size: 12px;
			color: #ffffff;
			&-item {
				display: flex;
				align-items: center;
			}
			.date-label {
				margin-right: 4px;
				font-family: Source Han Sans CN-Bold, Source Han Sans CN;
				font-weight: bold;
			}
			.date-content {
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				opacity: 0.8;
			}
		}
	}

	// 关键节点
	.milestone-section {
		background: rgba(10, 35, 75, 0.6);
		border: 1px solid rgba(23, 66, 114, 0.6);

		.section-header {
			padding: 0 12px;
			display: flex;
			align-items: center;
			height: 36px;
			border-bottom: 1px solid rgba(23, 66, 114, 0.6);

			.section-icon {
				margin-right: 5px;
				width: 24px;
				height: 16px;
				img {
					width: 100%;
					height: 100%;
				}
			}

			.section-title {
				margin-right: 3px;
				font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
				font-size: 16px;
				color: #ffffff;
				text-shadow: 0px 0px 9px #158eff;
			}
		}

		.time-line-wrap {
			padding: 0 12px;
		}
	}
}

.date-picker-container {
	.current-date {
		display: flex;
		align-items: center; // 已有垂直居中属性
		.date-icon {
			margin-right: 3px;
			width: 12px;
			height: 12px;
			display: flex; // 添加flex布局
			align-items: center; // 添加垂直居中
			justify-content: center; // 添加水平居中

			img {
				width: 100%;
				height: 100%;
				display: block; // 确保图片作为块元素
				vertical-align: middle; // 垂直对齐
			}
		}

		.date-text {
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-size: 12px;
			color: #ffffff;
			line-height: 1; // 添加行高为1，确保文本垂直居中
			display: flex; // 添加flex布局
			align-items: center; // 添加垂直居中
			height: 12px; // 与图标高度一致
		}
	}
}
</style>

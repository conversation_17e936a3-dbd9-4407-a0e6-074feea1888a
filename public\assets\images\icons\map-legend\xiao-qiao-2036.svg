<svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#229;&#176;&#143;&#230;&#161;&#165;">
<g id="Ellipse 15" filter="url(#filter0_d_342_3758)">
<circle cx="10.9651" cy="10.7393" r="8" fill="url(#paint0_radial_342_3758)" fill-opacity="0.7"/>
<circle cx="10.9651" cy="10.7393" r="7.55492" stroke="url(#paint1_linear_342_3758)" stroke-width="0.890162"/>
</g>
<circle id="Ellipse 16" cx="11.039" cy="10.8137" r="7.07681" fill="url(#paint2_radial_342_3758)" stroke="url(#paint3_linear_342_3758)" stroke-width="0.200286"/>
<g id="&#229;&#176;&#143;&#230;&#161;&#165;_2" filter="url(#filter1_d_342_3758)">
<g id="Frame" clip-path="url(#clip0_342_3758)">
<path id="Vector" d="M15.5227 10.676H15.2026C14.8829 10.676 14.5897 10.4979 14.4424 10.2166C13.5604 8.53163 12.4116 7.68066 10.9926 7.68066C9.63317 7.68066 8.48549 8.6439 7.54651 10.2621C7.39733 10.5192 7.11826 10.676 6.81901 10.676H6.46238C6.20292 10.676 5.99255 10.8844 5.99255 11.1415V12.2373C5.99255 12.452 6.16818 12.626 6.38482 12.626H9.6938V10.3949C9.6938 9.68421 10.2753 9.10809 10.9926 9.10809C11.7098 9.10809 12.2913 9.68421 12.2913 10.3949V12.626H15.6003C15.8169 12.626 15.9926 12.452 15.9926 12.2373V11.1415C15.9926 10.8844 15.7822 10.676 15.5227 10.676Z" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_342_3758" x="0.294602" y="0.0687718" width="21.341" height="21.341" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.33524"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.388235 0 0 0 0 0.640523 0 0 0 0 0.807843 0 0 0 0.51 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_342_3758"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3758" result="shape"/>
</filter>
<filter id="filter1_d_342_3758" x="5.90063" y="5.15332" width="10" height="10.4451" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.445081"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.150327 0 0 0 0 0.247059 0 0 0 0 0.145098 0 0 0 0.75 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_342_3758"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3758" result="shape"/>
</filter>
<radialGradient id="paint0_radial_342_3758" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(10.9651 10.7393) scale(8)">
<stop stop-color="#D3D3D3"/>
<stop offset="0.03" stop-color="#D3D3D3" stop-opacity="0.9216"/>
<stop offset="0.13" stop-color="#D3D3D3" stop-opacity="0.7056"/>
<stop offset="0.23" stop-color="#D3D3D3" stop-opacity="0.5329"/>
<stop offset="0.35" stop-color="#D3D3D3" stop-opacity="0.4225"/>
<stop offset="0.47" stop-color="#D3D3D3" stop-opacity="0.3364"/>
<stop offset="0.6" stop-color="#D3D3D3" stop-opacity="0.2916"/>
<stop offset="0.76" stop-color="#D3D3D3" stop-opacity="0.2601"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.5"/>
</radialGradient>
<linearGradient id="paint1_linear_342_3758" x1="10.9651" y1="18.7393" x2="10.9651" y2="2.73926" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<radialGradient id="paint2_radial_342_3758" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(11.039 10.8137) scale(7.17695)">
<stop stop-color="#D3D3D3"/>
<stop offset="1" stop-color="#0B103B" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint3_linear_342_3758" x1="11.039" y1="17.9906" x2="11.039" y2="3.63672" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<clipPath id="clip0_342_3758">
<rect width="10" height="10" fill="white" transform="translate(5.90063 5.15332)"/>
</clipPath>
</defs>
</svg>

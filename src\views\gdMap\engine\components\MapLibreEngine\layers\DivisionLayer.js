import maplibregl from "maplibre-gl";
import emitter from "@/utils/emitter";
// import seamlessDots from "/src/assets/modules/common/bg/seamless-dots.png";
import DivisionMarkerVue from "@/views/gdMap/engine/components/MapLibreEngine/components/DivisionMarker.vue";

const minzoom = 8;
const defaultConfig = {
	fill: {
		opacity: 0.15,
		color: "#3C9EFF",
	},
	line: {
		opacity: 0.3,
		color: "#3C9EFF",
	},
	glow: {
		opacity: 0,
	},
};

const activeConfig = {
	fill: {
		opacity: 0.08,
	},
	line: {
		opacity: 0.8,
	},
	glow: {
		opacity: 1,
	},
};

/**
 * 分部区域图层
 */
export class DivisionLayer {
	constructor(map) {
		this.map = map;
		this.imageId = "division-polygon-pattern"; // 分区填充图层的图案
		this.polygonSourceId = "division-polygon-source";
		this.polygonFillLayerId = "division-polygon-fill-layer"; // 分区填充图层
		this.polygonLineLayerId = "division-polygon-line-layer"; // 分区边界线图层
		this.polygonGlowLayerId = "division-polygon-glow-layer"; // 分区发光图层
		// this.ZOrder = 4;
		this.IdMap = new Map();
	}

	async init() {
		await Promise.all([
			// this.loadImage(),
			this.loadPolygonLayer(),
			this.loadPointLayer(),
		]);
	}

	// async loadImage() {
	// 	const image = await fetch(seamlessDots)
	// 		.then((res) => res.blob())
	// 		.then((blob) => createImageBitmap(blob));
	// 	this.map.addImage(this.imageId, image);
	// }

	async loadPolygonLayer() {
		const response = await fetch("/config/engine/map/json/division/polygon.json");
		const geoJson = await response.json();

		geoJson.features.forEach((feature, index) => {
			const sourceId = this.polygonSourceId + "-" + index;
			const fillLayerId = this.polygonFillLayerId + "-" + index;
			const lineLayerId = this.polygonLineLayerId + "-" + index;
			const glowLayerId = this.polygonGlowLayerId + "-" + index;

			this.map.addSource(sourceId, {
				type: "geojson",
				data: feature,
				lineMetrics: true,
				tolerance: 0.001,
				lineMetrics: true, // 确保线条连续性
				promoteId: true,
				cluster: false,
			});

			this.map.addLayer({
				id: fillLayerId,
				type: "fill",
				source: sourceId,
				paint: {
					// "fill-pattern": this.imageId,
					"fill-color": defaultConfig.fill.color,
					"fill-opacity": defaultConfig.fill.opacity,
				},
			});
			this.IdMap.set("fill" + index, { sourceId, layerId: fillLayerId });

			this.map.addLayer({
				id: lineLayerId,
				type: "line",
				source: sourceId,
				paint: {
					"line-width": 1,
					"line-color": defaultConfig.line.color,
					"line-opacity": defaultConfig.line.opacity,
				},
			});
			this.IdMap.set("line" + index, { sourceId, layerId: lineLayerId });

			this.map.addLayer({
				id: glowLayerId,
				type: "line",
				source: sourceId,
				paint: {
					"line-color": "#fff",
					"line-width": 20,
					"line-blur": 60,
					"line-opacity": defaultConfig.glow.opacity,
				},
			});
			this.IdMap.set("glow" + index, { sourceId, layerId: glowLayerId });

			this.map.on("click", (e) => {
				const features = this.map.queryRenderedFeatures(e.point, {
					layers: [fillLayerId],
				});
				if (features.length === 0) {
					// 点击不在 fillLayer 内
					this.map.setPaintProperty(fillLayerId, "fill-opacity", defaultConfig.fill.opacity);
					this.map.setPaintProperty(lineLayerId, "line-opacity", defaultConfig.line.opacity);
					this.map.setPaintProperty(glowLayerId, "line-opacity", defaultConfig.glow.opacity);
				}
			});

			// 双击选中
			this.map.on("dblclick", fillLayerId, (e) => {
				// 准备要发送的事件数据
				const eventData = {
					eventType: "dblclick",
					layerId: fillLayerId,
					clickPosition: e.lngLat,
					pixelPosition: e.point,
					feature: e.features[0],
					properties: e.features[0].properties,
					geometry: e.features[0].geometry,
					// center: center,
					targetZoom: minzoom + 0.1,
					timestamp: new Date().toISOString(),
				};

				// 发送双击事件到其他组件
				emitter.$emit("division-area-dblclick", eventData);

				this.flyToCenter(e);

				this.map.setPaintProperty(fillLayerId, "fill-opacity", activeConfig.fill.opacity);
				this.map.setPaintProperty(lineLayerId, "line-opacity", activeConfig.line.opacity);
				this.map.setPaintProperty(glowLayerId, "line-opacity", activeConfig.glow.opacity);
			});
		});
	}

	async loadPointLayer() {
		const response = await fetch("/config/engine/map/json/division/point.json");
		const geoJson = await response.json();

		geoJson.features.forEach((feature) => {
			const { geometry, properties } = feature;
			const el = document.createElement("div");

			const app = createApp(DivisionMarkerVue, {
				properties,
			});
			app.mount(el);

			const marker = new maplibregl.Marker({
				element: el,
			})
				.setLngLat(geometry.coordinates)
				.addTo(this.map);
			this.updateElementSize(marker, 6);
			this.updateElementVisible(marker, false);

			this.map.on("zoom", () => {
				const zoom = this.map.getZoom();
				// 根据 circle-radius 的插值规则设置 size
				let size;
				if (zoom <= 5) {
					size = 6;
				} else if (zoom <= 9) {
					// 5~9: 6~14
					size = 6 + ((zoom - 5) / (9 - 5)) * (14 - 6);
				} else if (zoom <= 13) {
					// 9~13: 14~44
					size = 14 + ((zoom - 9) / (13 - 9)) * (44 - 14);
				} else {
					size = 44;
				}
				this.updateElementSize(marker, size * 1.5);
				this.updateElementVisible(marker, zoom > minzoom);
			});
		});
	}

	updateElementSize(marker, size) {
		const element = marker.getElement();
		element.style.width = size + "px";
		element.style.height = size + "px";
		element.style.fontSize = size + "px";
	}

	updateElementVisible(marker, visible) {
		const element = marker.getElement();
		element.style.visibility = visible ? "visible" : "hidden";
	}

	flyToCenter(e) {
		const bounds = new maplibregl.LngLatBounds();
		e.features[0].geometry.coordinates[0].forEach((coord) => bounds.extend(coord));
		const center = bounds.getCenter(); // 近似中心点
		this.map.flyTo({
			center,
			zoom: minzoom + 0.1,
			duration: 3000,
			bearing: 0,
			pitch: 0,
		});
	}
}

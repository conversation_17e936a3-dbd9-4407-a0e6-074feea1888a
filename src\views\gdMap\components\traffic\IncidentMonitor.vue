<template>
	<TrafficCard title="交通事件监测">
		<div class="incident-monitor">
			<div class="target-info">
				<div class="target-item">
					<div class="target-icon">
						<jam />
					</div>
					<div class="target-content">
						<div class="target-value">{{ statusData.yongduNum || 0 }}</div>
						<div class="target-label">交通拥堵</div>
					</div>
				</div>
				<div class="target-item">
					<div class="target-icon">
						<roadClosure />
					</div>
					<div class="target-content">
						<div class="target-value">{{ statusData.fengluNum || 0 }}</div>
						<div class="target-label">道路封路</div>
					</div>
				</div>
				<div class="target-item">
					<div class="target-icon">
						<accident />
					</div>
					<div class="target-content">
						<div class="target-value">{{ statusData.shiguNum || 0 }}</div>
						<div class="target-label">交通事故</div>
					</div>
				</div>
				<div class="target-item">
					<div class="target-icon">
						<otherAccident />
					</div>
					<div class="target-content">
						<div class="target-value">{{ statusData.elseNum || 0 }}</div>
						<div class="target-label">交通管制</div>
					</div>
				</div>
			</div>
			<div class="incident-table">
				<div class="table-header">
					<div class="table-title">交通事件</div>
					<div class="table-tab-list">
						<div
							v-for="item in tabList"
							:key="item.value"
							class="table-tab-list-item"
							:class="{ 'is-active': item.value === activeTab }"
							@click="onClickTab(item)"
						>
							{{ item.label }}
						</div>
					</div>
				</div>
				<div ref="containerRef" class="scroll-container">
					<div class="table-body">
						<div
							v-for="(item, index) in listData"
							:key="index"
							class="table-row"
							:class="item.state === 1 ? 'deal' : 'unDeal'"
							@click="onClickRow(item)"
						>
							<div class="table-col icon">
								<component :is="getIcon(item.eventType)" />
							</div>
							<div class="table-col type">{{ getLabel(item.eventType) }}</div>
							<div class="table-col area">{{ item.locationZhuang }}</div>
							<div class="table-col time">{{ item.eventTime }}</div>
							<div class="table-col dealStatus">
								{{ item.state === 1 ? "已处理" : "未处理" }}
							</div>
						</div>
					</div>
				</div>
			</div>
			<div></div>
		</div>
	</TrafficCard>
</template>

<script setup>
import request from "@/utils/request";
import { useMarquee } from "@/hooks/useMarquee";
import jam from "@/assets/modules/traffic/icon/jam.svg?component";
import roadClosure from "@/assets/modules/traffic/icon/roadClosure.svg?component";
import accident from "@/assets/modules/traffic/icon/accident.svg?component";
import otherAccident from "@/assets/modules/traffic/icon/otherAccident.svg?component";
import emitter from "@/utils/emitter";

const tabList = [
	{ label: "全部", value: "" },
	{ label: "未处理", value: "0" },
	{ label: "已处理", value: "1" },
];
const eventMap = {
	1: {
		icon: jam,
		label: "交通拥堵",
	},
	2: {
		icon: accident,
		label: "交通事故",
	},
	3: {
		icon: otherAccident,
		label: "交通管制",
	},
	4: {
		icon: roadClosure,
		label: "道路封路",
	},
};

const activeTab = ref("");
const statusData = ref({});
const listData = ref();
const currentDepartmentId = ref(null);

const { containerRef, resetScroll } = useMarquee({
	speed: 10, // 滚动速度
	delay: 2000, // 滚动到底部后停顿时间
	step: 1, // 每次滚动的像素
});

// 当数据变化时，重置滚动
watch(
	listData,
	() => {
		setTimeout(resetScroll, 300); // 延迟一点时间确保DOM已更新
	},
	{ deep: true }
);

onMounted(() => {
	// 监听区域双击事件
	emitter.$on("division-area-dblclick", handleAreaChange);
	getData();
});

onBeforeUnmount(() => {
	emitter.$off("division-area-dblclick", handleAreaChange);
});

const handleAreaChange = (eventData) => {
	console.log("交通事件监测 - 接收到区域双击事件:", eventData);
	currentDepartmentId.value = eventData.properties.id;
	getData();
};

const getData = () => {
	getStatus();
	getList();
};

const getStatus = () => {
	const params = currentDepartmentId.value ? { departmentId: currentDepartmentId.value } : {};
	request.get("/api/screen/baotong/traffic/event/stat", params).then((res) => {
		if (res.code == 200) {
			statusData.value = res.data;
		}
	});
};

const getList = () => {
	const params = currentDepartmentId.value
		? { departmentId: currentDepartmentId.value, eventState: unref(activeTab) }
		: { eventState: unref(activeTab) };
	request.get("/api/screen/baotong/traffic/event/list", params).then((res) => {
		if (res.code == 200) {
			listData.value = res.data;
		}
	});
};

const onClickTab = (tab) => {
	activeTab.value = tab.value;
	getList(); // 切换tab时重新获取列表数据
};

const getIcon = (key) => {
	return eventMap[key].icon;
};

const getLabel = (key) => {
	return eventMap[key].label;
};

const onClickRow = (item) => {
	emitter.$emit("flyto-traffic-accident-marker", item);
};
</script>

<style lang="scss" scoped>
:deep(.card-body) {
	background-color: transparent;
}

.incident-monitor {
	height: 249px;
	padding-top: 8px;
	display: flex;
	flex-direction: column;

	.target-info {
		padding: 0 12px 12px;
		display: flex;
		justify-content: center;
		column-gap: 12px;

		.target-item {
			display: flex;
			align-items: center;

			&:nth-of-type(1) .target-icon {
				background-color: rgba(203, 143, 143, 0.1);
				&::after {
					background-color: rgba(255, 143, 143, 0.22);
					border-color: #cb335c;
				}
			}

			&:nth-of-type(2) .target-icon {
				background-color: rgba(255, 173, 41, 0.1);
				&::after {
					background-color: rgba(255, 173, 41, 0.22);
					border-color: #ffad29;
				}
			}

			&:nth-of-type(3) .target-icon {
				background-color: rgba(23, 228, 255, 0.1);
				&::after {
					background-color: rgba(23, 228, 255, 0.22);
					border-color: #17e4ff;
				}
			}

			&:nth-of-type(4) .target-icon {
				background-color: rgba(0, 186, 250, 0.1);
				&::after {
					background-color: rgba(0, 186, 250, 0.22);
					border-color: #0093ff;
				}
			}

			.target-icon {
				width: 44px;
				height: 44px;
				display: flex;
				align-items: center;
				justify-content: center;
				position: relative;
				border-radius: 50%;

				&::after {
					content: "";
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
					width: 32px;
					height: 32px;
					border: 2px solid;
					border-radius: 50%;
				}

				svg {
					width: 20px;
					height: 20px;
				}
			}

			.target-content {
				margin-left: 8px;
				display: flex;
				flex-direction: column;
				align-items: start;
				justify-content: center;
				height: 100%;

				.target-label {
					font-family: Alibaba PuHuiTi;
					font-weight: normal;
					font-size: 10px;
					color: #ffffff;
					white-space: nowrap; // 防止文本换行
				}

				.target-value {
					font-family: "D-DIN-PRO";
					font-weight: bold;
					font-size: 20px;
					color: #ffffff;
					white-space: nowrap; // 防止数值换行
				}
			}
		}
	}

	.incident-table {
		flex: 1;
		overflow: hidden;
		display: flex;
		flex-direction: column;

		.scroll-container {
			height: 1px;
			flex: 1;
		}

		.table-header {
			height: 32px;
			background-image: linear-gradient(
				180deg,
				rgba(0, 138, 255, 0) 47%,
				rgba(0, 138, 255, 0.1) 100%
			);
			box-sizing: border-box;
			border: 1px solid;
			border-image: linear-gradient(
					90deg,
					rgba(0, 138, 255, 0),
					rgba(0, 138, 255, 1),
					rgba(0, 138, 255, 0.2),
					rgba(0, 138, 255, 0)
				)
				1 1;
			padding: 0 12px;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.table-title {
				font-family: Alibaba PuHuiTi;
				font-size: 16px;
				color: #ffffff;
			}

			.table-tab-list {
				display: flex;
				column-gap: 4px;

				&-item {
					width: 44px;
					height: 20px;
					display: flex;
					align-items: center;
					justify-content: center;
					font-family: Alibaba PuHuiTi;
					font-size: 12px;
					color: #ffffff;
					border-top: 1px solid transparent;
					cursor: pointer;

					&.is-active {
						background: linear-gradient(180deg, rgba(0, 138, 255, 0) 0%, #008aff 100%);
						border-color: #008aff;
					}
				}
			}
		}

		.table-body {
			.table-row {
				height: 28px;
				display: flex;
				align-items: center;
				padding: 0 8px;
				box-sizing: border-box;
				border: 1px solid transparent;
				position: relative;

				&.deal {
					// &::after {
					// 	content: "";
					// 	position: absolute;
					// 	bottom: -1px;
					// 	left: 8px;
					// 	width: calc(100% - 16px);
					// 	height: 0px;
					// 	border-bottom: 1px solid rgba(0, 138, 255, 0.4);
					// }

					.dealStatus {
						background: linear-gradient(180deg, #008aff 0%, #0064b8 100%);
					}
				}

				&.unDeal {
					&:hover {
						background: rgba(220, 94, 98, 0.3);
						border-color: #cb335c;
					}

					// & + .unDeal {
					// 	border-top: none;
					// }

					.dealStatus {
						background: linear-gradient(180deg, #ed5b80 0%, #cb335c 100%);
					}
				}

				.table-col {
					margin-left: 4px;
					font-family: Alibaba PuHuiTi;
					font-size: 12px;
					color: #ffffff;

					&.icon {
						svg {
							width: 16px;
							height: 16px;
						}
					}

					&.type {
						width: 70px;
					}

					&.area {
						flex: 1;
						text-align: left;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}

					&.time {
						width: 128px;
						text-align: left;
					}

					&.dealStatus {
						width: 54px;
						height: 20px;
						display: flex;
						align-items: center;
						justify-content: center;
						border-radius: 4px;
					}
				}
			}
		}
	}
}
</style>

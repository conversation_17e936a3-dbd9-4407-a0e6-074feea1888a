<template>
  <div class="alert-marker">
    <!-- 顶部描述信息 -->
    <div class="alert-header">
      <span class="alert-icon">
        <img :src="HeaderIcon" />
      </span>
      <span class="alert-header-label">{{ formData?.title || '预警信息' }}</span>
    </div>

    <div class="alert-content">
      <div class="alert-info">
        <div class="alert-camera">{{ formData?.cameraName || '未知摄像头' }}</div>
        <div class="alert-department">{{ formData?.department || '未知分部' }}</div>
        <div class="alert-time">{{ formData?.timestamp || '未知时间' }}</div>
      </div>
      <!-- 下部分图片 -->
      <div class="alert-image">
        <a-image width="100%" :src="formData?.cameraImage" />
        <!-- <img  alt="监控图片" /> -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import HeaderIcon from '@/assets/modules/common/icons/yujing-card-icon.svg';

// 定义组件名称
defineOptions({
  name: 'AlertMarker',
});

// 定义props
const props = defineProps({
  title: {
    type: String,
    default: '预警信息',
  },
  properties: {
    type: Object,
    default: () => ({}),
  },
});

// 计算属性
const formData = computed(() => {
  try {
    // 检查是否为字符串，如果是则解析为对象
    if (props.properties.formData && typeof props.properties.formData === 'string') {
      return JSON.parse(props.properties.formData);
    }
    // 如果已经是对象，则直接返回
    return props.properties.formData || {};
  } catch (error) {
    console.error('解析 formData 失败:', error);
    return {};
  }
});
</script>

<style scoped lang="scss">
.alert-marker {
  display: flex;
  flex-direction: column;
  width: 516px;
  height: 384px;
  background: rgba(17, 20, 43, 0.74);
  backdrop-filter: blur(8px);
  box-shadow: 0px 6px 16px 0px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  border: 1px solid #d93526;
  box-sizing: border-box;
}

.alert-header {
  position: relative; /* 添加相对定位 */
  display: flex;
  align-items: center;
  padding: 0 12px;
  height: 32px;
  background: rgba(17, 20, 43, 0.74);
  box-shadow: 0px 5px 12px 0px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid #d93526;
  &::before {
    content: '';
    position: absolute;
    left: 4px;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 100%;
    background: #d93526;
  }
  &-label {
    font-size: 18px;
    font-family: YouSheBiaoTiHei;
    color: #fff;
  }
}

.alert-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 6px;
  img {
    width: 100%;
    height: 100%;
  }
}

.alert-content {
  flex: 1;
  overflow: hidden;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  .alert-info {
    display: flex;
    font-size: 18px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    gap: 12px;
    color: #ffffff;
  }

  .alert-camera,
  .alert-department,
  .alert-time {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .alert-time {
    text-align: right;
    flex: 1;
  }

  .alert-image {
    flex: 1;
    display: flex;
    align-items: center;
    width: 100%;
    // height: 200px;
    overflow: hidden;
  }
}
</style>

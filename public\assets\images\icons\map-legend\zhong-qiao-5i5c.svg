<svg width="23" height="22" viewBox="0 0 23 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#228;&#184;&#173;&#230;&#161;&#165;">
<g id="Ellipse 15" filter="url(#filter0_d_342_3811)">
<circle cx="11.5223" cy="10.9248" r="8" fill="url(#paint0_radial_342_3811)" fill-opacity="0.7"/>
<circle cx="11.5223" cy="10.9248" r="7.55492" stroke="url(#paint1_linear_342_3811)" stroke-width="0.890162"/>
</g>
<circle id="Ellipse 16" cx="11.522" cy="10.9256" r="7.01097" fill="url(#paint2_radial_342_3811)" stroke="url(#paint3_linear_342_3811)" stroke-width="0.200286"/>
<g id="Group 1321315401" filter="url(#filter1_d_342_3811)">
<g id="Frame" clip-path="url(#clip0_342_3811)">
<path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M16.2938 8.44167H15.8661V9.40303H16.0142C16.2948 9.40303 16.522 9.63226 16.522 9.91507V14.1776C16.522 14.3683 16.3687 14.523 16.1796 14.523H15.4329C15.2931 14.523 15.1782 14.4137 15.168 14.273C15.0325 12.3922 13.4521 10.9819 11.522 10.9819C9.592 10.9819 8.01141 12.3922 7.87593 14.273C7.86579 14.4137 7.75084 14.523 7.61107 14.523H6.86433C6.67527 14.523 6.52197 14.3684 6.52197 14.1776V9.91507C6.52197 9.63226 6.74926 9.40303 7.02946 9.40303H7.17776V8.44167H6.75048C6.62605 8.44167 6.52528 8.34077 6.52528 8.21647V7.65099C6.52528 7.52655 6.62618 7.42578 6.75048 7.42578H16.2938C16.4183 7.42578 16.519 7.52668 16.519 7.65099V8.21647C16.519 8.3409 16.4181 8.44167 16.2938 8.44167ZM7.61789 9.40303H9.23983V8.44167H7.61789V9.40303ZM9.67997 9.40303H11.3018V8.44167H9.67997V9.40303ZM11.7419 9.40303H13.3639V8.44167H11.7419V9.40303ZM13.804 9.40303H15.4259V8.44167H13.804V9.40303Z" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_342_3811" x="0.851853" y="0.254319" width="21.341" height="21.341" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.33524"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.388235 0 0 0 0 0.640523 0 0 0 0 0.807843 0 0 0 0.51 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_342_3811"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3811" result="shape"/>
</filter>
<filter id="filter1_d_342_3811" x="6.52197" y="5.9248" width="10" height="10.4451" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.445081"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.150327 0 0 0 0 0.247059 0 0 0 0 0.145098 0 0 0 0.75 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_342_3811"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3811" result="shape"/>
</filter>
<radialGradient id="paint0_radial_342_3811" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(11.5223 10.9248) scale(8)">
<stop stop-color="#D3D3D3"/>
<stop offset="0.03" stop-color="#D3D3D3" stop-opacity="0.9216"/>
<stop offset="0.13" stop-color="#D3D3D3" stop-opacity="0.7056"/>
<stop offset="0.23" stop-color="#D3D3D3" stop-opacity="0.5329"/>
<stop offset="0.35" stop-color="#D3D3D3" stop-opacity="0.4225"/>
<stop offset="0.47" stop-color="#D3D3D3" stop-opacity="0.3364"/>
<stop offset="0.6" stop-color="#D3D3D3" stop-opacity="0.2916"/>
<stop offset="0.76" stop-color="#D3D3D3" stop-opacity="0.2601"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.5"/>
</radialGradient>
<linearGradient id="paint1_linear_342_3811" x1="11.5223" y1="18.9248" x2="11.5223" y2="2.9248" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<radialGradient id="paint2_radial_342_3811" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(11.522 10.9256) scale(7.11111)">
<stop stop-color="#D3D3D3"/>
<stop offset="1" stop-color="#0B103B" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint3_linear_342_3811" x1="11.522" y1="18.0367" x2="11.522" y2="3.81445" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<clipPath id="clip0_342_3811">
<rect width="10" height="10" fill="white" transform="translate(6.52197 5.9248)"/>
</clipPath>
</defs>
</svg>

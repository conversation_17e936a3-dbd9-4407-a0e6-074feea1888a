<template>
  <div class="news-notification" ref="rootRef">
    <div class="weather-icon" @click="togglePanel">
      <img :src="News" alt="消息" />
      <!-- <span class="dot" v-if="hasUnread"></span> -->
      <div class="panel" v-if="showPanel">
        <div class="panel-title">消息通知（99+）</div>
        <div class="message-list">
          <div class="message-item" v-for="msg in messages" :key="msg.id" @click.stop="showMsgDetail(msg)">
            <div class="msg-title" :class="{ 'msg-title-read': msg.read }">
              {{ msg.title }}
              <span class="dot" v-if="!msg.read"></span>
            </div>
            <div class="msg-time">{{ msg.time }}</div>
          </div>
        </div>
        <div class="clear-btn" @click="clearAll">全部清空</div>
      </div>
    </div>

    <NewsDetail v-if="showDetail && selectedMsg" :msg="selectedMsg" @close="closeDetail"/>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed } from "vue";
import News from "@/assets/images/people/News.svg";
import NewsDetail from './NewsDetail.vue';

const showPanel = ref(false);
const rootRef = ref(null);
const selectedMsg = ref(null);
const showDetail = ref(false);
const messages = ref([
  { id: 1, title: "气象预警", content: "青海省自然资源厅，青海省气象局2025年07月03日08时35分联合更新发布地质灾害气象风险蓝色预警，注意道路施工及保通情况！", time: "今天 10:00:00", type: 'blue', read: false },
  { id: 2, title: "气象预警", content: "青海省自然资源厅，青海省气象局2025年07月03日08时35分联合更新发布地质灾害气象风险黄色预警，注意道路施工及保通情况！", time: "今天 10:00:00", type: 'yellow', read: false },
  { id: 3, title: "气象预警", content: "青海省自然资源厅，青海省气象局2025年07月03日08时35分联合更新发布地质灾害气象风险橙色预警，注意道路施工及保通情况！", time: "今天 10:00:00", type: 'orange', read: false },
  { id: 4, title: "气象预警", content: "青海省自然资源厅，青海省气象局2025年07月03日08时35分联合更新发布地质灾害气象风险红色预警，注意道路施工及保通情况！", time: "今天 10:00:00", type: 'red', read: false },
]);

const hasUnread = computed(() => messages.value.some(msg => !msg.read));

function togglePanel() {
  showPanel.value = !showPanel.value;
  if(!showPanel.value){
    showMsgDetail()
  }
}

function clearAll() {
  // messages.value = [];
}

function handleClickOutside(event) {
  // if (rootRef.value && !rootRef.value.contains(event.target)) {
  //   showPanel.value = false;
  // }
}

function showMsgDetail(msg) {
  console.log("showMsgDetail");
  selectedMsg.value = msg;
  showDetail.value = true;
}

function closeDetail() {
  showDetail.value = false;
  selectedMsg.value = null;
}

onMounted(() => {
  // document.addEventListener('mousedown', handleClickOutside);
});
onBeforeUnmount(() => {
  // document.removeEventListener('mousedown', handleClickOutside);
});
</script>

<style scoped lang="scss">
.news-notification {
  position: relative;
  display: flex;
  align-items: center;
  height: 28px;
  margin-right: 26px;
  border-right: 1px solid #9BB3D1;
  
  .weather-icon {
    width: 16px;
    height: 16px;
    margin-right: 26px;
	pointer-events: all;
    cursor: pointer;
    // z-index: 11;
    position: relative;
    img {
      width: 100%;
      height: 100%;
    }
    // .dot {
    //   position: absolute;
    //   top: -2px;
    //   right: -2px;
    //   width: 8px;
    //   height: 8px;
    //   background: #FF0000;
    //   border-radius: 50%;
    //   border: 2px solid #1a2636;
    // //   z-index: 101;
    // }
  }
  .panel {
    position: absolute;
    left: 50%;
    top: 32px;
    transform: translateX(-50%);
    width: 150px;
    max-width: 150px;
    background: rgba(9,16,27,0.9);
    border-radius: 8px;
    color: #fff;
    padding: 12px 12px 0 12px;
    z-index: 21;
	  box-shadow: 4px 8px 14px 0px rgba(0,0,0,0.05);
    border: 1px solid #242E39;
    max-height: 320px; // 可选，限制整体弹窗高度
    display: flex;
    flex-direction: column;
    &::before {
      content: '';
      position: absolute;
      top: -10px; // 三角高度
      left: 50%;
      transform: translateX(-50%);
      border-width: 0 8px 10px 8px; // 宽度和高度
      border-style: solid;
      border-color: transparent transparent #242E39 transparent; // 下方为panel的边框色
      width: 0;
      height: 0;
      z-index: 1;
    }
    .panel-title {
      font-size: 12px;
      // border-bottom: 1px solid #22304a;
      // margin-bottom: 8px;
      font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
      font-weight: normal;
      color: #FFFFFF;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    .message-list {
      height: 110px; // 用 height 替换 max-height
      overflow-y: auto;
	    padding:8px 8px 8px 0;
      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 37px;
        background: transparent;
      }
      &::-webkit-scrollbar-thumb {
        background: #008AFF;
        border-radius: 4px;
      }
      &::-webkit-scrollbar-track {
        background: rgba(0,18,33,0.2);
      }
      .message-item {
        pointer-events: all;
        cursor: pointer;
		    margin-top: 8px;
        // border-bottom: 1px solid #22304a;
        .msg-title {
          font-size: 12px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          color: #D6E4FF;
          .dot {
            margin-left: 8px;
            width: 5px;
            height: 5px;
            background: #FF0000;
            border-radius: 50%;
            display: inline-block;
          }
        }
		.msg-time {
          font-size: 10px;
          color: #7699B9;
          margin-top: 4px;
        }
		.msg-title-read { 
			color: #D6E4FF !important;
}
        .msg-time-read {
			color: #7699B9 !important;
        }
      }
    }
    .clear-btn {
      text-align: center;
	  color: #17A9FF;
      font-size: 12px;
      padding: 8px 0;
      pointer-events: all;
    cursor: pointer;
      border-top: 1px solid #22304a;
      transition: color 0.2s;
      &:hover {
        color: #50cfff;
      }
    }
  }
}
</style>

export class RouteLayer {
	constructor(map) {
		this.map = map;
		this.baseSourceId = "route-base-source";
		this.baseLayerId = "route-base-layer";
		this.ZOrder = 2;
		this.IdMap = new Map();
	}

	init() {
		try {
			const fileList = [
				"/config/engine/map/json/route/route_driving_EPSG4490_1751363041595.json",
				"/config/engine/map/json/route/route_driving_EPSG4490_1751363504287.json",
				"/config/engine/map/json/route/route_driving_EPSG4490_1751277625127.json",
				"/config/engine/map/json/route/route_driving_EPSG4490_1752660230640.json",
			];
			fileList.forEach(async (file, index) => {
				const response = await fetch(file);
				const geojson = await response.json();

				const sourceId = this.baseSourceId + "-" + index;
				const layerId = this.baseLayerId + "-" + index;

				this.map.addSource(sourceId, {
					type: "geojson",
					data: geojson,
					lineMetrics: true,
				});

				this.map.addLayer({
					id: layerId,
					type: "line",
					source: sourceId,
					paint: {
						"line-color": "#004190",
						"line-width": 4,
					},
					layout: {
						"line-cap": "round",
						"line-join": "round",
					},
				});

				this.IdMap.set(layerId, { sourceId, layerId });

				this.map.addLayer({
					id: layerId + "test",
					type: "line",
					source: sourceId,
					paint: {
						"line-color": "#000",
						"line-width": 8,
						"line-opacity": 0.5,
						"line-translate": [8, 8],
					},
					layout: {
						"line-cap": "round",
						"line-join": "round",
					},
				});
			});
		} catch (error) {
			console.log(error);
		}
	}
}

<svg width="1662" height="124" viewBox="0 0 1662 124" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#229;&#159;&#186;&#230;&#156;&#172;&#228;&#187;&#139;&#231;&#187;&#141;">
<g id="Group 297">
<foreignObject x="-4.42697" y="-4.42697" width="1670.85" height="132.854"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2.21px);clip-path:url(#bgblur_0_1534_10113_clip_path);height:100%;width:100%"></div></foreignObject><path id="Rectangle 154" data-figma-bg-blur-radius="4.42697" d="M1661.5 0.5V123.5H6.20703L0.5 117.793V0.5H1661.5Z" fill="#001624" fill-opacity="0.5" stroke="url(#paint0_linear_1534_10113)"/>
<path id="Vector 25" d="M1 21V1H21" stroke="#19CDAD" stroke-width="2"/>
<path id="Vector 27" d="M0 124V120L4 124H0Z" fill="#008AFF"/>
<g id="Rectangle 34625222">
<g filter="url(#filter1_i_1534_10113)">
<rect x="1574" width="88" height="124" fill="#008AFF" fill-opacity="0.3"/>
<rect x="1574" width="88" height="124" fill="url(#paint1_linear_1534_10113)" fill-opacity="0.3"/>
<rect x="1574" width="88" height="124" fill="url(#paint2_radial_1534_10113)" fill-opacity="0.6"/>
</g>
<rect x="1574.5" y="0.5" width="87" height="123" stroke="url(#paint3_linear_1534_10113)"/>
</g>
<path id="Vector 26" d="M1661 103L1661 123L1641 123" stroke="#19CDAD" stroke-width="2"/>
</g>
</g>
<defs>
<clipPath id="bgblur_0_1534_10113_clip_path" transform="translate(4.42697 4.42697)"><path d="M1661.5 0.5V123.5H6.20703L0.5 117.793V0.5H1661.5Z"/>
</clipPath><filter id="filter1_i_1534_10113" x="1574" y="0" width="88" height="124" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1" operator="erode" in="SourceAlpha" result="effect1_innerShadow_1534_10113"/>
<feOffset/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.541176 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1534_10113"/>
</filter>
<linearGradient id="paint0_linear_1534_10113" x1="423.555" y1="-104" x2="423.555" y2="132" gradientUnits="userSpaceOnUse">
<stop stop-color="#50BAFB"/>
<stop offset="0.515" stop-color="#0E76AE"/>
<stop offset="1" stop-color="#4CBCF3"/>
</linearGradient>
<linearGradient id="paint1_linear_1534_10113" x1="1618" y1="0" x2="1618" y2="124" gradientUnits="userSpaceOnUse">
<stop offset="0.46556" stop-color="#008AFF" stop-opacity="0"/>
<stop offset="1" stop-color="#008AFF"/>
</linearGradient>
<radialGradient id="paint2_radial_1534_10113" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(1618 124) rotate(-90) scale(49.9444 35.4444)">
<stop stop-color="#64C6FF"/>
<stop offset="1" stop-color="#083E73" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint3_linear_1534_10113" x1="1574" y1="61.9991" x2="1662" y2="61.9991" gradientUnits="userSpaceOnUse">
<stop stop-color="#008AFF" stop-opacity="0"/>
<stop offset="0.097669" stop-color="#008AFF"/>
<stop offset="0.897457" stop-color="#008AFF" stop-opacity="0.2"/>
<stop offset="1" stop-color="#008AFF" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>

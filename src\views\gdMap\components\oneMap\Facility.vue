<template>
  <TrafficCard title="设备管理">
    <div class="real-time-flow">
      <FacilityChart :data="data" />
    </div>
    <template #extra>
      <div class="link" @click="onInfo">查看更多 <icon-double-right /></div>
    </template>
  </TrafficCard>
</template>

<script setup>
import request from "@/utils/request";
import FacilityChart from "./components/FacilityChart.vue";
import emitter from "@/utils/emitter";

const data = ref([]);
const currentDepartmentId = ref(null);

onMounted(() => {
  // 监听区域双击事件
  emitter.$on("division-area-dblclick", handleAreaChange);
  getData();
});

onBeforeUnmount(() => {
  emitter.$off("division-area-dblclick", handleAreaChange);
});

const handleAreaChange = (eventData) => {
  console.log("设备管理 - 接收到区域双击事件:", eventData);
  currentDepartmentId.value = eventData.properties.id;
  getData();
};

const getData = () => {
  const params = currentDepartmentId.value ? { departmentId: currentDepartmentId.value } : {};
  request.get("/api/screen/board/right/device/stat", params).then((res) => {
    if (res.code === 200) {
      data.value = res.data.map(({ deviceType, deviceNumber, devicePercent }) => ({
        name: deviceType,
        value: deviceNumber,
        percent: devicePercent,
      }));
    }
  });
};

function onInfo() {
  // console.log('onInfo')
}
</script>

<style lang="scss" scoped>
.real-time-flow {
  height: 168px;
}
</style>

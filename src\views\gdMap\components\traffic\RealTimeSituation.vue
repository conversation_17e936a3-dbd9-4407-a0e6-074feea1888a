<template>
  <TrafficCard title="实时工区情况">
    <div class="real-time-situation">
      <a-scrollbar outer-style="height:100%" style="height: 100%; overflow: auto" type="track">
        <div class="chart">
          <RealTimeSituationChart :leftData="leftData" :rightData="rightData" />
        </div>
      </a-scrollbar>
    </div>
  </TrafficCard>
</template>

<script setup>
import request from "@/utils/request";
import RealTimeSituationChart from "./components/RealTimeSituationChart.vue";
import emitter from "@/utils/emitter";

const directionList = [1, 2];
const leftData = ref([]);
const rightData = ref([]);
const currentDepartmentId = ref(null);

onMounted(() => {
  // 监听区域双击事件
  emitter.$on("division-area-dblclick", handleAreaChange);
  getData();
});

onBeforeUnmount(() => {
  emitter.$off("division-area-dblclick", handleAreaChange);
});

const handleAreaChange = (eventData) => {
  console.log("实时工区情况 - 接收到区域双击事件:", eventData);
  currentDepartmentId.value = eventData.properties.id;
  getData();
};

const getData = () => {
  const baseParams = currentDepartmentId.value ? { departmentId: currentDepartmentId.value } : {};
  directionList.forEach((direction) => {
    const params = { ...baseParams, direction };
    request.get("/api/screen/baotong/traffic/realtime", params).then((res) => {
      if (res.code == 200) {
        const data = (res.data || []).map(({ name, strNum }) => ({
          label: name,
          value: strNum,
        }));
        if (direction === 1) {
          leftData.value = data;
        } else if (direction === 2) {
          rightData.value = data;
        }
      }
    });
  });
};
</script>

<style lang="scss" scoped>
.real-time-situation {
  height: 258px;
}

.chart {
  height: 720px;
}

:deep(.arco-scrollbar) {
  .arco-scrollbar-track {
    width: 6px;
    background-color: #0f304e;
    border: none;

    .arco-scrollbar-thumb {
      margin: 0;
      .arco-scrollbar-thumb-bar {
        width: 6px;
        margin: 0;
        background-color: #008aff;
        border-radius: 4px;
      }
    }
  }
}
</style>

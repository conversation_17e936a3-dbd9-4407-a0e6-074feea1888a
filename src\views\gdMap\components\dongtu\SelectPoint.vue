<template>
  <a-dropdown trigger="click">
    <!-- 冻土切换监测点 -->
    <div class="select-point">
      <img class="ahead-img" src="@/assets/images/dongtu/select-point.png" alt="" />
      <span class="select-point-text">{{ selectedPonit }}</span>
      <span class="arrow-down">
        <icon-caret-down />
      </span>
    </div>
    <template #content>
      <a-doption v-for="point in pointOptions" :key="point" @click="selectedPonit = point">
        {{ point }}
      </a-doption>
    </template>
  </a-dropdown>
</template>

<script setup>
import { ref } from 'vue';
import { IconCaretDown } from '@arco-design/web-vue/es/icon';

const pointOptions = ['路基路面项目', '桥梁项目', '隧道项目', '边坡项目'];
const selectedPonit = ref('路基路面项目');
</script>

<style lang="scss">
.select-point {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: absolute;
  top: 0;
  right: 36px;
  cursor: pointer;
  pointer-events: all;
  z-index: 10;
  .ahead-img {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }
  &-text {
    font-size: 18px;
    color: #ffffff;
    line-height: 40px;
  }
  .arrow-down {
    color: #ffffff;
    width: 16px;
    height: 16px;
    margin-left: 8px;
  }
}
</style>

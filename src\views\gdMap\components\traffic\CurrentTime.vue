<template>
	<TrafficCard title="当前时间">
		<div class="current-time">
			<div class="hms">
				<div class="number">
					<span class="gradient">{{ hours[0] }}</span>
				</div>
				<div class="number">
					<span class="gradient">{{ hours[1] }}</span>
				</div>
				<div class="split">:</div>
				<div class="number">
					<span class="gradient">{{ minutes[0] }}</span>
				</div>
				<div class="number">
					<span class="gradient">{{ minutes[1] }}</span>
				</div>
				<div class="split">:</div>
				<div class="number">
					<span class="gradient">{{ seconds[0] }}</span>
				</div>
				<div class="number">
					<span class="gradient">{{ seconds[1] }}</span>
				</div>
			</div>
			<div class="date">
				<div class="ymd">{{ dateStr }}</div>
				<div class="divider"></div>
				<div class="week">{{ weekStr }}</div>
			</div>
		</div>
	</TrafficCard>
</template>

<script setup>
const dateStr = ref("");
const hours = ref("");
const minutes = ref("");
const seconds = ref("");
let timer = null;
const weekdays = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
const weekStr = ref("");

// 更新时间函数
const updateTime = () => {
	const now = new Date();
	// 格式化日期：YYYY-MM-DD
	const year = now.getFullYear();
	const month = String(now.getMonth() + 1).padStart(2, "0");
	const day = String(now.getDate()).padStart(2, "0");
	dateStr.value = `${year}-${month}-${day}`;
	weekStr.value = weekdays[now.getDay()];
	// 格式化时间：HH:MM:SS
	hours.value = String(now.getHours()).padStart(2, "0");
	minutes.value = String(now.getMinutes()).padStart(2, "0");
	seconds.value = String(now.getSeconds()).padStart(2, "0");
};

onMounted(() => {
	// 立即更新一次时间
	updateTime();
	// 设置定时器，每秒更新一次
	timer = setInterval(updateTime, 1000);
});

onBeforeUnmount(() => {
	// 组件销毁前清除定时器
	if (timer) {
		clearInterval(timer);
		timer = null;
	}
});
</script>

<style lang="scss" scoped>
.current-time {
	color: #fff;
	display: flex;
	align-items: center;
	flex-direction: column;
	height: 102px;
	.hms {
		display: flex;
		align-items: center;
		padding: 12px 0;
		column-gap: 12px;
	}
	.number {
		width: 36px;
		height: 52px;
		background-image: linear-gradient(
			180deg,
			rgba(0, 138, 255, 0) 47%,
			rgba(0, 138, 255, 0.2) 100%
		);
		background-color: rgba(0, 138, 255, 0.1);
		border-radius: 4px 4px 4px 4px;
		border: 1px solid;
		border-image: linear-gradient(
				90deg,
				rgba(0, 138, 255, 0),
				rgba(0, 138, 255, 0.2),
				rgba(0, 138, 255, 1),
				rgba(0, 138, 255, 0.2),
				rgba(0, 138, 255, 0)
			)
			1 1;
		display: flex;
		align-items: center;
		justify-content: center;
		.gradient {
			font-family: D-DIN-PRO, D-DIN-PRO;
			font-weight: bold;
			font-size: 36px;
			background: linear-gradient(90deg, #ffffff 0%, #008aff 100%);
			background-clip: text;
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}
	}
	.split {
		height: 100%;
		font-family: D-DIN-PRO;
		font-weight: bold;
		font-size: 36px;
		background: linear-gradient(180deg, #ffffff 0%, #008aff 100%);
		background-clip: text;
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		display: flex;
		align-items: center;
	}
	.date {
		display: flex;
		align-items: center;
		background: linear-gradient(180deg, #ffffff 0%, #008aff 100%);
		background-clip: text;
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
	}
	.week {
		font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
		font-size: 16px;
	}
	.ymd {
		font-family: D-DIN-PRO;
		font-weight: 500;
		font-size: 16px;
	}
	.divider {
		width: 0px;
		height: 14px;
		margin: 0 12px;
		border: 1px solid;
		border-image: linear-gradient(
				0deg,
				rgba(0, 138, 255, 0),
				rgba(0, 138, 255, 1),
				rgba(0, 138, 255, 0)
			)
			1 1;
	}
}
</style>

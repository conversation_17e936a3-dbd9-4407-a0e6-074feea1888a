import { BaseMarker } from "../BaseMarker";
import maplibregl from "maplibre-gl";
import { createApp } from "vue";
import ProjectMarker from "../../../components/ProjectMarker.vue";
import mapConfig from "@/config/engine/maplibre/map.config.js";

// 图层缩放级别常量
const LAYER_ZOOM_LEVELS = {
	ICON: {
		MIN: 4,
		MAX: mapConfig.maxzoom,
		SIZE: {
			MIN_ZOOM: 5,
			MIN_SIZE: 1.2,
			MAX_ZOOM: 9,
			MAX_SIZE: 1.5,
		},
		OPACITY: {
			MIN_ZOOM: 5,
			MIN_OPACITY: 0.8,
			MAX_ZOOM: mapConfig.maxzoom,
			MAX_OPACITY: 1,
		},
	},
	BOARD: {
		MIN: 4,
		MAX: mapConfig.maxzoom,
	},
};

/**
 * 区域标记渲染器
 */
export class AreaMarker extends BaseMarker {
	/**
	 * 渲染区域标记
	 * @param {Object} map - MapLibre地图实例
	 * @param {String} sourceId - 数据源ID
	 * @param {Object} options - 渲染选项
	 * @returns {Object} 渲染结果
	 */
	static render(map, sourceId, options = {}) {
		const { data } = options;

		// 如果已存在源，先移除
		this.remove(map, sourceId);

		// 处理数据，分离图标标记和立牌标记
		const { iconMarkers, boardMarkers } = this.processMarkerData(data);

		// 添加数据源
		map.addSource(sourceId, {
			type: "geojson",
			data: iconMarkers,
		});

		// 添加图层
		const layerId = `${sourceId}-layer`;

		// 添加底座图层
		map.addLayer({
			id: layerId,
			type: "symbol",
			source: sourceId,
			filter: ["!", ["has", "point_count"]], // 只显示非聚合点
			layout: {
				// "icon-image": ["get", "icon", ["get", "style"]],
				"icon-allow-overlap": true,
				"icon-ignore-placement": true,
				"icon-size": [
					"interpolate",
					["linear"],
					["zoom"],
					LAYER_ZOOM_LEVELS.ICON.SIZE.MIN_ZOOM,
					LAYER_ZOOM_LEVELS.ICON.SIZE.MIN_SIZE,
					LAYER_ZOOM_LEVELS.ICON.SIZE.MAX_ZOOM,
					LAYER_ZOOM_LEVELS.ICON.SIZE.MAX_SIZE,
				],
				"icon-offset": ["get", "offset", ["get", "style"]],
			},
			paint: {
				"icon-opacity": [
					"interpolate",
					["linear"],
					["zoom"],
					LAYER_ZOOM_LEVELS.ICON.OPACITY.MIN_ZOOM,
					LAYER_ZOOM_LEVELS.ICON.OPACITY.MIN_OPACITY,
					LAYER_ZOOM_LEVELS.ICON.OPACITY.MAX_ZOOM,
					LAYER_ZOOM_LEVELS.ICON.OPACITY.MAX_OPACITY,
				],
			},
			minzoom: LAYER_ZOOM_LEVELS.ICON.MIN,
			maxzoom: LAYER_ZOOM_LEVELS.ICON.MAX,
		});

		// 添加点击事件
		this.setupClickHandler(map, layerId, options);

		// 直接渲染立牌标记
		this.renderBoardMarkers(map, boardMarkers, options);

		// 添加地图缩放事件监听，动态更新图层可见性
		this.setupZoomHandler(map, layerId, sourceId, options);

		return {
			layerId,
			sourceId,
		};
	}

	/**
	 * 处理标记数据，分离图标标记和立牌标记
	 * @param {Object} data - GeoJSON数据
	 * @returns {Object} 分离后的图标标记和立牌标记
	 */
	static processMarkerData(data) {
		if (!data || data.type !== "FeatureCollection" || !Array.isArray(data.features)) {
			return { iconMarkers: data, boardMarkers: [] };
		}

		const iconMarkers = {
			type: "FeatureCollection",
			features: [],
		};

		const boardMarkers = [];

		// 为每个特征添加样式和位置信息
		data.features.forEach((feature, index) => {
			const id = feature.id || `area-${index}`;
			const coordinates = feature.geometry.coordinates;
			const properties = feature.properties || {};

			// 添加索引信息到properties中，用于决定左右显示
			properties.markerIndex = index;
			properties.markerPosition = index % 2 === 0 ? "left" : "right";

			// 确保style对象存在
			if (!properties.style) {
				properties.style = {};
			}

			// 如果没有指定图标，设置默认图标
			if (!properties.style.icon) {
				properties.style.icon = "command-center-icon";
			}

			// 标记为区域标记
			properties.markerType = "area";

			// 修改判断逻辑，允许同时渲染图标标记和立牌标记
			if (properties.types && properties.types.includes("area_marker")) {
				// 添加到图标标记集合
				iconMarkers.features.push({
					...feature,
					id,
					properties,
				});
			}

			if (properties.types && properties.types.includes("area_board")) {
				// 添加到立牌标记集合
				boardMarkers.push({
					...feature,
					id,
					coordinates,
					label: properties.label,
					properties,
				});
			}
		});

		return { iconMarkers, boardMarkers };
	}

	/**
	 * 渲染立牌标记
	 * @param {Object} map - MapLibre地图实例
	 * @param {Array} boardMarkers - 立牌标记数据
	 * @param {Object} options - 渲染选项
	 */
	static renderBoardMarkers(map, boardMarkers, options = {}) {
		// 清除现有的立牌标记
		this.clearBoardMarkers(map);

		// 渲染新的立牌标记
		boardMarkers.forEach(({ id, coordinates, label, properties }) => {
			// 将全局选项合并到每个标记的属性中
			const mergedProperties = {
				...properties,

				// minZoom: options.minZoom !== undefined ? options.minZoom : properties.minZoom || 5,
				// maxZoom: options.maxZoom !== undefined ? options.maxZoom : properties.maxZoom || 9,
			};

			this.createBoardMarker(map, id, coordinates, label, mergedProperties);
		});
	}

	/**
	 * 设置缩放事件处理
	 * @param {Object} map - MapLibre地图实例
	 * @param {String} layerId - 图层ID
	 * @param {String} sourceId - 数据源ID
	 * @param {Object} options - 选项
	 */
	static setupZoomHandler(map, layerId, sourceId, options = {}) {
		const handlerKey = `_${sourceId}ZoomHandler`;

		// 移除已有的缩放事件
		if (map[handlerKey]) {
			map.off("zoomend", map[handlerKey]);
		}

		// 创建新的缩放事件处理函数
		map[handlerKey] = () => {
			// 根据当前缩放级别更新所有立牌标记的可见性
			this.updateAllBoardMarkersVisibility(map);
		};

		// 添加缩放事件
		map.on("zoomend", map[handlerKey]);

		// 初始化时执行一次
		map[handlerKey]();
	}

	/**
	 * 设置点击事件处理
	 * @param {Object} map - MapLibre地图实例
	 * @param {String} layerId - 图层ID
	 * @param {Object} options - 选项
	 */
	static setupClickHandler(map, layerId, options = {}) {
		// 移除已有的点击事件
		if (map._areaClickHandler) {
			map.off("click", layerId, map._areaClickHandler);
		}

		// 创建新的点击事件处理函数
		map._areaClickHandler = (e) => {
			const features = map.queryRenderedFeatures(e.point, {
				layers: [layerId],
			});

			if (features.length > 0) {
				const feature = features[0];
				const coordinates = feature.geometry.coordinates.slice();
				const properties = feature.properties;
				const id = feature.id || `area-marker-${Date.now()}`;

				// 防止地图缩放时弹出窗口位置偏移
				while (Math.abs(e.lngLat.lng - coordinates[0]) > 190) {
					coordinates[0] += e.lngLat.lng > coordinates[0] ? 360 : -360;
				}

				// 如果需要，可以在这里添加点击事件的处理逻辑
				// 例如高亮显示或者显示详细信息等

				// 阻止事件冒泡
				e.stopPropagation();
			}
		};

		// 添加点击事件
		map.on("click", layerId, map._areaClickHandler);
	}

	/**
	 * 创建立牌标记
	 * @param {Object} map - MapLibre地图实例
	 * @param {String} id - 标记ID
	 * @param {Array} lngLat - 经纬度坐标
	 * @param {String} label - 标记标签
	 * @param {Object} properties - 标记属性
	 * @returns {Object} 标记对象
	 */
	static createBoardMarker(map, id, lngLat, label, properties = {}) {
		const el = document.createElement("div");

		const app = createApp(ProjectMarker, {
			label,
			properties,
			position: properties.markerPosition || "left",
			lineHeight: properties.style.lineHeight,
		});
		app.mount(el);

		// 创建 Marker，修改锚点为bottom-left或bottom-right
		const marker = new maplibregl.Marker({
			element: el,
			anchor: "right",
			// 可以添加偏移量，微调位置
			offset: [0, 0],
		})
			.setLngLat(lngLat)
			.addTo(map);

		// 设置立牌标记的缩放级别范围
		properties.minZoom = LAYER_ZOOM_LEVELS.BOARD.MIN; // 最小缩放级别
		properties.maxZoom = properties.maxZoom || LAYER_ZOOM_LEVELS.BOARD.MAX; // 最大缩放级别

		// 初始化时根据可见性设置显示状态
		this.updateBoardMarkerVisibility(map, marker, properties);

		// 保存标记信息
		if (!map._boardMarkers) {
			map._boardMarkers = new Map();
		}

		map._boardMarkers.set(id, {
			marker,
			app,
			el,
			properties,
		});

		return marker;
	}

	/**
	 * 更新单个立牌标记可见性
	 * @param {Object} map - MapLibre地图实例
	 * @param {Object} marker - 标记对象
	 * @param {Object} properties - 标记属性
	 */
	static updateBoardMarkerVisibility(map, marker, properties = {}) {
		const zoom = map.getZoom();
		const minZoom = LAYER_ZOOM_LEVELS.BOARD.MIN;
		const maxZoom = LAYER_ZOOM_LEVELS.BOARD.MAX;

		const shouldBeVisible = zoom >= minZoom && zoom <= maxZoom;
		marker.getElement().style.display = shouldBeVisible ? "" : "none";
	}

	/**
	 * 更新所有立牌标记可见性
	 * @param {Object} map - MapLibre地图实例
	 */
	static updateAllBoardMarkersVisibility(map) {
		if (map._boardMarkers) {
			map._boardMarkers.forEach(({ marker, properties }) => {
				this.updateBoardMarkerVisibility(map, marker, properties);
			});
		}
	}

	/**
	 * 移除立牌标记
	 * @param {Object} map - MapLibre地图实例
	 * @param {String} id - 标记ID
	 */
	static removeBoardMarker(map, id) {
		if (map._boardMarkers && map._boardMarkers.has(id)) {
			const { marker, app } = map._boardMarkers.get(id);
			marker.remove();
			app.unmount();
			map._boardMarkers.delete(id);
		}
	}

	/**
	 * 清除所有立牌标记
	 * @param {Object} map - MapLibre地图实例
	 */
	static clearBoardMarkers(map) {
		if (map._boardMarkers) {
			map._boardMarkers.forEach(({ marker, app }) => {
				marker.remove();
				app.unmount();
			});
			map._boardMarkers.clear();
		}
	}

	/**
	 * 移除图层和数据源
	 * @param {Object} map - MapLibre地图实例
	 * @param {String} sourceId - 数据源ID
	 */
	static remove(map, sourceId) {
		const layerId = `${sourceId}-layer`;
		const clusterLayerId = `${sourceId}-cluster-layer`;
		const ripple1LayerId = `${layerId}-ripple-1`;
		const ripple2LayerId = `${layerId}-ripple-2`;

		// 停止水波纹动画
		if (map[`_${sourceId}RippleAnimation`]) {
			cancelAnimationFrame(map[`_${sourceId}RippleAnimation`]);
			delete map[`_${sourceId}RippleAnimation`];
		}

		// 移除水波纹图层
		if (map.getLayer(ripple1LayerId)) {
			map.removeLayer(ripple1LayerId);
		}
		if (map.getLayer(ripple2LayerId)) {
			map.removeLayer(ripple2LayerId);
		}

		// 移除图层
		if (map.getLayer(layerId)) {
			map.removeLayer(layerId);
		}

		// 移除聚合图层
		if (map.getLayer(clusterLayerId)) {
			map.removeLayer(clusterLayerId);
		}

		// 移除数据源
		if (map.getSource(sourceId)) {
			map.removeSource(sourceId);
		}

		// 清除立牌标记
		this.clearBoardMarkers(map);

		// 移除点击事件
		if (map._areaClickHandler) {
			map.off("click", layerId, map._areaClickHandler);
		}
	}
}

<template>
  <div class="left-card">
    <m-card title="人员巡查统计" :height="391">
      <div class="person-patrol">
        <ScreenTable :columns="tableColumns">
          <div ref="containerRef" class="patrol-table">
            <div class="table-body">
              <div class="table-row" v-for="(item, index) in patrolData" :key="index">
                <div class="col name" :title="item.name">{{ item.name }}</div>
                <div class="col inspector" :title="item.inspector">{{ item.inspector }}</div>
                <div class="col time">{{ item.time }}</div>
                <div class="col type">
                  <span :class="['dot', setBg(item.type)]"></span>{{ item.type }}
                </div>
              </div>
            </div>
          </div>
        </ScreenTable>
      </div>
    </m-card>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import ScreenTable from '../ScreenTable.vue';
import { useMarquee } from '@/hooks/useMarquee';
import { getPersonPatrol } from '@/views/gdMap/services/dongtu.mock.js';

const patrolData = ref();

const { containerRef, resetScroll } = useMarquee({
  speed: 10, // 滚动速度
  delay: 2000, // 滚动到底部后停顿时间
  step: 1, // 每次滚动的像素
});

// 当数据变化时，重置滚动
watch(
  patrolData,
  () => {
    setTimeout(resetScroll, 300); // 延迟一点时间确保DOM已更新
  },
  { deep: true }
);

onMounted(async () => {
  const defaultData = await getPersonPatrol();
  patrolData.value = defaultData.list;
});

// 表格列配置
const tableColumns = [
  { title: '巡查事项', width: '20%' },
  { title: '巡查人', width: '20%' },
  { title: '巡查时间', width: '45%' },
  { title: '状态', width: '15%' },
];

const setBg = type => {
  if (type === '正常') {
    return 'green';
  } else if (type === '异常') {
    return 'red';
  } else {
    return 'orange';
  }
};
</script>

<style lang="scss">
.person-patrol {
  height: 100%;
  display: flex;
  flex-direction: column;
  .patrol-table {
    height: 100%;
    overflow-y: hidden;
    .table-body {
      .table-row {
        display: flex;
        height: 28px;
        align-items: center;

        // 单行背景色
        &:nth-child(odd) {
          background-color: #091e3f;
        }

        // 双行背景色
        &:nth-child(even) {
          background-color: #142b50;
        }

        &:last-child {
          border-bottom: none;
        }

        .col {
          padding: 0 6px;
          font-size: 12px;
          color: #fff;
          font-family: Source Han Sans CN, Source Han Sans CN;
          box-sizing: border-box;
          &.time {
            width: 45%;
          }
          &.type {
            width: 15%;
          }
          &.name {
            width: 20%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          &.inspector {
            width: 20%;
          }
        }
      }
    }
  }
  .dot {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 1px;
    margin-right: 6px;
    &.green {
      background: #06d239;
    }
    &.red {
      background: #ff4042;
    }
    &.orange {
      background: #ff9e49;
    }
  }
}
</style>

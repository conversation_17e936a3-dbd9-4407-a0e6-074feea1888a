import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import { OUTPUT_DIR } from "./build/constant";
import path from "path";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { ArcoResolver } from "unplugin-vue-components/resolvers";
import svgLoader from "vite-svg-loader";

const TimeStamp = new Date().getTime();

function resolve(url) {
	return path.resolve(__dirname, url);
}
// https://vitejs.dev/config/
export default ({ mode }) => {
	// 获取环境变量
	const env = loadEnv(mode, process.cwd());
	return defineConfig({
		base: "/dp/",
		assetsInclude: [
			"**/*.glb",
			"**/*.gltf",
			"**/*.fbx",
			"**/*.hdr",
			"**/*.json",
			"**/*.mp4",
			"**/*.mov",
		],
		resolve: {
			alias: {
				"@": resolve("./src"),
				"~@": resolve("./src"),
			},
			// 省略文件后缀
			extensions: [".mjs", ".js", ".jsx", ".json"],
		},
		build: {
			minify: "esbuild",
			target: "es2015",
			cssTarget: "chrome80",
			outDir: OUTPUT_DIR,
			terserOptions: {
				compress: {
					// keep_infinity: true,
					// // Used to delete console in production environment
					// drop_console: VITE_DROP_CONSOLE,
					// drop_debugger: true,
				},
			},
			// Turning off brotliSize display can slightly reduce packaging time
			reportCompressedSize: false,
			chunkSizeWarningLimit: 2000,
			rollupOptions: {
				// 参考：https://blog.cinob.cn/archives/393
				output: {
					// 入口文件名
					entryFileNames: `assets/[name]-${TimeStamp}.js`,
					// 块文件名
					chunkFileNames: `assets/[name]-[hash]-${TimeStamp}.js`,
					// 资源文件名 css 图片等等
					assetFileNames: `assets/[name]-[hash]-balabala-${TimeStamp}.[ext]`,
					arco: ["@arco-design/web-vue"],
					chart: ["echarts", "vue-echarts"],
					vue: ["vue", "vue-router", "pinia"],
				},
			},
		},

		plugins: [
			vue(),
			AutoImport({
				imports: ["vue"],
				resolvers: [ArcoResolver()],
			}),
			Components({
				resolvers: [
					ArcoResolver({
						resolveIcons: true,
						sideEffect: true,
					}),
				],
			}),
			svgLoader({
				defaultImport: "url",
				svgoConfig: {
					plugins: [
						{
							name: "preset-default",
							params: {
								overrides: {
									cleanupIds: false,
									removeViewBox: false,
								},
							},
						},
					],
				},
			}),
		],

		server: {
			host: "0.0.0.0", // 允许通过IP访问
			port: 3000, // 可以指定端口，默认是5173
			open: false, // 自动打开浏览器

			proxy: {
				// 本地图形服务代理
				"/geoserver-proxy": {
					target: "http://localhost:50006",
					changeOrigin: true,
					rewrite: (path) => path.replace(/^\/geoserver-proxy/, ""),
				},
				"/tianditu": {
					target: env.VITE_TIANDITU_URL || "https://t0.tianditu.gov.cn",
					changeOrigin: true,
					rewrite: (path) => path.replace(/^\/tianditu/, ""),
				},
				"/api": {
					target: env.VITE_API_BASE_URL || "http://localhost:3000",
					changeOrigin: true,
					// rewrite: (path) => path.replace(/^\/api/, ""),
				},
			},
		},
	});
};

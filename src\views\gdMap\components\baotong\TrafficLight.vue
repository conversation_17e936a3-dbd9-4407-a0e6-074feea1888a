<template>
  <div class="left-card">
    <m-card title="当前红绿灯统计" :height="205">
      <v-chart ref="vChart" :option="option" :autoresize="true" />
    </m-card>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import * as echarts from 'echarts';
import mCard from '@/components/mCard/index.vue';
import VChart from 'vue-echarts';
import { getTrafficLight } from '@/views/gdMap/services/baotong.mock.js';

const option = ref();

onMounted(async () => {
  const defaultData = await getTrafficLight();
  const trafficLight = defaultData;
  setOptions(trafficLight);
});

const setOptions = trafficLight => {
  option.value = {
    legend: {
      show: true,
      left: '5%',
      top: '1%',
      textStyle: {
        color: '#fff',
        fontSize: 12,
      },
      icon: 'rect',
      itemWidth: 8,
      itemHeight: 4,
    },
    title: {
      show: false,
      text: '亿元',
      left: '5%',
      top: '8%',
      textStyle: {
        color: '#D3F8F2',
        fontSize: 8,
      },
    },
    grid: {
      left: '12%',
      top: '25%',
      width: '82%',
      height: '55%',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
        shadowStyle: { opacity: 0.2 },
      },
      backgroundColor: 'rgba(0,0,0,1)',
      borderWidth: 1,
      borderColor: '#999999',
      textStyle: {
        color: '#ffffff',
        fontSize: 10,
      },
    },
    color: ['#6BC7F6'],
    xAxis: [
      {
        type: 'category',
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(217, 231, 255, 0.85)',
          },
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          color: '#ffffff',
          fontSize: 12,
          interval: 0,
          padding: [0, 0, 0, 0],
        },
        data: ['格尔木方向', '那曲方向'],
      },
      {
        axisLine: {
          show: false,
          lineStyle: {
            color: 'rgba(0,0,0,0)',
          },
        },
        data: [],
      },
    ],
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(217, 231, 255, 0.25)',
        },
      },
      axisLabel: {
        color: 'rgba(217, 231, 255, 1)',
        fontSize: 8,
      },
    },
    series: [
      {
        name: '红灯',
        type: 'pictorialBar',
        color: '#FF4042',
        symbol: 'path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z',
        symbolOffset: [-20, 0],
        barWidth: 80,
        label: {
          show: true,
          position: 'top',
          distance: 3,
          color: '#ffffff',
          fontSize: 12,
          offset: [-20, 0],
          fontFamily: 'DDINPRO-Medium',
        },
        data: [
          {
            value: trafficLight.red[0],
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(255, 102, 64, 1)' },
                { offset: 1, color: 'rgba(255, 5, 5, 0)' },
              ]),
            },
          },
          {
            value: trafficLight.red[1],
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(255, 102, 64, 1)' },
                { offset: 1, color: 'rgba(255, 5, 5, 0)' },
              ]),
            },
          },
        ],
      },
      {
        name: '绿灯',
        type: 'pictorialBar',
        color: '#40FF63',
        symbol: 'path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z',
        symbolOffset: [20, 0],
        barWidth: 80,
        label: {
          show: true,
          position: 'top',
          distance: 3,
          color: '#ffffff',
          fontSize: 12,
          offset: [20, 0],
        },
        // markLine: {
        //   symbol: 'none',
        //   label: {
        //     show: false,
        //   },
        //   lineStyle: {
        //     type: 'dashed',
        //     color: '#fff',
        //   },
        //   data: [
        //     [
        //       {
        //         name: '两个坐标之间的标线',
        //         coord: ['格尔木方向', 0],
        //         symbolOffset: [20, 0],
        //       },
        //       {
        //         type: 'max',
        //         symbolOffset: [20, 0],
        //       },
        //     ],
        //   ],
        // },
        data: [
          {
            value: trafficLight.green[0],
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(64, 255, 99, 1)' },
                { offset: 1, color: 'rgba(0, 255, 77, 0)' },
              ]),
            },
          },
          {
            value: trafficLight.green[1],
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(64, 255, 99, 1)' },
                { offset: 1, color: 'rgba(0, 255, 77, 0)' },
              ]),
            },
          },
        ],
      },
    ],
  };
};
</script>
<style lang="scss"></style>

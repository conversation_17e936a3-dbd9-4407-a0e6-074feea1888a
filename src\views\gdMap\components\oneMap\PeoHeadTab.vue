<template>
	<div class="head-tab">
		<div
			v-for="(tab, idx) in tabs"
			:key="tab.label"
			:class="['tab-btn', { active: idx === activeIndex }]"
			@click="selectTab(idx)"
		>
			{{ tab.label }}
		</div>
	</div>
</template>

<script setup>
import emitter from "@/utils/emitter";

const emit = defineEmits(["header-tab-change"]);

const tabs = [{ label: "看板模式" }, { label: "指挥模式" }];

const activeIndex = ref(0);

function selectTab(idx) {
	activeIndex.value = idx;
	emitter.$emit("header-tab-change", idx);
}
</script>

<style lang="scss" scoped>
.head-tab {
	z-index: 1;
	position: absolute;
	height: 44px;
	top: calc(100% * 92 / 1080);
	left: 50%;
	transform: translateX(-50%);
	display: flex;
	align-items: center;
	gap: 2px;
	background: transparent;
	padding: 0 4px;
	background: linear-gradient(
		180deg,
		rgba(0, 18, 33, 0.6) 0%,
		rgba(0, 18, 33, 0.6) 44%,
		#001221 100%
	);
	border-radius: 50px;
	// border: 1px solid;
	border-image: linear-gradient(180deg, rgba(23, 100, 167, 0), rgba(0, 30, 56, 1)) 1 1;
}
.tab-btn {
	min-width: 88px;
	height: 36px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50px;
	font-size: 14px;
	font-family: Alibaba PuHuiTi;
	color: rgba(141, 191, 255, 0.7);

	cursor: pointer;
	transition: all 0.2s;
	box-shadow: none;
	font-weight: normal;
}
.tab-btn.active {
	// font-size: 14px;
	color: #ffffff;
	background: linear-gradient(180deg, rgba(0, 138, 255, 0) 47%, #008aff 100%),
		radial-gradient(0% 87% at -13% 112%, #64c6ff 0%, rgba(8, 62, 115, 0) 100%),
		rgba(0, 138, 255, 0.3);
	box-shadow: inset 0px 0px 8px 1px rgba(0, 138, 255, 0.25);
	border-radius: 30px 30px 30px 30px;
	// border: 1px solid;
	border-image: linear-gradient(
			90deg,
			rgba(0, 138, 255, 0),
			rgba(0, 138, 255, 1),
			rgba(0, 138, 255, 0.2),
			rgba(0, 138, 255, 0)
		)
		1 1;
}
</style>

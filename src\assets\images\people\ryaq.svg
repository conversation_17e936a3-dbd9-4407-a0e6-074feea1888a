<svg width="23" height="22" viewBox="0 0 23 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#228;&#186;&#186;&#229;&#145;&#152;&#229;&#174;&#137;&#229;&#133;&#168;">
<g id="&#228;&#186;&#186;&#229;&#145;&#152;&#229;&#174;&#137;&#229;&#133;&#168;_2">
<g id="Union" filter="url(#filter0_dddi_1579_5027)">
<path d="M7.90877 14.8247C8.12789 14.7555 8.26963 14.8057 8.41672 14.9889C10.0334 17.0109 12.9033 17.022 14.5505 15.0165C14.7199 14.8112 14.8887 14.7614 15.1413 14.8447C16.3247 15.2252 17.5112 15.5974 18.7001 15.9557C19.1721 16.0974 19.4111 16.3951 19.4888 16.8811C19.6333 17.7863 19.8112 18.6887 19.9722 19.5912C20.0972 20.2911 19.9304 20.4966 19.2111 20.4966C16.6445 20.5021 14.075 20.4996 11.5028 20.4996H3.88045C3.00823 20.4996 2.90616 20.3995 3.03949 19.5329C3.18111 18.6055 3.35227 17.6835 3.52442 16.7644C3.59942 16.3644 3.83092 16.0976 4.23648 15.9726C5.46137 15.5976 6.68666 15.2108 7.90877 14.8247Z" fill="url(#paint0_linear_1579_5027)"/>
<path d="M16.0713 9.83113C16.0102 10.6198 15.8926 11.3613 15.3316 11.9611C15.1903 12.111 15.1128 12.3274 15.0324 12.5243C14.5157 13.7827 13.6206 14.6719 12.3929 15.2191C11.6347 15.5578 10.8989 15.4111 10.2045 14.9889C9.17123 14.3611 8.40194 13.511 7.96862 12.3694C7.89362 12.1749 7.77448 11.9829 7.63561 11.8246C7.21074 11.3386 7.11313 10.7391 7.0187 10.1365C7.00206 10.045 7.00428 9.94758 6.99875 9.83113C10.0376 10.095 13.0435 10.0395 16.0713 9.83113Z" fill="url(#paint1_linear_1579_5027)"/>
<path d="M9.9636 1.72998C10.9775 1.44943 11.9895 1.39972 13.0006 1.73305C13.5194 1.90516 13.6187 2.03884 13.5576 2.58015C13.4354 3.63843 13.2889 4.69696 13.1556 5.75523C13.1306 5.94946 13.1219 6.14382 13.108 6.34912C13.2412 6.37965 13.3441 6.40271 13.4717 6.43046C13.9467 5.30823 14.1857 4.13311 14.4385 2.93311C15.544 3.83033 16.2219 4.93605 16.458 6.31383C16.4997 6.54991 16.5804 6.79733 16.7082 7.00286C17.0525 7.55281 16.9978 8.13892 16.9061 8.72775C16.895 8.79993 16.725 8.89151 16.6222 8.90269C15.8833 8.97492 15.1464 9.04642 14.4047 9.08531C11.7438 9.22416 9.08812 9.18066 6.43556 8.91957C6.15804 8.89174 6.0503 8.77975 6.06418 8.51597C6.08083 8.17172 6.0886 7.82466 6.11636 7.48319C6.12469 7.3693 6.15799 7.22979 6.23299 7.15479C6.52168 6.86315 6.53369 6.48273 6.6059 6.11893C6.84205 4.91086 7.44435 3.91378 8.36915 3.11112C8.42471 3.05556 8.49204 3.02001 8.58092 2.95613C8.84473 4.13914 9.07768 5.29727 9.56921 6.39976C9.69688 6.36368 9.80526 6.33332 9.93291 6.30001C9.81902 5.50001 9.6999 4.71882 9.5999 3.93827C9.53326 3.43008 9.47757 2.91895 9.45258 2.4052C9.4304 1.9859 9.56645 1.8383 9.9636 1.72998Z" fill="url(#paint2_linear_1579_5027)"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_dddi_1579_5027" x="-5.00867" y="-5.49902" width="33.0188" height="41.9993" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.380392 0 0 0 0 0.568627 0 0 0 0 1 0 0 0 0.28 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1579_5027"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.380392 0 0 0 0 0.568627 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1579_5027" result="effect2_dropShadow_1579_5027"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.709804 0 0 0 0 0.835294 0 0 0 0 0.972549 0 0 0 0.03 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_1579_5027" result="effect3_dropShadow_1579_5027"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_1579_5027" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.165999"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect4_innerShadow_1579_5027"/>
</filter>
<linearGradient id="paint0_linear_1579_5027" x1="3.76851" y1="6.93842" x2="19.9512" y2="6.93842" gradientUnits="userSpaceOnUse">
<stop stop-color="#EDFEFF"/>
<stop offset="1" stop-color="#9EC5F5"/>
</linearGradient>
<linearGradient id="paint1_linear_1579_5027" x1="3.76851" y1="6.93842" x2="19.9512" y2="6.93842" gradientUnits="userSpaceOnUse">
<stop stop-color="#EDFEFF"/>
<stop offset="1" stop-color="#9EC5F5"/>
</linearGradient>
<linearGradient id="paint2_linear_1579_5027" x1="3.76851" y1="6.93842" x2="19.9512" y2="6.93842" gradientUnits="userSpaceOnUse">
<stop stop-color="#EDFEFF"/>
<stop offset="1" stop-color="#9EC5F5"/>
</linearGradient>
</defs>
</svg>

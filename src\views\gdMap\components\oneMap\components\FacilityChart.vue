<template>
	<div class="facility-chart-container">
		<div class="facility-pie-wrap">
			<div class="facility-pie-bg"></div>
			<v-chart class="facility-pie" :option="option" autoresize />
			<div class="facility-center">
				<div class="facility-center-title">设备总数</div>
				<div class="facility-center-value">{{ total.toLocaleString() }}</div>
				<div class="facility-center-unit">台</div>
			</div>
		</div>
		<div class="facility-legend-list">
			<div v-for="(item, index) in props.data" :key="item.name" class="facility-legend-row">
				<span class="facility-legend-dot" :style="{ background: colorList[index] }"></span>
				<span class="facility-legend-label">{{ item.name }}</span>
				<span class="facility-legend-percent">{{ item.percent }}%</span>
				<span class="facility-legend-value">{{ item.value.toLocaleString() }}</span>
				<span class="facility-legend-unit">台</span>
			</div>
		</div>
	</div>
</template>

<script setup>
import VChart from "vue-echarts";

const props = defineProps({
	data: {
		type: Array,
		default: () => [],
	},
});

const colorList = ["#2D8CF0", "#FFB800", "#00C292", "#00D8FF"];

const total = computed(() => props.data.reduce((sum, item) => sum + item.value, 0));

const option = computed(() => ({
	color: colorList,
	tooltip: {
		trigger: "item",
		// formatter: '{b} <br/>{c}台 ({d}%)',
		confine: true,
	},
	legend: { show: false },
	series: [
		{
			name: "设备总数",
			type: "pie",
			radius: ["70%", "90%"],
			center: ["50%", "50%"],
			avoidLabelOverlap: false,
			itemStyle: {
				borderRadius: 0,
				borderColor: "#111",
				borderWidth: 2,
			},
			label: { show: false },
			emphasis: { scale: false },
			labelLine: { show: false },
			data: props.data.map((item) => ({
				value: item.value,
				name: item.name,
			})),
		},
	],
}));
</script>

<style lang="scss" scoped>
.facility-chart-container {
	position: relative;
	width: 100%;
	height: 100%;
	display: flex;

	.facility-pie-wrap {
		width: 50%;
		height: 100%;
		// display: flex;
		// align-items: center;
		// justify-content: center;
		position: relative;

		.facility-pie-bg {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			width: 171px;
			height: 144px;
			background: url(~@/assets/images/people/echartBg.svg) center center/cover no-repeat;
		}

		.facility-pie {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			width: 144px;
			height: 144px;
			z-index: 1;
		}

		.facility-center {
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
			text-align: center;
			pointer-events: none;
			z-index: 0;

			.facility-center-title {
				font-size: 12px;
				color: #b1d2e9;
				margin-bottom: 2px;
			}

			.facility-center-value {
				font-weight: 600;
				font-size: 20px;
				text-align: center;
				background: -webkit-linear-gradient(top, #f7fdfd 0%, #29a9ff 100%);
				background: linear-gradient(to bottom, #f7fdfd 0%, #29a9ff 100%);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
			}

			.facility-center-unit {
				font-size: 12px;
				color: #b1d2e9;
				margin-top: 2px;
			}
		}
	}

	.facility-legend-list {
		width: 50%;
		// height: 100%;
		// margin-left: 24px;
		display: flex;
		flex-direction: column;
		justify-content: center;

		.facility-legend-row {
			display: flex;
			align-items: center;
			margin-bottom: 12px;
			font-size: 12px;
			color: #c4e5ff;
			font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;

			.facility-legend-dot {
				width: 6px;
				height: 6px;
				// border-radius: 2px;
				margin-right: 8px;
				display: inline-block;
			}

			.facility-legend-label {
				width: 80px;
				border-right: 1px solid #c4e5ff;
			}

			.facility-legend-percent {
				width: 48px;
				text-align: right;
			}

			.facility-legend-value {
				width: 60px;
				text-align: right;
			}

			.facility-legend-unit {
				margin-left: 4px;
			}
		}
	}
}
</style>

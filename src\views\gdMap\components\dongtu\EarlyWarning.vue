<template>
  <div class="center-card">
    <div class="center-card-hd">
      <div class="center-card-hd-bg"></div>
      <div class="saoguang">
        <img src="@/assets/images/m-card/saoguang.svg" alt="" />
      </div>
      <div class="center-card-hd-title">预警统计</div>
      <img class="title-img" src="@/assets/images/dongtu/alarm-title.png" alt="" />
    </div>
    <div class="center-card-bd">
      <div class="center-card-bd-bg"></div>
      <div class="center-card-bd-content">
        <div class="early-warning">
          <v-chart ref="myChart" :option="option" :autoresize="true" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import dayjs from 'dayjs';
import { ref, onMounted, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';
import VChart from 'vue-echarts';
import { getEarlyWarning } from '@/views/gdMap/services/dongtu.mock.js';

const option = ref();
const myChart = ref();
const data = [];
const now = new Date().getTime();
let latestTime = now;
let timer = null;
// 使用默认生成时间数据
const generalDefault = list => {
  for (let i = 0; i < list.length; i++) {
    const time = dayjs(now - 10000 * (list.length - i)).format('HH:mm:ss');
    const item = [time, list[i]];
    data.push(item);
  }
};

const randomData = () => {
  const time = dayjs(latestTime).format('HH:mm:ss');
  const item = [time, Math.floor(Math.random() * 100)];
  return item;
};

onMounted(async () => {
  const defaultData = await getEarlyWarning();
  generalDefault(defaultData.list);
  setOption(data);
  // 每隔10s更新一次数据
  let startNumber = 1;
  const len = defaultData.list.length;
  timer = setInterval(() => {
    data.shift();
    data.push(randomData());
    latestTime += 10000;
    myChart.value.setOption({
      series: [
        {
          data: data,
        },
      ],
    });
    myChart.value.dispatchAction({
      type: 'dataZoom',
      startValue: startNumber,
      endValue: startNumber + len - 1,
    });
    startNumber++;
  }, 10000);
});

onBeforeUnmount(() => {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
});

const setOption = data => {
  option.value = {
    title: {
      text: '单位: 个',
      left: '0.8%',
      top: '3%',
      textStyle: {
        color: '#ffffff',
        fontSize: 10,
      },
    },
    grid: {
      left: '3%',
      top: '12%',
      right: '3%',
      bottom: '12%',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
        shadowStyle: { opacity: 0.2 },
      },
      backgroundColor: 'rgba(0,0,0,1)',
      borderWidth: 1,
      borderColor: '#999999',
      textStyle: {
        color: '#ffffff',
        fontSize: 10,
      },
    },
    color: ['#6BC7F6'],
    dataZoom: [
      {
        type: 'slider',
        show: false,
        realtime: true,
        startValue: 0,
        endValue: data.length - 1, // 初始显示index0-12的数据,可根据你的数据量设置
        filterMode: 'none',
      },
    ],
    xAxis: [
      {
        type: 'category',
        boundaryGap: true,
        axisLine: {
          show: true,
          lineStyle: {
            color: '#ffffff',
          },
        },
        axisTick: {
          show: true,
          alignWithLabel: true,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          color: '#ffffff',
          fontSize: 10,
          padding: [0, 0, 0, 0],
        },
        // data: time,
      },
    ],
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: ['rgba(255, 255, 255, 0.3)'],
          opacity: 1,
          width: 1,
        },
      },
      axisLabel: {
        color: '#ffffff',
        fontSize: 10,
      },
    },
    series: [
      {
        data: data,
        type: 'line',
        symbol:
          'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAQ5JREFUOE+1la1OQ0EQRs8kCFIQdVCCxIBpCK1AInkDHC+B4QkwvASub4GsaE0NGCRpKhGFVDQZ+Mjem9vNbkphmWTMfjNnd3b2x0iYuxuwH7wNbIewBfAGzORm5nG6ElfM3QU6AXZSkzXG3oEnMxO8thWgux8DR2tAsfxiZs/VYA38Jazi1NBvYCizv+HK4vCRyrfQgIsf7Nm6+bSnjwIeAGeZaGnXQDfoE+ABmGbixwKeAoeJAMHuv3w30ubATQb6KqDKjZPEuAXOMysZAncJbS7gJbCVEAdAKwP8AK4S2vJfgMVLLt6UDtAreWx0W8od7OJXryq16OPwR2j6+WpAyz2wDagape7vARt9AZ+G3HmhiKS3xwAAAABJRU5ErkJggg==',
        symbolSize: 10,
        showSymbol: true, // 是否显示 symbol, 如果 false 则只有在 tooltip hover 的时候显示。
        yAxisIndex: 0,
        z: 0,
        label: {
          show: false,
          position: 'top',
          distance: 10,
          color: '#ffffff',
          fontSize: 10,
        },
        lineStyle: {
          shadowColor: 'rgba(0, 0, 0, 0.4)',
          shadowBlur: 3,
          shadowOffsetY: 4,
          width: 3,
          color: '#499DEE',
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(73,157,238,0.54)',
            },
            {
              color: 'rgba(10,79,145,0)',
              offset: 1,
            },
          ]),
        },
      },
    ],
  };
};
</script>

<style lang="scss" scoped>
.center-card {
  bottom: 164px;
  position: absolute;
  left: 423px;
  right: 423px;
  height: 328px;
  z-index: 3;
  .title-img {
    width: 14px;
    height: 14px;
    position: absolute;
    left: 118px;
    top: 13px;
  }
  &-hd {
    position: absolute;
    height: 39px;
    width: 100%;
    z-index: 2;
    &-bg {
      position: absolute;
      height: 39px;
      width: 100%;
      background-size: 100% 100%;
      background-image: url('~@/assets/images/dongtu/card-header-center.png');
    }
    &-title {
      position: absolute;
      left: 33px;
      color: #fff;
      font-size: 20px;
      letter-spacing: 1.6px;
      height: 39px;
      line-height: 39px;
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-weight: 400;
      text-shadow: 0px 0px 9px #158eff;
    }
    .saoguang {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 39px;
      overflow: hidden;
      pointer-events: none;
      img {
        width: 89px;
        height: 39px;
        animation: saoguangMove 6s linear infinite;
      }
    }
  }
  &-bd {
    position: absolute;
    left: 0;
    top: 43px;
    width: 100%;
    height: 285px;
    z-index: 1;
    &-bg {
      position: absolute;
      width: 100%;
      height: 285px;
      background-image: url('~@/assets/images/dongtu/card-bg-center.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
    &-content {
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      pointer-events: all;
      overflow: hidden;
    }
  }
}

.early-warning {
  color: #fff;
  height: 100%;
}
</style>

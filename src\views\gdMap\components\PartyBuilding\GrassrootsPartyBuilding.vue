<template>
	<BasicCard title="基层党建">
		<div class="grassroots-party-building">
			<div class="card-content">
				<div class="tabs">
					<template v-for="tab in tabList" :key="tab.value">
						<div
							class="tabs-item"
							:class="{ 'is-active': tab.value === activeTab }"
							@click="onClickTab(tab)"
						>
							<div class="tabs-item-bg" />
							<div class="tabs-item-text">
								{{ tab.label }}
							</div>
						</div>
					</template>
				</div>
				<div class="news">
					<div v-for="item in data" :key="item.id" class="news-item" @click="onDetail(item)">
						<div class="news-item-desc">
							{{ item.abstractInfo }}
						</div>
					</div>
					<a-empty v-if="data.length === 0" />
				</div>
			</div>
		</div>
		<template #extra>
			<div class="link" @click="onInfo">查看详情 <icon-double-right /></div>
		</template>
	</BasicCard>
	<GrassrootsPartyBuildingModal ref="GrassrootsPartyBuildingModalRef" />
	<ParagraphModal ref="ParagraphModalRef" />
</template>

<script setup>
import { usePartyBuildingStore } from "@/store";
import request from "@/utils/request";
import GrassrootsPartyBuildingModal from "./components/GrassrootsPartyBuildingModal.vue";
import ParagraphModal from "./components/ParagraphModal.vue";

const { proxy } = getCurrentInstance();
const PartyBuildingStore = usePartyBuildingStore();
const tabList = [
	{ label: "三会一课", value: "三会一课" },
	{ label: "党组织建设", value: "党组织建设" },
	{ label: "身边的榜样", value: "身边的榜样" },
];
const activeTab = ref("三会一课");
const data = ref([]);

onMounted(() => {
	getData();
});

watch(
	() => PartyBuildingStore.getDepartment,
	() => {
		getData();
	}
);

const getData = () => {
	const params = {
		type: "基层党建",
		department: PartyBuildingStore.getDepartment,
		grassrootType: unref(activeTab),
	};
	request.get("/api/screen/dangjian/article/list", params).then((res) => {
		if (res.code == 200) {
			data.value = res.data;
		}
	});
};

const onClickTab = (tab) => {
	activeTab.value = tab.value;
	getData();
};

const onInfo = () => {
	proxy.$refs.GrassrootsPartyBuildingModalRef.open();
};

const onDetail = (item) => {
	proxy.$refs.ParagraphModalRef.open(item.id);
};
</script>

<style lang="scss" scoped>
.grassroots-party-building {
	height: 208px;
	width: 100%;
}

.card-content {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
	overflow: hidden;
}

.tabs {
	display: flex;
	flex-direction: row;
	padding: 10px 18px;
	margin-bottom: 3px;
	column-gap: 12px;
	&-item {
		flex: 1;
		height: 38px;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		cursor: pointer;

		&-bg {
			position: absolute;
			width: 100%;
			height: 100%;
			border-radius: 2px;
			background: linear-gradient(180deg, rgba(235, 156, 0, 0) 47%, #eb9c00 100%),
				rgba(210, 145, 18, 0.2);
			transform: skewX(-20deg); /* 水平倾斜 */
		}

		&.is-active &-bg {
			background: linear-gradient(180deg, rgba(235, 156, 0, 0) 47%, #eb9c00 100%), #d29112;
		}

		&-text {
			font-family: "Alibaba PuHuiTi";
			font-size: 18px;
			color: #f8cac5;
			z-index: 1;
		}

		&.is-active &-text {
			color: #ffffff;
		}
	}
}

.news {
	flex: 1;
	display: flex;
	flex-direction: column;
	row-gap: 12px;

	&-item {
		height: 26px;
		border: 1px solid;
		border-image: linear-gradient(180deg, rgba(255, 64, 44, 0), rgba(255, 64, 44, 1)) 1 1;
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 0 32px;
		cursor: pointer;

		&-desc {
			font-family: Alibaba PuHuiTi;
			font-size: 12px;
			color: #ffffff;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
	}
}

.link {
	font-family: Alibaba PuHuiTi;
	font-size: 14px;
	color: #ffffff;
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-top: auto;
	margin-bottom: 13px;
	cursor: pointer;
}

:deep(.arco-empty) {
	margin: auto;
	.arco-empty-image,
	.arco-empty-description {
		color: #fdcb01;
	}
}
</style>

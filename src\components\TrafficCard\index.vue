<template>
	<div class="traffic-card card-container" :style="{ height: props.height + 'px' }">
		<div class="card-header">
			<div class="card-title">{{ title }}</div>
			<div class="card-extra">
				<slot name="extra" />
			</div>
		</div>
		<div class="card-body">
			<slot />
		</div>
	</div>
</template>

<script setup>
const props = defineProps({
	height: Number,
	title: {
		type: String,
		default: "",
	},
});
</script>

<style lang="scss" scoped>
.traffic-card {
	& + .traffic-card {
		margin-top: 8px;
	}
}

.card-container {
	width: 100%;
	background: linear-gradient(
		90deg,
		rgba(0, 138, 255, 0.08) 0%,
		rgba(0, 138, 255, 0.12) 50%,
		rgba(0, 138, 255, 0.08) 100%
	);
}

.card-header {
	height: 58px;
	background-image: url("@/assets/modules/traffic/bg/card-header-bg.webp");
	background-size: 100% 100%;
	display: flex;
	flex-direction: row;
	align-items: end;
	justify-content: space-between;
}

.card-body {
	margin-top: -8px;
	background: rgba(9, 16, 27, 0.5);
}

.card-title {
	font-family: Alibaba PuHuiTi;
	font-size: 22px;
	color: #ffffff;
	letter-spacing: 2px;
	margin-left: 71px;
	margin-bottom: 24px;
}

.card-extra {
	font-family: Alibaba PuHuiTi;
	font-size: 14px;
	color: #ffffff;
	margin-bottom: 17px;

	:deep(.link) {
		display: flex;
		flex-direction: row;
		align-items: center;
		cursor: pointer;
	}
}
</style>

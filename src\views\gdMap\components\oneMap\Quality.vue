<template>
  <TrafficCard title="质量管理">
    <div class="quality-container">
      <QualityChart :data="data" />
    </div>
  </TrafficCard>
</template>

<script setup>
import request from "@/utils/request";
import QualityChart from "./components/QualityChart.vue";
import emitter from "@/utils/emitter";
const data = ref([]);
const currentDepartmentId = ref(null);

onMounted(() => {
  // 监听区域双击事件
  emitter.$on("division-area-dblclick", handleAreaChange);
  getData();
});

onBeforeUnmount(() => {
  emitter.$off("division-area-dblclick", handleAreaChange);
});

const handleAreaChange = (eventData) => {
  console.log("质量管理 - 接收到区域双击事件:", eventData);
  currentDepartmentId.value = eventData.properties.id;
  getData();
};

const getData = () => {
  const params = currentDepartmentId.value ? { departmentId: currentDepartmentId.value } : {};
  request.get("/api/screen/board/right/quality/stat", params).then((res) => {
    if (res.code === 200) {
      data.value = res.data
        .map(({ deviceType, deviceNumber }) => ({
          name: deviceType,
          value: deviceNumber,
          checked: true,
        }))
        .slice(0, 3);
    }
  });
};
</script>

<style lang="scss" scoped>
.quality-container {
  height: 132px;
}
</style>

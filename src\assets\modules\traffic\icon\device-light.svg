<svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#231;&#186;&#162;&#231;&#187;&#191;&#231;&#129;&#175;1">
<g id="&#231;&#186;&#162;&#231;&#187;&#191;&#231;&#129;&#175;1_2">
<g id="&#229;&#174;&#158;&#230;&#151;&#182;&#232;&#183;&#175;&#229;&#134;&#181;" clip-path="url(#clip0_1143_3708)">
<g id="Vector" filter="url(#filter0_dddi_1143_3708)">
<path d="M17.5594 9.35814H15.0822V7.75112C15.4077 7.75189 15.7302 7.68834 16.0311 7.56412C16.332 7.4399 16.6054 7.25746 16.8356 7.02727C17.0658 6.79708 17.2482 6.52368 17.3724 6.22277C17.4967 5.92187 17.5602 5.59938 17.5594 5.27385H15.0822C15.085 5.06202 15.0454 4.85176 14.9657 4.65549C14.8859 4.45922 14.7677 4.28093 14.6179 4.13113C14.4681 3.98132 14.2898 3.86306 14.0935 3.78331C13.8972 3.70356 13.687 3.66395 13.4751 3.66681H8.52059C8.30876 3.66395 8.09851 3.70356 7.90224 3.78331C7.70597 3.86306 7.52767 3.98132 7.37787 4.13113C7.22807 4.28093 7.10981 4.45922 7.03006 4.65549C6.95031 4.85176 6.9107 5.06202 6.91356 5.27385H4.43629C4.43551 5.59938 4.49906 5.92187 4.62328 6.22277C4.7475 6.52368 4.92994 6.79708 5.16013 7.02727C5.39032 7.25746 5.66372 7.4399 5.96463 7.56412C6.26554 7.68834 6.58802 7.75189 6.91356 7.75112V9.35814H4.43629C4.43551 9.68368 4.49906 10.0062 4.62328 10.3071C4.7475 10.608 4.92994 10.8814 5.16013 11.1116C5.39032 11.3418 5.66372 11.5242 5.96463 11.6484C6.26554 11.7726 6.58802 11.8362 6.91356 11.8354V13.4424H4.43629C4.43551 13.768 4.49906 14.0905 4.62328 14.3914C4.7475 14.6923 4.92994 14.9657 5.16013 15.1959C5.39032 15.4261 5.66372 15.6085 5.96463 15.7327C6.26554 15.8569 6.58802 15.9205 6.91356 15.9197V16.7262C6.9107 16.938 6.95031 17.1483 7.03006 17.3445C7.10981 17.5408 7.22807 17.7191 7.37787 17.8689C7.52767 18.0187 7.70597 18.137 7.90224 18.2167C8.09851 18.2965 8.30876 18.3361 8.52059 18.3332H13.4084C13.6202 18.3361 13.8305 18.2965 14.0268 18.2167C14.223 18.137 14.4013 18.0187 14.5511 17.8689C14.7009 17.7191 14.8192 17.5408 14.899 17.3445C14.9787 17.1483 15.0183 16.938 15.0155 16.7262V15.9197C15.341 15.9205 15.6635 15.8569 15.9644 15.7327C16.2653 15.6085 16.5387 15.4261 16.7689 15.1959C16.9991 14.9657 17.1815 14.6923 17.3057 14.3914C17.43 14.0905 17.4935 13.768 17.4927 13.4424H15.0155V11.8354C15.6798 11.8392 16.319 11.5815 16.795 11.118C17.271 10.6545 17.5456 10.0224 17.5594 9.35814ZM10.9979 16.7232C10.786 16.7261 10.5758 16.6865 10.3795 16.6067C10.1832 16.527 10.0049 16.4087 9.85514 16.2589C9.70534 16.1091 9.58707 15.9308 9.50733 15.7346C9.42758 15.5383 9.38796 15.328 9.39083 15.1162C9.39083 14.7984 9.48508 14.4877 9.66166 14.2234C9.83824 13.9591 10.0892 13.7531 10.3829 13.6315C10.6765 13.5099 10.9996 13.478 11.3114 13.54C11.6231 13.602 11.9095 13.7551 12.1342 13.9799C12.359 14.2046 12.512 14.4909 12.574 14.8027C12.636 15.1144 12.6042 15.4375 12.4826 15.7312C12.3609 16.0248 12.155 16.2758 11.8907 16.4524C11.6264 16.629 11.3157 16.7232 10.9979 16.7232ZM10.9979 12.6389C10.5725 12.6377 10.165 12.4678 9.86462 12.1665C9.56428 11.8653 9.39563 11.4573 9.39563 11.0319C9.39563 10.6065 9.56428 10.1985 9.86462 9.89725C10.165 9.59601 10.5725 9.42614 10.9979 9.42487C11.2093 9.42424 11.4188 9.46534 11.6143 9.54581C11.8099 9.62629 11.9876 9.74457 12.1373 9.89385C12.287 10.0431 12.4059 10.2205 12.4869 10.4158C12.568 10.6111 12.6097 10.8205 12.6097 11.0319C12.6097 11.2433 12.568 11.4527 12.4869 11.648C12.4059 11.8433 12.287 12.0207 12.1373 12.1699C11.9876 12.3192 11.8099 12.4375 11.6143 12.518C11.4188 12.5985 11.2093 12.6396 10.9979 12.6389ZM10.9979 8.55464C10.5717 8.5546 10.163 8.38526 9.86161 8.08389C9.56027 7.78252 9.39098 7.37378 9.39098 6.9476C9.39098 6.52141 9.56027 6.11268 9.86161 5.8113C10.163 5.50993 10.5717 5.3406 10.9979 5.34056C11.2089 5.34054 11.4179 5.38209 11.6129 5.46284C11.8079 5.5436 11.9851 5.66197 12.1343 5.8112C12.2836 5.96043 12.4019 6.13759 12.4827 6.33258C12.5635 6.52756 12.6051 6.73655 12.6051 6.9476C12.6051 7.15865 12.5635 7.36763 12.4827 7.56262C12.4019 7.7576 12.2836 7.93477 12.1343 8.084C11.9851 8.23323 11.8079 8.3516 11.6129 8.43235C11.4179 8.5131 11.2089 8.55466 10.9979 8.55464Z" fill="url(#paint0_linear_1143_3708)"/>
</g>
</g>
</g>
</g>
<defs>
<filter id="filter0_dddi_1143_3708" x="-3.56372" y="-3.33334" width="29.1232" height="37.6667" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.380392 0 0 0 0 0.568627 0 0 0 0 1 0 0 0 0.28 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1143_3708"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.380392 0 0 0 0 0.568627 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1143_3708" result="effect2_dropShadow_1143_3708"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.709804 0 0 0 0 0.835294 0 0 0 0 0.972549 0 0 0 0.03 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_1143_3708" result="effect3_dropShadow_1143_3708"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_1143_3708" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.165999"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect4_innerShadow_1143_3708"/>
</filter>
<linearGradient id="paint0_linear_1143_3708" x1="5.03556" y1="7.86416" x2="17.514" y2="7.86416" gradientUnits="userSpaceOnUse">
<stop stop-color="#EDFEFF"/>
<stop offset="1" stop-color="#9EC5F5"/>
</linearGradient>
<clipPath id="clip0_1143_3708">
<rect width="22" height="22" fill="white"/>
</clipPath>
</defs>
</svg>

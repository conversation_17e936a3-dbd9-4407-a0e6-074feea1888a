// 保通默认数据
const data = {
  // 监测总览
  // 项目统计
  projectStat: {
    listData: [
      {
        name: '路基路面项目',
        value: 20,
        unit: '个',
        image: 'proStat1',
      },
      {
        name: '桥梁项目',
        value: 10,
        unit: '个',
        image: 'proStat2',
      },
      {
        name: '隧道项目',
        value: 10,
        unit: '个',
        image: 'proStat3',
      },
      {
        name: '边坡项目',
        value: 100,
        unit: '个',
        image: 'proStat4',
      },
    ],
  },
  // 报警统计
  alarmStat: {
    listData: [
      {
        name: '路基路面项目',
        value: 20,
        unit: '个',
        image: 'proStat1',
      },
      {
        name: '桥梁项目',
        value: 10,
        unit: '个',
        image: 'proStat2',
      },
      {
        name: '隧道项目',
        value: 10,
        unit: '个',
        image: 'proStat3',
      },
      {
        name: '边坡项目',
        value: 100,
        unit: '个',
        image: 'proStat4',
      },
    ],
  },
  // 测点状态统计
  pointStat: {
    list: [
      { time: '2025-03-13 19:22:10', type: '报警', name: 'YPC(1)-DIS-PO9YJJK' },
      { time: '2025-03-13 18:34:22', type: '正常', name: 'ZK3080+100' },
      { time: '2025-03-13 17:10:13', type: '报警', name: 'ZK3080+100' },
      { time: '2025-03-13 16:50:50', type: '报警', name: 'ZK3080+100' },
      { time: '2025-03-13 14:28:30', type: '报警', name: 'ZK3080+100' },
      { time: '2025-03-13 13:30:22', type: '离线', name: 'ZK3080+100' },
      { time: '2025-03-13 11:20:59', type: '报警', name: 'ZK3080+100' },
      { time: '2025-03-13 09:40:00', type: '报警', name: 'ZK3080+100' },
      { time: '2025-03-13 08:30:10', type: '正常', name: 'ZK3080+100' },
      { time: '2025-03-13 07:22:30', type: '报警', name: 'ZK3080+100' },
      { time: '2025-03-13 07:22:30', type: '报警', name: 'ZK3080+100' },
      { time: '2025-03-13 07:22:30', type: '报警', name: 'ZK3080+100' },
      { time: '2025-03-13 07:22:30', type: '报警', name: 'ZK3080+100' },
      { time: '2025-03-13 07:22:30', type: '报警', name: 'ZK3080+100' },
    ],
  },
  // 测点信息
  pointInfo: {
    treeData: [
      {
        title: 'G109国道冻土数字孪生平台监测项目',
        key: '0-0',
        children: [
          {
            title: '路基路面项目',
            key: '0-0-0',
            children: [
              {
                title: 'OSHYMITDO-RAD-T-01-001-02',
                key: '0-0-0-0',
                status: 'online',
              },
              {
                title: 'OSHYMITDO-RAD-T-01-001-02',
                key: '0-0-0-1',
                status: 'offline',
              },
            ],
          },
          {
            title: '桥梁项目',
            key: '0-0-1',
            children: [
              {
                title: 'OSHYMITDO-RAD-T-01-001-02',
                key: '0-0-1-0',
                status: 'online',
              },
            ],
          },
          {
            title: '隧道项目',
            key: '0-0-2',
            children: [
              {
                title: 'OSHYMITDO-RAD-T-01-001-02',
                key: '0-0-2-0',
                status: 'online',
              },
            ],
          },
          {
            title: '边坡项目',
            key: '0-0-3',
            children: [
              {
                title: 'OSHYMITDO-RAD-T-01-001-02',
                key: '0-0-3-0',
                status: 'online',
              },
            ],
          },
        ],
      },
    ],
  },
  // 测点状态统计
  personPatrol: {
    list: [
      { inspector: '张力', time: '2025-03-13 19:22:10', type: '异常', name: '日常巡查' },
      { inspector: '张力', time: '2025-03-13 18:34:22', type: '正常', name: '日常巡查' },
      { inspector: '张力', time: '2025-03-13 17:10:13', type: '异常', name: '定期巡查' },
      { inspector: '张力', time: '2025-03-13 16:50:50', type: '异常', name: '定期巡查' },
      { inspector: '张力', time: '2025-03-13 14:28:30', type: '警告', name: '定期巡查' },
      { inspector: '张力', time: '2025-03-13 13:30:22', type: '异常', name: '日常巡查' },
      { inspector: '张力', time: '2025-03-13 11:20:59', type: '异常', name: '日常巡查' },
      { inspector: '张力', time: '2025-03-13 09:40:00', type: '异常', name: '定期巡查' },
      { inspector: '张力', time: '2025-03-13 08:30:10', type: '正常', name: '日常巡查' },
      { inspector: '张力', time: '2025-03-13 07:22:30', type: '异常', name: '日常巡查' },
      { inspector: '张力', time: '2025-03-13 07:22:30', type: '异常', name: '定期巡查' },
      { inspector: '张力', time: '2025-03-13 07:22:30', type: '异常', name: '日常巡查' },
      { inspector: '张力', time: '2025-03-13 07:22:30', type: '异常', name: '定期巡查' },
      { inspector: '张力', time: '2025-03-13 07:22:30', type: '异常', name: '日常巡查' },
    ],
  },
  // 实时监测
  // 监测总览
  monitorOverview: {
    listData: [
      {
        name: '监测点总数',
        value: 1023,
        unit: '个',
        image: 'proStat1',
      },
      {
        name: '报警次数',
        value: 53,
        unit: '个',
        image: 'proStat2',
      },
      {
        name: '已处理',
        value: 43,
        unit: '个',
        image: 'proStat3',
      },
      {
        name: '处理率',
        value: '93.21%',
        unit: '',
        image: 'proStat4',
      },
    ],
  },
  // 监测排行TOP5
  monitorRank: {
    listData: [
      {
        name: 'QSHYMJTDQ-RAD-T-02-002-01',
        value: '98.31%',
        unit: '',
      },
      {
        name: 'QSHYMJTDQ-RAD-T-02-002-02',
        value: '85.23%',
        unit: '',
      },
      {
        name: 'QSHYMJTDQ-RAD-T-02-002-03',
        value: '79.17%',
        unit: '',
      },
      {
        name: 'QSHYMJTDQ-RAD-T-02-002-04',
        value: '74.39%',
        unit: '',
      },
      {
        name: 'QSHYMJTDQ-RAD-T-02-002-05',
        value: '67.87%',
        unit: '',
      },
    ],
  },
  // 数据总览
  dataOverview: {
    list: [
      {
        unit: 'cm',
        value: '0.590',
        time: '2025-03-13 19:22:10',
        type: '异常',
        name: 'QSHYMJTDQ-RAD-T-02-002-01',
      },
      {
        unit: 'cm',
        value: '0.590',
        time: '2025-03-13 18:34:22',
        type: '正常',
        name: 'QSHYMJTDQ-RAD-T-02-002-01',
      },
      {
        unit: 'cm',
        value: '0.590',
        time: '2025-03-13 17:10:13',
        type: '异常',
        name: 'QSHYMJTDQ-RAD-T-02-002-02',
      },
      {
        unit: 'cm',
        value: '0.590',
        time: '2025-03-13 16:50:50',
        type: '异常',
        name: 'QSHYMJTDQ-RAD-T-02-002-01',
      },
      {
        unit: 'cm',
        value: '0.590',
        time: '2025-03-13 14:28:30',
        type: '警告',
        name: 'QSHYMJTDQ-RAD-T-02-002-03',
      },
      {
        unit: 'cm',
        value: '0.590',
        time: '2025-03-13 13:30:22',
        type: '异常',
        name: 'QSHYMJTDQ-RAD-T-02-002-04',
      },
      {
        unit: 'cm',
        value: '0.590',
        time: '2025-03-13 11:20:59',
        type: '异常',
        name: 'QSHYMJTDQ-RAD-T-02-002-01',
      },
      {
        unit: 'cm',
        value: '0.590',
        time: '2025-03-13 09:40:00',
        type: '异常',
        name: 'QSHYMJTDQ-RAD-T-02-002-02',
      },
      {
        unit: 'cm',
        value: '0.590',
        time: '2025-03-13 08:30:10',
        type: '正常',
        name: 'QSHYMJTDQ-RAD-T-02-002-01',
      },
      {
        unit: 'cm',
        value: '0.590',
        time: '2025-03-13 07:22:30',
        type: '异常',
        name: 'QSHYMJTDQ-RAD-T-02-002-03',
      },
      {
        unit: 'cm',
        value: '0.590',
        time: '2025-03-13 07:22:30',
        type: '异常',
        name: 'QSHYMJTDQ-RAD-T-02-002-01',
      },
      {
        unit: 'cm',
        value: '0.590',
        time: '2025-03-13 07:22:30',
        type: '异常',
        name: 'QSHYMJTDQ-RAD-T-02-002-04',
      },
    ],
  },
};

export default data;

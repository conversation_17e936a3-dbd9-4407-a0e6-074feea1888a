<template>
	<v-chart class="chart" :option="option" autoresize :init-options="{ renderer: 'svg' }" />

	<svg>
		<linearGradient id="textGradient" x1="0" y1="0" x2="0" y2="1">
			<stop offset="24%" stop-color="#FFFFFF"></stop>
			<stop offset="48%" stop-color="#FCFFD9"></stop>
			<stop offset="77%" stop-color="#FF4200"></stop>
		</linearGradient>
	</svg>
</template>

<script setup>
import * as echarts from "echarts";
import VChart from "vue-echarts";

const props = defineProps({
	data: {
		type: Array,
		default: [],
	},
});

const option = ref(null);

onMounted(() => {
	setOptions();
});

watch(
	() => props.data,
	() => setOptions()
);

const setOptions = () => {
	option.value = {
		grid: {
			left: "8%",
			right: "4%",
			top: "12%",
			bottom: "16%",
		},
		xAxis: {
			type: "category",
			data: props.data.map(({ name }) => name),
			axisLine: {
				lineStyle: {
					color: "#fff",
					opacity: 0.5,
				},
			},
			axisTick: {
				show: false,
			},
		},
		yAxis: {
			type: "value",
			splitLine: {
				lineStyle: {
					type: "dashed",
					color: "rgba(255, 255, 255, 0.2)",
				},
			},
		},
		axisLabel: {
			fontFamily: "Alibaba PuHuiTi",
			fontSize: "12px",
			color: "#F8CAC5",
		},
		label: {
			show: true,
			position: "top",
			fontFamily: "Alibaba PuHuiTi",
			fontSize: 14,
			color: "url(#textGradient)", //渐变色
			backgroundColor: "transparent",
		},
		series: [
			{
				type: "bar",
				itemStyle: {
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						{ offset: 0, color: "rgba(255,61,13,0)" },
						{ offset: 0.34, color: "rgba(255, 61, 13, 1)" },
						{ offset: 0.61, color: "rgba(255, 180, 0, 1)" },
						{ offset: 1, color: "rgba(255, 246, 0, 1)" },
					]),
					borderWidth: 1,
					borderColor: "rgba(255, 180, 44, 1)",
				},
				data: props.data.map(({ value }) => value),
			},
		],
	};
};
</script>

<style lang="scss" scoped>
.chart {
	width: 100%;
	height: 100%;
}
</style>

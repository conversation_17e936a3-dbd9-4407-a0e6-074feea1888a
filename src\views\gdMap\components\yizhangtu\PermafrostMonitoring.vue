<template>
  <div class="permafrost-monitoring">
    <a-spin class="card-loading" dot :loading="loading">
      <BasicCard title="冻土监测">
        <div class="card-content">
          <!-- 监测数据卡片 -->
          <div class="monitoring-cards">
            <div class="card-row">
              <div class="monitoring-card">
                <div class="card-icon">
                  <img
                    src="@/assets/modules/yizhangtu/icons/monitoring-points-total.png"
                    alt="监测点"
                  />
                </div>
                <div class="card-content">
                  <div class="card-value">{{ monitoringData.totalPoints }}<span class="unit">台</span></div>
                  <div class="card-title">监测点总数</div>
                </div>
              </div>
              <div class="monitoring-card">
                <div class="card-icon">
                  <img src="@/assets/modules/yizhangtu/icons/alarm-count.png" alt="报警" />
                </div>
                <div class="card-content">
                  <div class="card-value">{{ monitoringData.alarmCount }}<span class="unit">台</span></div>
                  <div class="card-title">报警次数</div>
                </div>
              </div>
            </div>
            <div class="card-row">
              <div class="monitoring-card">
                <div class="card-icon">
                  <img src="@/assets/modules/yizhangtu/icons/processed-status.png" alt="已处理" />
                </div>
                <div class="card-content">
                  <div class="card-value">{{ monitoringData.processedCount }}<span class="unit">台</span></div>
                  <div class="card-title">已处理</div>
                </div>
              </div>
              <div class="monitoring-card">
                <div class="card-icon">
                  <img src="@/assets/modules/yizhangtu/icons/processing-rate.png" alt="处理率" />
                </div>
                <div class="card-content">
                  <div class="card-value">{{ monitoringData.processingRate }}<span class="unit">%</span></div>
                  <div class="card-title">处理率</div>
                </div>
              </div>
            </div>
          </div>

          <div class="personnel-warning">
            <div class="warning-header">
              <div class="warning-title">
                <div class="warning-icon">
                  <span class="icon-arrow">
                    <img :src="RightArrow" alt="" />
                  </span>
                  <span class="title-text">监测预警</span>
                  <span class="title-icon">
                    <img :src="PersonWarning" alt="" />
                  </span>
                </div>
                <div class="date-filter">
                  <a-dropdown trigger="click">
                    <div class="filter-btn">
                      <span class="filter-btn-text">{{ selectedDays }}天</span>
                      <span class="arrow-down">
                        <icon-caret-down />
                      </span>
                    </div>
                    <template #content>
                      <a-doption v-for="day in dayOptions" :key="day" @click="changeDays(day)">
                        {{ day }}天
                      </a-doption>
                    </template>
                  </a-dropdown>
                </div>
              </div>
            </div>
            <ScreenTable height="350px" :columns="tableColumns">
              <div ref="listContainerRef" class="scroll-container">
                <div class="warning-table">
                  <div class="table-body">
                    <div class="table-row" v-for="(item, index) in warningData" :key="index">
                      <div class="col time">{{ item.time }}</div>
                      <div class="col type" @click="locateToPosition(item)">
                        <a-tooltip position="top">
                          <div class="ellipsis-text hover-highlight">{{ item.type }}</div>
                          <template #content>
                            {{ item.type }}
                          </template>
                        </a-tooltip>
                      </div>
                      <div class="col value">{{ item.area }}</div>
                      <div class="col status">
                        <span class="status-dot" style="background-color: #ff9e49;"></span>
                        {{ item.status }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </ScreenTable>
          </div>
        </div>
      </BasicCard>
    </a-spin>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount } from 'vue';
import BasicCard from '@/components/BasicCard/index.vue';
import ScreenTable from '../ScreenTable.vue';
import RightArrow from '@/assets/modules/common/icons/card-header-right-arrow.svg';
import PersonWarning from '@/assets/modules/yizhangtu/icons/monitoring-warning.svg';
import { useMarquee } from '@/hooks/useMarquee';
import { IconCaretDown } from '@arco-design/web-vue/es/icon';
import { getPermafrostMonitoringData, getPermafrostWarningData } from '../../services/yizhangtu.mock';
import emitter from '@/utils/emitter';

// 加载状态
const loading = ref(false);

// 监测数据
const monitoringData = ref({
  totalPoints: 0,
  alarmCount: 0,
  processedCount: 0,
  processingRate: 0
});

// 选中的天数，默认为7天
const selectedDays = ref(7);
// 天数选项
const dayOptions = [3, 7, 15, 30];

// 表格列配置
const tableColumns = [
  { title: '时间', width: '30%' },
  { title: '测点名称', width: '30%' },
  { title: '计算值', width: '20%' },
  { title: '报警状态', width: '20%', align: 'right' },
];

// 预警数据
const warningData = ref([]);

// 当前区域类型
const currentArea = ref('all');

// 使用跑马灯效果
const { containerRef: listContainerRef, resetScroll } = useMarquee({
  speed: 10, // 滚动速度
  delay: 3000, // 滚动到底部后停顿时间
  step: 1, // 每次滚动的像素
});

// 定位到地图上的指定位置
const locateToPosition = item => {
  if (!item || !item.coordinates || item.coordinates.length !== 2) {
    // Message.warning('该预警记录没有位置信息');
    return;
  }

  // 发送定位事件到地图引擎
  emitter.$emit('locateToPosition', {
    type: 'permafrostAlert',
    data: item, // 传递完整数据，以便地图引擎可以显示更多信息
  });
};

// 加载数据的方法
const loadData = async (value = 'all') => {
  // 更新当前区域
  currentArea.value = value;

  // 重新加载数据
  loading.value = true;
  try {
    monitoringData.value = await getPermafrostMonitoringData(currentArea.value);
    warningData.value = await getPermafrostWarningData(selectedDays.value, currentArea.value);
  } catch (error) {
    console.error('加载冻土监测数据失败:', error);
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 1000); // 延迟一点时间确保DOM已更新
  }
};

// 切换时间范围
const changeDays = async newDays => {
  selectedDays.value = newDays;
  // 重新加载数据
  loading.value = true;
  try {
    warningData.value = await getPermafrostWarningData(selectedDays.value, currentArea.value);
  } catch (error) {
    console.error('加载冻土监测预警数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 当数据变化时，重置滚动
watch(
  warningData,
  () => {
    setTimeout(resetScroll, 300); // 延迟一点时间确保DOM已更新
  },
  { deep: true }
);

// 组件挂载时初始化数据并监听事件
onMounted(() => {
  loadData();
  emitter.$on('pageNavChange', loadData);
});

// 组件销毁前移除事件监听
onBeforeUnmount(() => {
  emitter.$off('pageNavChange', loadData);
});
</script>

<style lang="scss" scoped>
.permafrost-monitoring {
  height: 320px;

  .card-content {
    display: flex;
    flex-direction: column;
    width: 100%;
  }
}

.monitoring-cards {
  padding-left: 30px;
  height: 126px;
  display: flex;
  flex-direction: column;
  align-items: space-between;
  justify-content: center;
  .card-row {
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .monitoring-card {
    flex: 1;
    height: 41px;
    display: flex;
    align-items: center;

    .card-icon {
      width: 41px;
      height: 100%;
      img {
        width: 100%;
        height: 100%;
      }
    }

    .card-content {
      margin-left: 12px;
      flex: 1;
      .card-value {
        font-family: D-DIN-PRO-Bold, D-DIN-PRO;
        font-weight: bold;
        font-size: 20px;
        color: #0783fa;
        .unit {
          margin-left: 2px;
          font-family: Source Han Sans CN-Regular, Source Han Sans CN;
          font-size: 10px;
          color: #ffffff;
          opacity: 0.8;
        }
      }

      .card-title {
        margin-top: 1px;
        font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        font-size: 12px;
        color: #ffffff;
        opacity: 0.8;
      }
    }
  }
}

.personnel-warning {
  flex: 1;
  overflow: hidden;
  background: rgba(10, 35, 75, 0.6);
  border: 1px solid rgba(23, 66, 114, 0.6);
  box-sizing: border-box;
  .warning-header {
    padding-left: 12px;
    padding-right: 5px;
    display: flex;
    align-items: center;
    height: 36px;
    border-bottom: 1px solid rgba(23, 66, 114, 0.6);

    .warning-title {
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 5x;

      .warning-icon {
        display: flex;
        align-items: center;

        .icon-arrow {
          margin-right: 5px;
          width: 24px;
          height: 16px;
          img {
            width: 100%;
            height: 100%;
          }
        }

        .title-text {
          margin-right: 3px;
          font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
          font-size: 16px;
          color: #ffffff;
          text-shadow: 0px 0px 9px #158eff;
        }

        .title-icon {
          width: 16px;
          height: 16px;
          cursor: pointer;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }

  .warning-table {
    .table-body {
      .table-row {
        display: flex;
        height: 28px;
        align-items: center;

        // 单行背景色
        &:nth-child(odd) {
          background-color: #091e3f;
        }

        // 双行背景色
        &:nth-child(even) {
          background-color: #142b50;
        }

        &:last-child {
          border-bottom: none;
        }

        .col {
          padding: 0 9px;
          font-size: 12px;
          color: #fff;
          font-family: Source Han Sans CN, Source Han Sans CN;
          cursor: pointer;
          &.time {
            width: 30%;
            color: #fff;
            text-align: left;
          }

          &.type {
            width: 30%;
            // 移除默认的蓝色
            color: #fff;

            .ellipsis-text {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              width: 100%;
              
              &.hover-highlight {
                transition: color 0.2s ease;
                
                &:hover {
                  color: #0783fa;
                  cursor: pointer;
                }
              }
            }
            
            &:active .ellipsis-text {
              color: #0783fa;
            }
          }

          &.value {
            width: 20%;
            text-align: center;
          }

          &.status {
            width: 20%;
            text-align: right;
            
            .status-dot {
              display: inline-block;
              width: 6px;
              height: 6px;
              border-radius: 1px;
              margin-right: 6px;
            }
          }
        }
      }
    }
  }
}

.scroll-container {
  height: 100%; // 容器需要有固定高度
  overflow: hidden; // 重要：设置为hidden，由hooks控制滚动
  position: relative; // 相对定位
}

.date-filter {
  .filter-btn {
    padding: 0 5px;
    display: flex;
    justify-content: space-between;
    width: 52px;
    height: 26px;
    line-height: 26px;
    background: #0b3567;
    box-shadow: inset 0px 0px 4px 0px #0e9aff;
    border-radius: 2px;
    border: 1px solid #1299ff;
    box-sizing: border-box;
    cursor: pointer;
    color: #ffffff;
    &-text {
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      font-size: 12px;
    }
    .arrow-down {
      width: 12px;
      height: 12px;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>

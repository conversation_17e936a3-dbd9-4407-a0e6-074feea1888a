<svg width="23" height="22" viewBox="0 0 23 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#232;&#191;&#135;&#231;&#168;&#139;&#230;&#142;&#146;&#230;&#148;&#190;">
<g id="&#232;&#191;&#135;&#231;&#168;&#139;&#230;&#142;&#146;&#230;&#148;&#190;_2">
<g id="Vector" filter="url(#filter0_dddi_1579_5212)">
<path d="M21.1465 19.2118V20.1094H1.85352V19.2118H21.1465ZM5.88441 6.20088L6.1418 13.3406L10.6796 10.986L10.6735 13.409L15.2556 10.9864L15.2056 13.341L19.8311 10.9856V18.7633H3.16894L3.62144 6.20091H5.88444L5.88441 6.20088ZM9.30763 14.7253H7.55371V16.52H9.30763V14.7253ZM13.6924 14.7253H11.9385V16.52H13.6924V14.7253H13.6924ZM18.0771 14.7253H16.3232V16.52H18.0771V14.7253ZM12.1577 0.816406C13.9739 0.816406 15.4463 1.82052 15.4463 3.05965C15.4463 4.29879 13.9739 5.30333 12.1577 5.30333C11.7352 5.30457 11.3144 5.24808 10.9072 5.13541C10.4656 5.51293 9.81449 5.7519 9.08838 5.7519C8.52976 5.7519 8.01543 5.61071 7.60677 5.3735C7.32614 5.60545 6.9153 5.7519 6.45751 5.7519C5.60995 5.7519 4.92286 5.24938 4.92286 4.63027C4.92286 4.0107 5.60993 3.50866 6.45751 3.50866C6.55662 3.50866 6.65308 3.51523 6.7469 3.52838C7.00429 2.74439 7.95493 2.16253 9.08838 2.16253H9.14273C9.64966 1.37064 10.809 0.816406 12.1577 0.816406Z" fill="url(#paint0_linear_1579_5212)"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_dddi_1579_5212" x="-6.14648" y="-6.18359" width="35.293" height="42.293" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.380392 0 0 0 0 0.568627 0 0 0 0 1 0 0 0 0.28 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1579_5212"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.380392 0 0 0 0 0.568627 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1579_5212" result="effect2_dropShadow_1579_5212"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.709804 0 0 0 0 0.835294 0 0 0 0 0.972549 0 0 0 0.03 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_1579_5212" result="effect3_dropShadow_1579_5212"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_1579_5212" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.165999"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect4_innerShadow_1579_5212"/>
</filter>
<linearGradient id="paint0_linear_1579_5212" x1="2.73454" y1="6.33791" x2="21.0797" y2="6.33791" gradientUnits="userSpaceOnUse">
<stop stop-color="#EDFEFF"/>
<stop offset="1" stop-color="#9EC5F5"/>
</linearGradient>
</defs>
</svg>

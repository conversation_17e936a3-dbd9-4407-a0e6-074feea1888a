<template>
  <Modal ref="ModalRef" :title="props.title" @close="onModalClose">
    <template v-if="useTitleSlot" #title>
      <slot name="title" />
    </template>
    <slot name="content"></slot>
    <div class="media">
      <a-carousel v-model:current="currentIndex" show-arrow="never" indicator-type="never">
        <a-carousel-item v-for="file in fileList" :style="{ width: '60%' }">
          <img v-if="file.type === 'image'" :src="file.url" />
          <video v-else="file.type === 'video'" :src="file.url" controls autoplay muted />
        </a-carousel-item>
      </a-carousel>
      <IconPrev v-if="hasPrev" class="icon-prev" @click="onClickPrev" />
      <IconPrevDisabled v-else class="icon-prev is-disabled" />
      <IconNext v-if="hasNext" class="icon-next" @click="onClickNext" />
      <IconNextDisabled v-else class="icon-next is-disabled" />
    </div>
  </Modal>
</template>

<script setup>
import { ref, computed, nextTick, watch } from "vue";
import request from "@/utils/request";
import Modal from "../../traffic/components/Modal.vue";
import IconPrev from "@/assets/modules/dashiji/icon/icon-prev.svg?component";
import IconNext from "@/assets/modules/dashiji/icon/icon-next.svg?component";
import IconPrevDisabled from "@/assets/modules/dashiji/icon/icon-prev-disabled.svg?component";
import IconNextDisabled from "@/assets/modules/dashiji/icon/icon-next-disabled.svg?component";

const props = defineProps({
  title: String,
  useTitleSlot: Boolean,
});

const ModalRef = ref(null);
const fileList = ref([]);
const currentIndex = ref(0);

const fileListLength = computed(() => {
  return fileList.value.length;
});

const hasPrev = computed(() => {
  return currentIndex.value > 0;
});

const hasNext = computed(() => {
  return currentIndex.value < fileListLength.value - 1;
});

// 自动播放当前视频的函数
const autoPlayCurrentVideo = () => {
  const tryPlay = (attempt = 1) => {
    console.log(`尝试自动播放视频，第${attempt}次`);

    // 方法1: 查找当前激活的轮播项中的视频
    let activeVideo = document.querySelector(".media .arco-carousel-slide-active video");

    // 方法2: 如果没找到，尝试其他选择器
    if (!activeVideo) {
      activeVideo = document.querySelector(".media .arco-carousel-item-active video");
    }

    // 方法3: 查找所有视频，找到可见的
    if (!activeVideo) {
      const allVideos = document.querySelectorAll(".media video");
      for (let video of allVideos) {
        const rect = video.getBoundingClientRect();
        if (rect.width > 0 && rect.height > 0) {
          activeVideo = video;
          break;
        }
      }
    }

    if (activeVideo) {
      console.log("找到视频元素，尝试播放");
      // 确保视频是静音的，这样更容易自动播放
      activeVideo.muted = true;
      // 重置视频到开始位置
      activeVideo.currentTime = 0;
      // 尝试播放
      const playPromise = activeVideo.play();
      if (playPromise !== undefined) {
        playPromise.catch((err) => {
          console.log("自动播放失败:", err);
          // 如果自动播放失败，尝试用户交互后播放
          setTimeout(() => {
            activeVideo.play().catch((e) => console.log("延迟播放也失败:", e));
          }, 500);
        });
      }
    } else if (attempt < 5) {
      // 如果没找到视频，再尝试几次
      setTimeout(() => tryPlay(attempt + 1), 100);
    } else {
      console.log("未找到可播放的视频元素");
    }
  };

  setTimeout(() => tryPlay(), 200);
};

const onOpen = (data = [], index = 0) => {
  ModalRef.value?.open();
  fileList.value = data.reverse();
  currentIndex.value = index;

  // 延迟一下确保DOM已渲染，然后重置并自动播放视频
  nextTick(() => {
    // 先停止所有视频播放并重置
    const allVideos = document.querySelectorAll(".media video");
    allVideos.forEach((video) => {
      video.pause();
      video.currentTime = 0;
      video.load(); // 重新加载视频，这样autoplay属性会重新生效
    });

    // 然后自动播放当前视频
    autoPlayCurrentVideo();
  });
};

const onClickPrev = () => {
  currentIndex.value = currentIndex.value - 1;
};

const onClickNext = () => {
  currentIndex.value = currentIndex.value + 1;
};

const onModalClose = () => {
  // 停止所有视频播放
  const videoElements = document.querySelectorAll(".media video");
  if (videoElements && videoElements.length > 0) {
    videoElements.forEach((video) => {
      if (video && !video.paused) {
        video.pause();
        video.currentTime = 0;
      }
    });
  }
};

// 监听currentIndex变化，切换视频时也自动播放
watch(currentIndex, () => {
  nextTick(() => {
    // 先停止所有视频
    const allVideos = document.querySelectorAll(".media video");
    allVideos.forEach((video) => {
      video.pause();
      video.currentTime = 0;
    });

    // 然后自动播放当前视频
    autoPlayCurrentVideo();
  });
});

defineExpose({
  open: onOpen,
});
</script>

<style lang="scss" scoped>
.top {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .tag {
    padding: 4px 8px;
    background: rgba(1, 119, 251, 0.5);
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #0177fb;
    font-family: Alibaba PuHuiTi;
    font-size: 16px;
    color: #ffffff;
    -webkit-text-fill-color: initial;
  }
}

.content {
  font-family: Alibaba PuHuiTi;
  font-size: 18px;
  color: #ffffff;
}

.media {
  flex: 1;
  position: relative;

  :deep(.arco-carousel) {
    margin-left: 10%;
    width: 80%;
    height: 100%;

    .arco-carousel-slide {
      > div {
        width: 100% !important;
        display: flex;
        align-items: center;

        img {
          width: 100%;
        }

        video {
          width: 100%;
        }
      }
    }
  }

  .icon-prev,
  .icon-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;

    &.is-disabled {
      cursor: not-allowed;
    }
  }

  .icon-prev {
    left: 0;
  }

  .icon-next {
    right: 0;
  }
}
</style>

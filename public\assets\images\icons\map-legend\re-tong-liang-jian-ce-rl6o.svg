<svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#231;&#131;&#173;&#233;&#128;&#154;&#233;&#135;&#143;&#231;&#155;&#145;&#230;&#181;&#139;">
<g id="Ellipse 15" filter="url(#filter0_d_342_3839)">
<circle cx="10.9651" cy="10.8301" r="8" fill="url(#paint0_radial_342_3839)" fill-opacity="0.7"/>
<circle cx="10.9651" cy="10.8301" r="7.55492" stroke="url(#paint1_linear_342_3839)" stroke-width="0.890162"/>
</g>
<circle id="Ellipse 16" cx="10.965" cy="10.8299" r="7.01097" fill="url(#paint2_radial_342_3839)" stroke="url(#paint3_linear_342_3839)" stroke-width="0.200286"/>
<g id="Group 1321315401" filter="url(#filter1_d_342_3839)">
<g id="Frame" clip-path="url(#clip0_342_3839)">
<path id="Vector" d="M11.7452 10.7809C11.7246 10.7549 11.6529 10.6885 11.5514 10.5688C11.5461 10.5634 11.4949 10.5169 11.4698 10.4925C11.3091 10.3217 11.2196 10.0961 11.2196 9.86162C11.2184 9.72189 11.2495 9.58379 11.3105 9.45808C11.3715 9.33237 11.4608 9.22245 11.5712 9.13691C11.5918 9.12089 11.6681 9.06062 11.7909 8.95611C11.8599 8.89654 11.9031 8.81265 11.9116 8.72196C11.9202 8.63126 11.8933 8.54078 11.8367 8.46941C11.7782 8.39586 11.6935 8.3478 11.6004 8.33529C11.5072 8.32278 11.4129 8.34678 11.337 8.40228C11.189 8.50603 11.1021 8.5785 11.0616 8.60901C10.8876 8.76033 10.7469 8.94626 10.6486 9.15494C10.5504 9.36363 10.4967 9.59049 10.4909 9.82108C10.4852 10.0517 10.5275 10.2809 10.6153 10.4942C10.703 10.7076 10.8343 10.9003 11.0006 11.0601C11.0258 11.0807 11.2661 11.3446 11.2859 11.3805C11.3903 11.5647 11.4276 11.7794 11.3914 11.9881C11.3553 12.1967 11.2479 12.3864 11.0876 12.5247C11.0315 12.5709 10.9718 12.6125 10.9091 12.6491C10.8317 12.696 10.7751 12.7706 10.7508 12.8577C10.7264 12.9449 10.7361 13.038 10.7779 13.1182C10.7793 13.1259 10.7827 13.133 10.7878 13.1388V13.1426C10.8125 13.1846 10.8453 13.2212 10.8844 13.2502C10.9234 13.2793 10.9679 13.3003 11.0151 13.3119C11.0624 13.3236 11.1115 13.3257 11.1596 13.3181C11.2076 13.3105 11.2537 13.2934 11.2951 13.2678C11.4133 13.1902 11.5242 13.1018 11.6262 13.0038C11.7822 12.851 11.9059 12.6684 11.99 12.4668C12.0741 12.2653 12.1169 12.0489 12.1159 11.8305C12.1157 11.4483 11.9842 11.0778 11.7436 10.7809H11.7452ZM13.6576 10.7809C13.6378 10.7549 13.5661 10.6885 13.4639 10.5688C13.4593 10.5634 13.4082 10.5169 13.3822 10.4925C13.2215 10.3218 13.132 10.0961 13.132 9.86162C13.1306 9.72196 13.1617 9.58389 13.2227 9.45827C13.2837 9.33264 13.3731 9.2229 13.4837 9.13767C13.5043 9.12165 13.5806 9.06138 13.7026 8.95687C13.7726 8.89803 13.817 8.81428 13.8264 8.72332C13.8358 8.63237 13.8095 8.54132 13.753 8.46941C13.6944 8.39581 13.6095 8.34773 13.5163 8.33522C13.423 8.32271 13.3285 8.34674 13.2526 8.40228C13.1053 8.50603 13.0184 8.5785 12.9779 8.60901C12.8037 8.76018 12.6629 8.94596 12.5645 9.15452C12.466 9.36309 12.4121 9.58986 12.406 9.82042C12.4 10.051 12.442 10.2803 12.5295 10.4937C12.6169 10.7071 12.7478 10.9 12.9139 11.0601C12.9398 11.0807 13.1793 11.3446 13.1999 11.3805C13.3035 11.565 13.3403 11.7796 13.304 11.988C13.2677 12.1965 13.1606 12.3861 13.0008 12.5247C12.9447 12.5709 12.885 12.6125 12.8223 12.6491C12.7439 12.695 12.686 12.7692 12.6605 12.8564C12.635 12.9436 12.6437 13.0373 12.685 13.1182C12.6864 13.1259 12.6898 13.133 12.6949 13.1388V13.1434C12.7198 13.1852 12.7527 13.2216 12.7918 13.2506C12.8308 13.2795 12.8753 13.3004 12.9225 13.3119C12.9697 13.3235 13.0188 13.3256 13.0668 13.318C13.1148 13.3104 13.1609 13.2933 13.2022 13.2678C13.3205 13.1902 13.4313 13.1018 13.5333 13.0038C13.6892 12.8509 13.813 12.6683 13.8972 12.4668C13.9815 12.2653 14.0245 12.0489 14.0238 11.8305C14.0253 11.4491 13.8961 11.0786 13.6576 10.7809ZM9.88991 10.7809C9.86931 10.7549 9.80294 10.6885 9.69614 10.5688C9.6908 10.5634 9.63969 10.5169 9.61452 10.4925C9.45415 10.3215 9.36472 10.096 9.3643 9.86162C9.36323 9.72194 9.39449 9.58391 9.45563 9.45831C9.51677 9.33272 9.60614 9.22298 9.71674 9.13767C9.73657 9.12165 9.81362 9.06138 9.93568 8.95687C10.0046 8.89731 10.0478 8.81341 10.0564 8.72272C10.0649 8.63203 10.0381 8.54155 9.98145 8.47017C9.92304 8.39629 9.83826 8.34794 9.74493 8.33528C9.6516 8.32262 9.557 8.34663 9.48102 8.40228C9.33303 8.50603 9.24606 8.5785 9.20563 8.60901C9.03154 8.76033 8.89088 8.94626 8.79262 9.15494C8.69436 9.36363 8.64065 9.59049 8.6349 9.82108C8.62916 10.0517 8.67152 10.2809 8.75927 10.4942C8.84703 10.7076 8.97826 10.9003 9.1446 11.0601C9.16977 11.0807 9.41007 11.3446 9.42991 11.3805C9.53421 11.5648 9.57137 11.7796 9.53507 11.9882C9.49876 12.1969 9.39123 12.3865 9.2308 12.5247C9.17485 12.5711 9.11514 12.6127 9.0523 12.6491C8.97443 12.6959 8.91735 12.7707 8.89268 12.8581C8.86802 12.9456 8.87763 13.0391 8.91956 13.1198C8.92126 13.1274 8.92494 13.1345 8.93024 13.1404V13.1442C8.95502 13.186 8.98789 13.2225 9.02694 13.2515C9.06598 13.2805 9.11042 13.3015 9.15765 13.3131C9.20487 13.3248 9.25395 13.3269 9.302 13.3194C9.35006 13.3118 9.39613 13.2948 9.43754 13.2693C9.5559 13.1915 9.66696 13.1032 9.76938 13.0053C9.92537 12.8525 10.0491 12.6699 10.1332 12.4684C10.2173 12.2668 10.2602 12.0505 10.2591 11.8321C10.2592 11.4498 10.1278 11.0791 9.88686 10.7824L9.88991 10.7809ZM6.81104 7.03906H9.70987V7.80191H6.81104V7.03906ZM6.81104 8.71734H8.18417V9.48019H6.81104V8.71734ZM6.81104 10.3956H8.18417V11.1585H6.81104V10.3956ZM6.81104 12.0739H8.18417V12.8367H6.81104V12.0739ZM6.81104 13.7522H8.64188V14.515H6.81104V13.7522ZM12.9139 7.03906H15.8127V7.80191H12.9139V7.03906ZM14.4396 8.71734H15.8127V9.48019H14.4396V8.71734ZM14.4396 10.3956H15.8127V11.1585H14.4396V10.3956ZM14.4396 12.0739H15.8127V12.8367H14.4396V12.0739ZM13.9818 13.7522H15.8127V14.515H13.9818V13.7522Z" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_342_3839" x="0.294602" y="0.159592" width="21.341" height="21.341" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.33524"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.388235 0 0 0 0 0.640523 0 0 0 0 0.807843 0 0 0 0.51 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_342_3839"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3839" result="shape"/>
</filter>
<filter id="filter1_d_342_3839" x="5.96521" y="5.83398" width="10" height="10.4451" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.445081"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.150327 0 0 0 0 0.247059 0 0 0 0 0.145098 0 0 0 0.75 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_342_3839"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3839" result="shape"/>
</filter>
<radialGradient id="paint0_radial_342_3839" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(10.9651 10.8301) scale(8)">
<stop stop-color="#D3D3D3"/>
<stop offset="0.03" stop-color="#D3D3D3" stop-opacity="0.9216"/>
<stop offset="0.13" stop-color="#D3D3D3" stop-opacity="0.7056"/>
<stop offset="0.23" stop-color="#D3D3D3" stop-opacity="0.5329"/>
<stop offset="0.35" stop-color="#D3D3D3" stop-opacity="0.4225"/>
<stop offset="0.47" stop-color="#D3D3D3" stop-opacity="0.3364"/>
<stop offset="0.6" stop-color="#D3D3D3" stop-opacity="0.2916"/>
<stop offset="0.76" stop-color="#D3D3D3" stop-opacity="0.2601"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.5"/>
</radialGradient>
<linearGradient id="paint1_linear_342_3839" x1="10.9651" y1="18.8301" x2="10.9651" y2="2.83008" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<radialGradient id="paint2_radial_342_3839" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(10.965 10.8299) scale(7.11111)">
<stop stop-color="#D3D3D3"/>
<stop offset="1" stop-color="#0B103B" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint3_linear_342_3839" x1="10.965" y1="17.941" x2="10.965" y2="3.71875" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<clipPath id="clip0_342_3839">
<rect width="10" height="10" fill="white" transform="translate(5.96521 5.83398)"/>
</clipPath>
</defs>
</svg>

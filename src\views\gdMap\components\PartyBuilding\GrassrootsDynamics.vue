<template>
	<BasicCard title="基层动态">
		<div class="grassroots-dynamics">
			<div class="card-content">
				<div class="pic">
					<div class="pic-item">
						<img src="@/assets/modules/PartyBuilding/bg/normal_u191_3.webp" />
					</div>
					<div class="pic-item">
						<img src="@/assets/modules/PartyBuilding/bg/normal_u191_4.webp" />
					</div>
				</div>

				<div class="news">
					<div v-for="item in data" :key="item.id" class="news-item" @click="onDetail(item)">
						<div class="news-item-time">「{{ item.publishDate }}」</div>
						<div class="news-item-desc">
							{{ item.title }}
						</div>
					</div>
					<a-empty v-if="data.length === 0" />
				</div>
			</div>
		</div>
		<template #extra>
			<div class="link" @click="onInfo">查看详情 <icon-double-right /></div>
		</template>
	</BasicCard>
	<GrassrootsDynamicsModal ref="GrassrootsDynamicsModalRef" />
	<ParagraphModal ref="ParagraphModalRef" />
</template>

<script setup>
import { usePartyBuildingStore } from "@/store";
import request from "@/utils/request";
import GrassrootsDynamicsModal from "./components/GrassrootsDynamicsModal.vue";
import ParagraphModal from "./components/ParagraphModal.vue";

const { proxy } = getCurrentInstance();
const PartyBuildingStore = usePartyBuildingStore();
const data = ref([]);

onMounted(() => {
	getData();
});

watch(
	() => PartyBuildingStore.getDepartment,
	() => {
		getData();
	}
);

const getData = () => {
	const params = {
		type: "基层动态",
		department: PartyBuildingStore.getDepartment,
	};
	request.get("/api/screen/dangjian/article/list", params).then((res) => {
		if (res.code == 200) {
			data.value = res.data;
		}
	});
};

const onInfo = () => {
	proxy.$refs.GrassrootsDynamicsModalRef.open();
};

const onDetail = (item) => {
	proxy.$refs.ParagraphModalRef.open(item.id);
};
</script>

<style lang="scss" scoped>
.grassroots-dynamics {
	height: 250px;
	width: 100%;
}

.card-content {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
	overflow: hidden;
}

.pic {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	padding: 4px 12px;

	&-item {
		width: 200px;
		height: 134px;
		border: 2px solid;
		border-image: linear-gradient(180deg, rgba(255, 180, 44, 0), rgba(255, 180, 44, 1)) 2 2;

		> img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}
}

.news {
	display: flex;
	flex-direction: column;
	row-gap: 8px;

	&-item {
		height: 28px;
		border: 1px solid;
		border-image: linear-gradient(180deg, rgba(255, 64, 44, 0), rgba(255, 64, 44, 1)) 1 1;
		display: flex;
		flex-direction: row;
		align-items: center;
		padding-left: 8px;
		padding-right: 32px;
		cursor: pointer;

		&-time {
			font-family: Alibaba PuHuiTi;
			font-size: 12px;
			color: #fff9de;
			text-shadow: 0px 0px 8px #ffde00;
			white-space: nowrap;
		}

		&-desc {
			margin-left: 20px;
			font-family: Alibaba PuHuiTi;
			font-size: 12px;
			color: #ffffff;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
	}
}

.link {
	font-family: Alibaba PuHuiTi;
	font-size: 14px;
	color: #ffffff;
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-top: auto;
	margin-bottom: 13px;
	cursor: pointer;
}

:deep(.arco-empty) {
	.arco-empty-image,
	.arco-empty-description {
		color: #fdcb01;
	}
}
</style>

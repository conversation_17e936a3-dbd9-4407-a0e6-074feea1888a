<svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#229;&#136;&#134;&#229;&#140;&#133;&#230;&#156;&#186;&#230;&#162;&#176;">
<g id="Ellipse 15" filter="url(#filter0_d_342_3944)">
<circle cx="10.9651" cy="10.6943" r="8" fill="url(#paint0_radial_342_3944)" fill-opacity="0.7"/>
<circle cx="10.9651" cy="10.6943" r="7.55492" stroke="url(#paint1_linear_342_3944)" stroke-width="0.890162"/>
</g>
<circle id="Ellipse 16" cx="10.9651" cy="10.6941" r="7.01097" fill="url(#paint2_radial_342_3944)" stroke="url(#paint3_linear_342_3944)" stroke-width="0.200286"/>
<g id="Group 1321315401" filter="url(#filter1_d_342_3944)">
<g id="Frame" clip-path="url(#clip0_342_3944)">
<path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M15.5012 9.08976C15.5258 9.08993 15.5501 9.09494 15.5728 9.10451C15.5954 9.11409 15.616 9.12804 15.6332 9.14556C15.6505 9.16308 15.6641 9.18383 15.6734 9.20662C15.6826 9.22941 15.6873 9.2538 15.6871 9.27839C15.6869 9.30299 15.6819 9.3273 15.6723 9.34995C15.6262 9.4593 15.5489 9.55261 15.45 9.61816C15.3511 9.6837 15.235 9.71857 15.1163 9.71837C15.0982 9.71837 15.0795 9.71749 15.0608 9.71574C14.9074 9.70161 14.7652 9.62927 14.6634 9.51358C14.5616 9.39789 14.508 9.24762 14.5136 9.09365C14.5215 8.86818 14.6504 8.6899 14.7934 8.51212C14.8814 8.40184 14.9293 8.26486 14.9292 8.12373V7.08878L12.1562 10.2292C12.1233 10.2664 12.083 10.2961 12.0378 10.3165C11.9926 10.3368 11.9436 10.3474 11.894 10.3473H11.1524C11.0597 10.3473 10.9708 10.3105 10.9052 10.2449C10.8397 10.1794 10.8029 10.0905 10.8029 9.99777V9.39003C10.8029 9.33579 10.8155 9.2823 10.8397 9.23378C10.864 9.18527 10.8992 9.14306 10.9426 9.1105L14.7367 6.26144C14.804 6.21089 14.8873 6.18634 14.9713 6.19228C15.0552 6.19822 15.1342 6.23426 15.1937 6.29377C15.2266 6.3266 15.2527 6.36568 15.2702 6.40871C15.2878 6.45174 15.2966 6.49785 15.2961 6.54433C15.3011 6.5614 15.3037 6.5791 15.3037 6.59689V8.12373C15.3038 8.3503 15.2268 8.57016 15.0853 8.74708C14.9628 8.89939 14.8914 9.00725 14.8879 9.10713C14.886 9.16059 14.9029 9.21302 14.9356 9.25531C14.9684 9.29761 15.0149 9.3271 15.0671 9.33866C15.1194 9.35021 15.174 9.34311 15.2215 9.31858C15.269 9.29405 15.3065 9.25365 15.3273 9.20438C15.3369 9.1817 15.3508 9.16112 15.3683 9.14382C15.3858 9.12653 15.4066 9.11285 15.4293 9.10357C15.4521 9.0943 15.4765 9.0896 15.5012 9.08976ZM11.035 10.7471H15.4828C15.6108 10.7471 15.7335 10.798 15.824 10.8885C15.9145 10.979 15.9653 11.1017 15.9653 11.2297V12.8533C15.9653 12.9812 15.9145 13.104 15.824 13.1945C15.7335 13.2849 15.6108 13.3358 15.4828 13.3358H15.0666C15.0199 12.9785 14.8449 12.6504 14.5741 12.4127C14.3034 12.1749 13.9554 12.0439 13.595 12.0439C13.2347 12.0439 12.8867 12.1749 12.6159 12.4127C12.3452 12.6504 12.1701 12.9785 12.1235 13.3358H9.78568C9.73904 12.9785 9.56401 12.6504 9.29324 12.4127C9.02247 12.1749 8.67446 12.0439 8.31415 12.0439C7.95383 12.0439 7.60582 12.1749 7.33505 12.4127C7.06428 12.6504 6.88925 12.9785 6.84261 13.3358H6.44785C6.31988 13.3358 6.19715 13.2849 6.10666 13.1945C6.01617 13.104 5.96533 12.9812 5.96533 12.8533V11.1449C5.96539 10.8835 6.02575 10.6256 6.14174 10.3913L7.22888 8.20508C7.3521 7.97374 7.58905 7.9429 7.72001 7.9429H9.8898C9.95317 7.94289 10.0159 7.95536 10.0745 7.9796C10.133 8.00384 10.1862 8.03938 10.231 8.08419C10.2758 8.129 10.3114 8.1822 10.3356 8.24075C10.3599 8.2993 10.3723 8.36205 10.3723 8.42543V10.0842C10.3723 10.26 10.4421 10.4286 10.5664 10.5529C10.6907 10.6772 10.8592 10.7471 11.035 10.7471ZM9.79171 10.4025C9.84764 10.3466 9.87906 10.2707 9.87906 10.1916L9.87894 8.76263C9.87894 8.68353 9.84752 8.60767 9.79158 8.55173C9.73565 8.4958 9.65979 8.46438 9.58069 8.46438H7.89529C7.83958 8.46436 7.78497 8.47996 7.73766 8.50939C7.69035 8.53883 7.65223 8.58093 7.62763 8.63092L6.92488 10.06C6.90253 10.1055 6.89212 10.1559 6.89466 10.2065C6.89719 10.2571 6.91258 10.3062 6.93936 10.3492C6.96614 10.3923 7.00345 10.4277 7.04774 10.4523C7.09204 10.4769 7.14188 10.4898 7.19255 10.4898H9.58081C9.65991 10.4898 9.73577 10.4584 9.79171 10.4025ZM12.548 13.5292C12.548 13.2515 12.6583 12.9851 12.8547 12.7887C13.051 12.5924 13.3174 12.482 13.5951 12.482C13.8728 12.482 14.1392 12.5924 14.3356 12.7887C14.532 12.9851 14.6423 13.2515 14.6423 13.5292C14.6423 13.8069 14.532 14.0733 14.3356 14.2697C14.1392 14.466 13.8728 14.5764 13.5951 14.5764C13.3174 14.5764 13.051 14.466 12.8547 14.2697C12.6583 14.0733 12.548 13.8069 12.548 13.5292ZM7.26727 13.5292C7.26727 13.6667 7.29435 13.8029 7.34698 13.9299C7.3996 14.057 7.47674 14.1724 7.57398 14.2697C7.67121 14.3669 7.78665 14.444 7.9137 14.4966C8.04075 14.5493 8.17692 14.5764 8.31443 14.5764C8.45195 14.5764 8.58812 14.5493 8.71516 14.4966C8.84221 14.444 8.95765 14.3669 9.05489 14.2697C9.15213 14.1724 9.22926 14.057 9.28188 13.9299C9.33451 13.8029 9.36159 13.6667 9.36159 13.5292C9.36159 13.3917 9.33451 13.2555 9.28188 13.1285C9.22926 13.0014 9.15213 12.886 9.05489 12.7887C8.95765 12.6915 8.84221 12.6144 8.71516 12.5617C8.58812 12.5091 8.45195 12.482 8.31443 12.482C8.17692 12.482 8.04075 12.5091 7.9137 12.5617C7.78665 12.6144 7.67121 12.6915 7.57398 12.7887C7.47674 12.886 7.3996 13.0014 7.34698 13.1285C7.29435 13.2555 7.26727 13.3917 7.26727 13.5292Z" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_342_3944" x="0.294602" y="0.02385" width="21.341" height="21.341" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.33524"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.388235 0 0 0 0 0.640523 0 0 0 0 0.807843 0 0 0 0.51 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_342_3944"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3944" result="shape"/>
</filter>
<filter id="filter1_d_342_3944" x="5.96545" y="5.19727" width="10" height="10.4451" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.445081"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.150327 0 0 0 0 0.247059 0 0 0 0 0.145098 0 0 0 0.75 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_342_3944"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3944" result="shape"/>
</filter>
<radialGradient id="paint0_radial_342_3944" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(10.9651 10.6943) scale(8)">
<stop stop-color="#D3D3D3"/>
<stop offset="0.03" stop-color="#D3D3D3" stop-opacity="0.9216"/>
<stop offset="0.13" stop-color="#D3D3D3" stop-opacity="0.7056"/>
<stop offset="0.23" stop-color="#D3D3D3" stop-opacity="0.5329"/>
<stop offset="0.35" stop-color="#D3D3D3" stop-opacity="0.4225"/>
<stop offset="0.47" stop-color="#D3D3D3" stop-opacity="0.3364"/>
<stop offset="0.6" stop-color="#D3D3D3" stop-opacity="0.2916"/>
<stop offset="0.76" stop-color="#D3D3D3" stop-opacity="0.2601"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.5"/>
</radialGradient>
<linearGradient id="paint1_linear_342_3944" x1="10.9651" y1="18.6943" x2="10.9651" y2="2.69434" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<radialGradient id="paint2_radial_342_3944" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(10.9651 10.6941) scale(7.11111)">
<stop stop-color="#D3D3D3"/>
<stop offset="1" stop-color="#0B103B" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint3_linear_342_3944" x1="10.9651" y1="17.8052" x2="10.9651" y2="3.58301" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<clipPath id="clip0_342_3944">
<rect width="10" height="10" fill="white" transform="translate(5.96545 5.19727)"/>
</clipPath>
</defs>
</svg>

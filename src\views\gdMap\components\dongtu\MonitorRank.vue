<template>
  <div class="left-card">
    <m-card title="监测排行TOP5" :height="355" :isRight="true">
      <div class="monitor-rank">
        <div class="rank-item" v-for="(item, index) in listData" :key="index">
          <div class="ranking">
            <template v-if="index < 3">
              <img :src="ranking[index]" alt="" />
            </template>
            <div v-else class="medal">{{ index + 1 }}</div>
          </div>
          <div class="r-content">
            <div class="r-name">{{ item.name }}</div>
            <div class="r-bar">
              <div class="r-bar-inner" :style="{ width: item.value }"></div>
            </div>
          </div>
          <div class="r-value">
            <span class="r-num">{{ item.value }}</span>
            <span class="r-unit">{{ item.unit }}</span>
          </div>
        </div>
      </div>
    </m-card>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import mCard from '@/components/mCard/index.vue';
import medal1 from '@/assets/images/baotong/medal-1.png';
import medal2 from '@/assets/images/baotong/medal-2.png';
import medal3 from '@/assets/images/baotong/medal-3.png';
import { getMonitorRank } from '@/views/gdMap/services/dongtu.mock.js';

const ranking = ref([medal1, medal2, medal3]);

const listData = ref();
onMounted(async () => {
  const defaultData = await getMonitorRank();
  listData.value = defaultData.listData;
});
</script>
<style lang="scss">
.monitor-rank {
  color: #fff;
  height: 100%;
  box-sizing: border-box;
  padding: 28px 0 18px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .rank-item {
    display: flex;
    padding: 0 10px;
    margin-bottom: 10px;
    align-items: center;
  }
  .ranking {
    width: 24px;
    height: 24px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .medal {
    position: relative;
    background: #b2b2b2;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: rgba(123, 124, 124, 1);
    &::before {
      content: '';
      position: absolute;
      top: 2px;
      left: 2px;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      border: 1px solid rgba(123, 124, 124, 1);
    }
  }
  .r-content {
    flex: 1;
    .r-name {
      font-size: 12px;
      line-height: 14px;
      margin-bottom: 6px;
    }
    .r-bar {
      border-radius: 3px;
      width: 100%;
      height: 6px;
      background: rgba(7, 131, 250, 0.2);
    }
    .r-bar-inner {
      border-radius: 3px;
      height: 100%;
      background: linear-gradient(270deg, #51e0ff 0%, #0085ff 100%);
    }
  }
  .r-value {
    margin-left: 8px;
    margin-top: 10px;
    width: 72px;
    text-align: right;
    .r-num {
      font-size: 20px;
      color: #48d6ff;
      margin-right: 2px;
      font-family: 'DDINPRO-Bold';
    }
    .r-unit {
      font-size: 12px;
      color: #bfd1ff;
    }
  }
}
</style>

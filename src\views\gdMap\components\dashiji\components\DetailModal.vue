<template>
	<MediaPlayer ref="ModalRef" use-title-slot>
		<template #title>
			<div class="top">
				<div>{{ detail.publishDate }}</div>
				<div class="tag">{{ detail.tags }}</div>
			</div>
		</template>
		<template #content>
			<div class="content">
				{{ detail.content }}
			</div>
		</template>
	</MediaPlayer>
</template>

<script setup>
import { cloneDeep } from "lodash-es";
import MediaPlayer from "@/views/gdMap/components/oneMap/components/MediaPlayer.vue";

const ModalRef = ref(null);
const detail = ref({});

const processData = async (data) => {
	const fileList = cloneDeep(data) || [];
	const stack = fileList.map(async (file) =>
		filePreUrl(file).then((res) => ({ ...file, url: res.data }))
	);
	return await Promise.all(stack);
};

const filePreUrl = (path) => {
	return request.get("/api/upload-histories/file/download/preurl", path);
};

const onOpen = (data) => {
	detail.value = data;
	const fileList = data.fileList ? JSON.parse(data.fileList) : [];
	ModalRef.value?.open(processData(fileList));
};

defineExpose({
	open: onOpen,
});
</script>

<style lang="scss" scoped>
.top {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;

	.tag {
		padding: 4px 8px;
		background: rgba(1, 119, 251, 0.5);
		border-radius: 2px 2px 2px 2px;
		border: 1px solid #0177fb;
		font-family: Alibaba PuHuiTi;
		font-size: 16px;
		color: #ffffff;
		-webkit-text-fill-color: initial;
	}
}

.content {
	font-family: Alibaba PuHuiTi;
	font-size: 18px;
	color: #ffffff;
}
</style>

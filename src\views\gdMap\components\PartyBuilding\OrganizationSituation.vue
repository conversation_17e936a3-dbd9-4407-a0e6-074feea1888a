<template>
	<BasicCard title="组织生活情况">
		<div class="organization-situation">
			<div class="situation-chart">
				<SituationChart :data="data" />
			</div>
			<div class="situation-select">
				<a-select
					v-model="state.year"
					:trigger-props="{
						'content-class': 'organization-situation-select-trigger',
						'auto-fit-position': false,
					}"
					@change="getData"
				>
					<a-option
						v-for="year in yearList"
						:key="year.value"
						:label="year.label"
						:value="year.value"
					/>
					<template #arrow-icon>
						<icon-caret-down />
					</template>
				</a-select>
				<a-select
					v-model="state.quarter"
					:trigger-props="{
						'content-class': 'organization-situation-select-trigger',
						'auto-fit-position': false,
					}"
					style="width: 72px"
					@change="onChangeQuarter"
				>
					<a-option
						v-for="quarter in quarterList"
						:key="quarter.value"
						:label="quarter.label"
						:value="quarter.value"
					/>
					<template #arrow-icon>
						<icon-caret-down />
					</template>
				</a-select>
				<a-select
					v-model="state.month"
					:trigger-props="{
						'content-class': 'organization-situation-select-trigger',
						'auto-fit-position': false,
					}"
					style="width: 64px"
					@change="getData"
				>
					<a-option
						v-for="month in monthList"
						:key="month.value"
						:label="month.label"
						:value="month.value"
						:disabled="isDisabledMonth(month)"
					/>
					<template #arrow-icon>
						<icon-caret-down />
					</template>
				</a-select>
			</div>
		</div>
	</BasicCard>
</template>

<script setup>
import { usePartyBuildingStore } from "@/store";
import request from "@/utils/request";
import SituationChart from "./components/SituationChart.vue";

const PartyBuildingStore = usePartyBuildingStore();
const yearList = [{ label: "2025年", value: 2025 }];
const quarterList = [
	{ label: "全部", value: "" },
	{ label: "Q1", value: "Q1" },
	{ label: "Q2", value: "Q2" },
	{ label: "Q3", value: "Q3" },
	{ label: "Q4", value: "Q4" },
];
const monthList = [
	{ label: "全部", value: "" },
	...Array.from({ length: 12 }, (_, i) => ({
		label: `${i + 1}月`,
		value: (i + 1 > 9 ? "" : "0") + (i + 1),
	})),
];
const state = ref({
	year: 2025,
	quarter: "",
	month: "",
});
const data = ref([]);

watch(
	() => PartyBuildingStore.getDepartment,
	() => getData()
);

onMounted(() => {
	getData();
});

const onChangeQuarter = () => {
	state.value.month = "";
	getData();
};

const isDisabledMonth = (month) => {
	const quarter = state.value.quarter[1];
	if (quarter) {
		const _month = month.value * 1;
		return !(_month > (quarter - 1) * 3 && _month <= quarter * 3);
	}
	return false;
};

const getData = () => {
	const params = {
		department: PartyBuildingStore.getDepartment,
		...unref(state),
	};
	request.get("/api/screen/dangjian/stat/meeting", params).then((res) => {
		if (res && res.code == 200) {
			data.value = res.data.map(({ name, count, intExt }) => ({
				name,
				value1: count,
				value2: intExt,
			}));
		}
	});
};
</script>

<style lang="scss" scoped>
.basic-card {
	--card-header-bg: url("@/assets/modules/PartyBuilding/bg/card-title-bg-long.webp");
}

.organization-situation {
	height: 208px;
	width: 100%;
	position: relative;
}

.situation-chart {
	width: 100%;
	height: 100%;
}

.situation-select {
	position: absolute;
	top: 12px;
	right: 16px;
	display: flex;
	flex-direction: row;
	column-gap: 12px;

	:deep(.arco-select) {
		width: auto;
		padding: 2px 8px 2px 10px;
		border-radius: 4px 4px 4px 4px;
		border: 1px solid #ffb42c;

		.arco-select-view-value {
			font-family: "Alibaba PuHuiTi";
			font-size: 12px;
			padding: 0;
			min-height: unset;
		}

		.arco-select-view-suffix {
			padding-left: 8px;
			color: #e5c507;
		}

		&.arco-select-view-focus {
			background-color: transparent;
		}
	}
}
</style>

<style>
.organization-situation-select-trigger {
	.arco-select-dropdown {
		/* background-color: transparent; */
		.arco-select-dropdown-list-wrapper {
			max-height: 120px;
		}

		/* .arco-select-option {
			background-color: transparent;
		} */
	}
}
</style>

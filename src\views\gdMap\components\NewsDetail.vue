<template>
  <div class="news-detail" v-if="msg">
    <div :class="['detail-card', cardColorClass]">
      <div class="detail-title">
        <div class="detail-title-text">{{ msg.title }}</div>
        <img src="@/assets/images/people/x.svg" alt="" @click="close">
      </div>
      <div class="detail-content">{{ msg.content }}</div>
      <div class="detail-time">{{ msg.time }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps,defineEmits } from 'vue';
const emit = defineEmits(['close']);
const props = defineProps({
  msg: {
    type: Object,
    required: true,
    // { type: 'blue'|'yellow'|'orange'|'red', title: string, content: string, time: string }
  }
});

const cardColorClass = computed(() => {
  switch (props.msg.type) {
    case 'blue': return 'card-blue';
    case 'yellow': return 'card-yellow';
    case 'orange': return 'card-orange';
    case 'red': return 'card-red';
    default: return 'card-blue';
  }
});
function close() {
  emit('close');
}
</script>

<style scoped lang="scss">
.news-detail {
  position: fixed;
  top: 80px;
  right: 16px;
  z-index: 2000;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  opacity: 0.95;
}
.detail-card {
  width: 232px;
  border-radius: 8px;
  margin-bottom: 18px;
  box-shadow: 0 4px 16px 0 rgba(0,0,0,0.12);
//   color: #fff;
  font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
  border: 1px solid rgba(255,255,255,0.2);
  .detail-title {
    font-size: 12px;
    font-weight: bold;
    padding:8px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255,255,255,0.2);
    // color: #D9E5F5;
    img{
        pointer-events: all;
        cursor: pointer;
    }
  }
  .detail-content {
    font-size: 12px;
    padding:8px 12px;
    // color: #D9E5F5;
    line-height: 1.6;
    word-break: break-all;
    border-bottom: 1px solid rgba(255,255,255,0.2);
  }
  .detail-time {
    font-size: 10px;
    color: #FFFFFF;
    padding:8px 12px;
    text-align: left;
  }
}
.card-blue {
    background: linear-gradient( 180deg, #0042F7 0%, #0075F8 100%);
    color: #D9E5F5;
}
.card-yellow {
    background: linear-gradient( 180deg, #F7E200 0%, #C3B200 100%);
    color: #FDFFDB;
}
.card-orange {
    background: linear-gradient( 180deg, #F78000 0%, #F88000 100%);
    color: #F5E7D9;
}
.card-red {
    background: linear-gradient( 180deg, #F71D00 0%, #F81D00 100%);
    color: #F5DCD9;
}
</style>

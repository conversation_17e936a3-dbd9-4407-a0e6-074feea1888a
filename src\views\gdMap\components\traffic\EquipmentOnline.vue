<template>
  <TrafficCard title="设备在线情况">
    <div class="equipment-online">
      <div
        v-for="(item, index) in listData"
        :key="item.name"
        class="equipment-item"
        :class="`is-${index}`"
      >
        <div class="equipment-item-icon">
          <component :is="deviceTypeMap[item.deviceType].icon" />
        </div>
        <div class="equipment-item-content">
          <div class="equipment-item-header">
            <div class="equipment-item-name">{{ deviceTypeMap[item.deviceType].label }}</div>
            <div class="equipment-item-desc">
              <div class="equipment-item-online">
                <deviceOnline />
                <span> 在线</span>
                <span class="equipment-item-online-number">
                  {{ item.onlineNumber }}
                </span>
                <span>台</span>
              </div>
              <div class="equipment-item-total">
                <deviceTotal />
                <span> 总数 </span>
                <span class="equipment-item-total-number">{{ item.totalNumber }}</span>
                <span>台 </span>
              </div>
            </div>
          </div>
          <div class="equipment-item-progress">
            <div
              class="equipment-item-progress-bar"
              :style="{ '--percent': item.onlinePercent * 1 || 0 }"
            />
            <div class="equipment-item-progress-text">
              <span>在线率</span>
              <span class="equipment-item-progress-text-percent">
                {{ item.onlinePercent * 1 || 0 }}%
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #extra>
      <div class="link" @click="onInfo">设备台账详情 <icon-double-right /></div>
    </template>
  </TrafficCard>

  <EquipmentOnlineModal ref="EquipmentOnlineModalRef" :data="listData" />
</template>
<script setup>
import request from "@/utils/request";
import deviceLight from "@/assets/modules/traffic/icon/device-light.svg?component";
import deviceCamera from "@/assets/modules/traffic/icon/device-camera.svg?component";
import deviceAdjustment from "@/assets/modules/traffic/icon/device-adjustment.svg?component";
import deviceEdge from "@/assets/modules/traffic/icon/device-edge.svg?component";
import deviceOnline from "@/assets/modules/traffic/icon/device-online.svg?component";
import deviceTotal from "@/assets/modules/traffic/icon/device-total.svg?component";
import EquipmentOnlineModal from "./components/EquipmentOnlineModal.vue";
import emitter from "@/utils/emitter";

const deviceTypeMap = {
  7: { icon: deviceCamera, label: "摄像机" },
  12: { icon: deviceLight, label: "信号灯" },
  13: { icon: deviceAdjustment, label: "交调设备" },
  14: { icon: deviceEdge, label: "边缘计算" },
};
const listData = ref();
const EquipmentOnlineModalRef = ref(null);
const currentDepartmentId = ref(null);

onMounted(async () => {
  // 监听区域双击事件
  emitter.$on("division-area-dblclick", handleAreaChange);
  getData();
});

onBeforeUnmount(() => {
  emitter.$off("division-area-dblclick", handleAreaChange);
});

const handleAreaChange = (eventData) => {
  console.log("设备在线情况 - 接收到区域双击事件:", eventData);
  currentDepartmentId.value = eventData.properties.id;
  getData();
};

const getData = () => {
  const params = currentDepartmentId.value ? { departmentId: currentDepartmentId.value } : {};
  request.get("/api/screen/baotong/stat/device", params).then((res) => {
    if (res.code == 200) {
      listData.value = res.data;
    }
  });
};

const onInfo = () => {
  EquipmentOnlineModalRef.value.open();
};
</script>
<style lang="scss" scoped>
.equipment-online {
  height: 224px;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  padding: 12px;
}

.equipment-item {
  display: flex;

  & + .equipment-item {
    margin-top: 12px;
  }

  &.is-0 {
    --item-number-color: #008aff;
    --item-bar-color: linear-gradient(90deg, #001c47 0%, #008aff 100%);
  }

  &.is-1 {
    --item-number-color: #ffad29;
    --item-bar-color: linear-gradient(90deg, #845200 0%, #ffad29 100%);
  }

  &.is-2 {
    --item-number-color: #29ffc6;
    --item-bar-color: linear-gradient(90deg, #003728 0%, #29ffc6 100%);
  }

  &.is-3 {
    --item-number-color: #2950ff;
    --item-bar-color: linear-gradient(90deg, #00146c 0%, #2950ff 100%);
  }

  &-icon {
    width: 40px;
    height: 40px;
    background-image: url("@/assets/modules/traffic/icon/device-bg.svg");
    background-size: 100% 100%;
    position: relative;

    svg {
      position: absolute;
      left: 50%;
      bottom: 17px;
      transform: translateX(-50%);
      width: 22px;
      height: 22px;
    }
  }

  &-content {
    margin-left: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;
    row-gap: 4px;
  }

  &-header {
    display: flex;
  }

  &-name {
    width: 56px;
    height: 20px;
    box-sizing: border-box;
    background: linear-gradient(
        90deg,
        rgba(0, 138, 255, 0.42) 0%,
        rgba(0, 138, 255, 0.38) 14%,
        rgba(0, 138, 255, 0.24) 45%,
        rgba(0, 138, 255, 0) 100%
      )
      no-repeat;
    border: 1px solid;
    border-image: linear-gradient(90deg, rgba(0, 138, 255, 1), rgba(0, 138, 255, 0)) 1 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: Alibaba PuHuiTi;
    font-size: 12px;
    color: #ffffff;
  }

  &-desc {
    margin-left: 60px;
    flex: 1%;
    display: flex;
    align-items: center;
  }

  &-online,
  &-total {
    width: 50%;
    display: flex;
    align-items: center;
    font-family: Alibaba PuHuiTi;
    font-size: 12px;
    color: #ffffff;

    svg {
      margin-right: 4px;
      width: 12px;
      height: 12px;
    }

    &-number {
      padding: 0 4px;
      font-family: D-DIN-PRO;
      font-weight: bold;
      font-size: 18px;
    }
  }

  &-online {
    &-number {
      background-image: linear-gradient(180deg, #ffffff 0%, var(--item-number-color) 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  &-progress {
    height: 18px;
    box-sizing: border-box;
    border-top: 1px solid;
    border-bottom: 1px solid;
    border-image: linear-gradient(
        90deg,
        rgba(0, 138, 255, 0),
        rgba(0, 138, 255, 1),
        rgba(0, 138, 255, 1),
        rgba(0, 138, 255, 0)
      )
      1 1;
    display: flex;
    align-items: center;

    &-bar {
      width: 235px;
      height: 6px;
      background: #0f304e;
      position: relative;

      &::before {
        content: "";
        position: absolute;
        width: 100%;
        height: 100%;
        background-image: var(--item-bar-color);
        clip-path: polygon(0 0, var(--percent, 100%) 0, var(--percent, 100%) 100%, 0 100%);
      }

      &::after {
        content: "";
        position: absolute;
        top: 0;
        left: calc(var(--percent, 100%) - 3px);
        width: 6px;
        height: 6px;
        background: #ffffff;
        border-radius: 50%;
        box-shadow: 0px 0px 8px 2px rgba(0, 138, 255, 0.6);
      }
    }

    &-text {
      margin-left: 8px;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-family: Alibaba PuHuiTi;
      font-size: 12px;
      color: #ffffff;

      &-percent {
        font-family: D-DIN-PRO;
        font-weight: 500;
        font-size: 14px;
        color: #008aff;
      }
    }
  }
}

.link {
  display: flex;
  flex-direction: row;
  align-items: center;
  cursor: pointer;
}
</style>

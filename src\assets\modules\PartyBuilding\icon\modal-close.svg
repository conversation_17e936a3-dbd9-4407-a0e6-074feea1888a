<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#229;&#188;&#185;&#231;&#170;&#151;&#229;&#133;&#179;&#233;&#151;&#173;&#230;&#140;&#137;&#233;&#146;&#174;">
<g id="&#232;&#183;&#175;&#229;&#190;&#132; 20">
<g filter="url(#filter0_i_644_13242)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M30 30H6L0 24V0H24L30 6V30Z" fill="#FBED5D" fill-opacity="0.1"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M30 30H6L0 24V0H24L30 6V30Z" stroke="#FBED5D" stroke-width="0.2"/>
</g>
<g id="&#229;&#133;&#179;&#233;&#151;&#173;">
<rect id="&#231;&#159;&#169;&#229;&#189;&#162;" opacity="0.01" x="7" y="7" width="16" height="16" fill="white"/>
<path id="Vector 88" d="M9.6665 9.66675L20.3332 20.3334" stroke="white" stroke-width="2" stroke-linecap="square"/>
<path id="Vector 89" d="M9.6665 20.3333L20.3332 9.66658" stroke="white" stroke-width="2" stroke-linecap="square"/>
</g>
</g>
<defs>
<filter id="filter0_i_644_13242" x="-0.100098" y="-0.100098" width="30.2002" height="31.2002" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.984314 0 0 0 0 0.929412 0 0 0 0 0.364706 0 0 0 0.395366 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_644_13242"/>
</filter>
</defs>
</svg>

<template>
	<div class="map-legend">
		<div class="map-legend-btn" @click="toggleModal">
			<div class="map-legend-btn-icon">
				<img src="@/assets/modules/yizhangtu/icons/map-legend.svg" alt="地图图例" />
			</div>
			<div class="map-legend-btn-text">地图图例</div>
		</div>

		<transition @before-enter="beforeEnter" @enter="enter" @leave="leave">
			<div v-if="isModalVisible" class="map-legend-modal" :style="modalStyle" @click.stop>
				<div class="map-legend-modal-header">
					<div class="map-legend-modal-title">地图图例</div>
				</div>
				<div class="map-legend-modal-content">
					<!-- 动态生成图例分类 -->
					<div
						v-for="category in legendData.categories"
						:key="category.key"
						class="legend-collapse"
					>
						<div class="legend-collapse-header">
							<div
								class="collapse-icon"
								:class="{ 'is-active': collapseStates[category.key] }"
								@click.stop="toggleCollapse(category.key)"
							>
								<img src="@/assets/modules/yizhangtu/icons/collapse-down.svg" alt="展开/收起" />
							</div>
							<div class="collapse-title">{{ category.title }}</div>
							<div class="collapse-switch">
								<a-switch
									size="small"
									:model-value="visibleStates[category.key]"
									@change="(val) => toggleVisible(category.key, val)"
									:disabled="category.disabled"
								/>
							</div>
						</div>
						<div
							class="legend-collapse-content"
							:class="`col-${category.colNum}`"
							v-show="collapseStates[category.key]"
						>
							<div
								v-for="(item, index) in category.items"
								:key="`${category.key}-${index}`"
								class="legend-item"
							>
								<div class="legend-icon" v-if="item.icon">
									<img :src="getIconSrc(item, category.key)" :alt="item.text" />
								</div>

								<!-- 交通图例项显示颜色线条 -->
								<div
									v-else-if="category.key === 'traffic' && item.colorValue"
									class="legend-color-line"
									:style="{ backgroundColor: item.colorValue }"
								></div>

								<div class="legend-text">
									{{ item.text }}{{ item.count !== null ? ` (${item.count})` : "" }}
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</transition>
	</div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onBeforeUnmount } from "vue";
import Mock from "mockjs";
import axios from "axios";
import emitter from "@/utils/emitter";

// 使用 ref 初始化数据结构
const legendData = ref({
	categories: [],
});

// 弹窗显示状态
const isModalVisible = ref(false);

// 初始化折叠面板状态和图层显示状态
const collapseStates = reactive({});
const visibleStates = reactive({});

// 图层类型与GeoJSON标记类型的映射关系
const layerTypeMapping = {
	type: {
		// 工程类型对应的标记类型
		bridge: "bridge_marker",
		culvert: "culvert_marker",
		parking: "parking_marker",
		mixing_plant: "mixing_plant_marker",
		road_section: "road_section_marker",
	},
	// 其他图例类型的映射可以在这里添加
};

// 初始化状态
onMounted(async () => {
	// 通过请求获取 JSON 数据
	try {
		const response = await axios.get("./mock/yizhangtu/legendData.json");
		// 使用 Mock.js 处理数据
		legendData.value = Mock.mock(response.data);

		// 根据JSON数据初始化状态
		legendData.value.categories.forEach((category) => {
			collapseStates[category.key] = category.enabled !== undefined ? category.enabled : true;
			visibleStates[category.key] = category.enabled !== undefined ? category.enabled : false;
		});

		// 初始化时加载图层数量信息
		await initLayerCounts();

		// 监听地图引擎初始化完成事件
		emitter.$on("engineOnLoad", () => {
			// console.log("地图引擎初始化完成，开始加载标记");
			// 根据当前图例状态加载标记
			updateMapMarkers();
		});
	} catch (error) {
		console.error("加载图例数据失败:", error);
		// 使用默认数据
		// ... 默认数据部分保持不变 ...
	}

	document.addEventListener("click", handleOutsideClick);
});

// 初始化图层数量信息
const initLayerCounts = async () => {
	try {
		// 获取所有需要统计数量的图层数据
		const layersToCount = legendData.value.categories.filter((category) =>
			["type", "progress", "facility"].includes(category.key)
		);

		for (const category of layersToCount) {
			if (category.key === "type") {
				await loadEngineeringFacilitiesCounts(category.key);
			}
			// 可以添加其他类型的图层数量统计
		}
	} catch (error) {
		console.error("初始化图层数量信息失败:", error);
	}
};

// 加载工程设施数量信息
const loadEngineeringFacilitiesCounts = async (categoryKey) => {
	try {
		// 获取工程设施标记数据
		const response = await fetch("/config/engine/map/json/mark/engineering-facilities.mark.json");
		const geoJsonData = await response.json();

		if (
			!geoJsonData ||
			geoJsonData.type !== "FeatureCollection" ||
			!Array.isArray(geoJsonData.features)
		) {
			console.error("无效的GeoJSON数据格式");
			return;
		}

		// 初始化计数对象
		const typeCounts = {};

		// 遍历所有特征，根据category统计数量
		geoJsonData.features.forEach((feature) => {
			if (!feature.properties) return;

			// 获取category，如果不存在则尝试获取bridgeType
			const category = feature.properties.category || feature.properties.bridgeType;
			if (category) {
				typeCounts[category] = (typeCounts[category] || 0) + 1;
			}
		});

		// 更新图例中的数量
		const typeCategory = legendData.value.categories.find((c) => c.key === "type");
		if (typeCategory && typeCategory.items) {
			typeCategory.items.forEach((item) => {
				// 根据item的category属性匹配对应的计数
				if (item.category && typeCounts[item.category]) {
					item.count = typeCounts[item.category];
				} else {
					item.count = 0; // 如果没有匹配的数据，设置为0
				}
			});
		}
	} catch (error) {
		console.error("加载工程设施数量信息失败:", error);
	}
};

// 加载工程设施标记
const loadEngineeringFacilities = async (categoryKey, isVisible) => {
	try {
		// 获取工程设施标记数据
		const response = await fetch("/config/engine/map/json/mark/engineering-facilities.mark.json");
		const geoJsonData = await response.json();

		if (
			!geoJsonData ||
			geoJsonData.type !== "FeatureCollection" ||
			!Array.isArray(geoJsonData.features)
		) {
			console.error("无效的GeoJSON数据格式");
			return;
		}

		// 如果是工程类型图例，统计各类型的数量
		if (categoryKey === "type") {
			// 数量已在初始化时加载，此处不需要重复计算
			// 如果需要刷新数量，可以调用 loadEngineeringFacilitiesCounts(categoryKey);
		}

		// 简化标记处理逻辑，只负责发送事件
		// 发送事件通知地图引擎更新标记，将数据和显示状态传递给MarkerLayer处理
		emitter.$emit("update-markers", {
			categoryKey,
			isVisible,
			features: geoJsonData.features, // 传递所有特征，让MarkerLayer负责筛选
			layerTypeMapping, // 传递映射关系
		});
	} catch (error) {
		console.error("加载工程设施标记失败:", error);
	}
};

// 卸载前移除点击事件监听
onBeforeUnmount(() => {
	document.removeEventListener("click", handleOutsideClick);
	// 移除事件监听
	emitter.$off("engine-ready");
});

// 切换折叠面板
const toggleCollapse = (key) => {
	collapseStates[key] = !collapseStates[key];
};

// 更新地图标记
const updateMapMarkers = () => {
	// 遍历所有图例类型，根据其可见状态更新地图标记
	Object.entries(visibleStates).forEach(([key, isVisible]) => {
		if (key === "type") {
			loadEngineeringFacilities(key, isVisible);
		}
		// 其他图例类型的处理可以在这里添加
	});
};

// 切换图层显示状态
const toggleVisible = (key, value) => {
	visibleStates[key] = value;

	// 根据图例类型更新地图标记
	if (key === "type") {
		loadEngineeringFacilities(key, value);
	}
};

// 切换弹窗显示状态
const toggleModal = () => {
	isModalVisible.value = !isModalVisible.value;
};

// 关闭弹窗
const closeModal = () => {
	isModalVisible.value = false;
};

// 点击外部关闭弹窗
const handleOutsideClick = (event) => {
	if (isModalVisible.value && !event.target.closest(".map-legend")) {
		closeModal();
	}
};

// 动画前准备
const beforeEnter = (el) => {
	// 设置初始样式
	el.style.opacity = "0";
	el.style.transformOrigin = "bottom left";
	el.style.transform = "translate(0, 0) scale(0.1) rotate(-5deg)";
};

// 进入动画
const enter = (el, done) => {
	// 强制重绘
	el.offsetHeight;

	// 设置过渡
	el.style.transition = `opacity 300ms cubic-bezier(0.34, 1.56, 0.64, 1), transform 300ms cubic-bezier(0.34, 1.56, 0.64, 1)`;

	// 设置最终样式
	el.style.opacity = "1";
	el.style.transform = modalStyle.value.transform || "";

	// 动画完成后调用done
	setTimeout(done, 300);
};

// 离开动画
const leave = (el, done) => {
	// 设置过渡
	el.style.transition = `opacity 300ms cubic-bezier(0.36, 0, 0.66, -0.56), transform 300ms cubic-bezier(0.36, 0, 0.66, -0.56)`;

	// 设置离开样式
	el.style.opacity = "0";
	el.style.transform = "translate(0, 0) scale(0.1) rotate(5deg)";

	// 动画完成后调用done
	setTimeout(done, 300);
};

// 计算弹窗样式
const modalStyle = computed(() => {
	return {
		width: "425px",
		height: "auto",
		bottom: "100%",
		left: "0",
		transform: "translate(0px, -10px)",
	};
});

// 获取图标源
const getIconSrc = (item, categoryKey) => {
	// 如果面板关闭或图层不可见，直接返回普通图标
	if (!visibleStates[categoryKey]) {
		return item.icon;
	}

	// 判断是否有激活图标并且计数大于0
	if (item.count && item.count > 0 && item.activeIcon) {
		return item.activeIcon;
	}

	// 默认返回普通图标
	return item.icon;
};
</script>

<style lang="scss" scoped>
.map-legend {
	position: relative;

	&-btn {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-between;
		width: 62px;
		height: 62px;
		cursor: pointer;
		&-icon {
			width: 40px;
			height: 40px;
			img {
				width: 100%;
				height: 100%;
			}
		}
		&-text {
			font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
			font-size: 14px;
			color: #ffffff;
			text-shadow: 0px 0px 9px #158eff;
		}
	}

	&-modal {
		width: 425px;
		height: 500px;
		position: absolute;
		z-index: 100;
		background: rgba(19, 51, 92, 0.3); /* 降低不透明度以便看到模糊效果 */
		backdrop-filter: blur(8px); /* 添加8px的模糊效果 */
		-webkit-backdrop-filter: blur(8px); /* Safari浏览器兼容 */
		border-radius: 4px;
		border: 1px solid;
		border-image: linear-gradient(
				90deg,
				rgba(7, 131, 250, 0),
				rgba(10, 135, 255, 1),
				rgba(7, 131, 250, 1),
				rgba(7, 131, 250, 1),
				rgba(7, 131, 250, 0)
			)
			1 1;
		&-header {
			position: relative;
			display: flex;
			justify-content: center;
			align-items: center;
			height: 44px;

			&::after {
				content: "";
				position: absolute;
				bottom: -1px;
				left: 50%;
				transform: translateX(-50%);
				width: 427px;
				height: 1px;
				background: linear-gradient(
					270deg,
					rgba(9, 135, 255, 0) 0%,
					#0987ff 47%,
					rgba(9, 135, 255, 0) 100%
				);
				border-radius: 0px 0px 0px 0px;
			}
		}

		&-title {
			font-family: YouSheBiaoTiHei;
			font-size: 22px;
			color: #ffffff;
			text-shadow: 0px 0px 9px #158eff;
		}

		&-content {
			padding: 12px;
			max-height: 500px;
			overflow-y: auto;
		}
	}
}

// 折叠面板样式
.legend-collapse {
	margin-bottom: 8px;

	&-header {
		display: flex;
		align-items: center;
		padding: 8px 0;
		cursor: default;

		.collapse-icon {
			width: 16px;
			height: 16px;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 8px;
			transition: transform 0.3s;
			cursor: pointer;
			transform: rotate(-90deg);
			img {
				width: 100%;
				height: 100%;
			}

			&.is-active {
				transform: rotate(0);
			}
		}

		.collapse-title {
			font-family: YouSheBiaoTiHei;
			font-size: 16px;
			color: #ffffff;
			// flex: 1;
		}

		.collapse-switch {
			// flex: ;
			margin-left: 8px;
		}
	}

	&-content {
		padding-left: 24px;
		display: grid;
		grid-gap: 8px;

		// 3列布局
		&.col-3 {
			grid-template-columns: repeat(3, 1fr);
		}

		// 4列布局
		&.col-4 {
			grid-template-columns: repeat(4, 1fr);
		}
	}
}

// 图例项样式
.legend-item {
	display: flex;
	color: #fff;
	font-size: 12px; // 减小字体大小以适应多列布局
	align-items: center; // 确保垂直居中对齐

	// 添加图例图标样式
	.legend-icon {
		width: 16px;
		height: 16px;
		margin-right: 4px;
		img {
			width: 100%;
			height: 100%;
		}

		// // 工程进度图标样式
		// &.progress-not-started {
		//   background-color: #cccccc;
		// }
		// &.progress-in-progress {
		//   background-color: #ffaa00;
		// }
		// &.progress-completed {
		//   background-color: #00cc66;
		// }

		// ... 其他图标样式保持不变 ...
	}

	// 添加交通图例颜色线条样式
	.legend-color-line {
		width: 16px;
		height: 4px;
		margin-right: 4px;
	}

	.legend-text {
		font-family: Source Han Sans CN-Regular, Source Han Sans CN;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
}
</style>

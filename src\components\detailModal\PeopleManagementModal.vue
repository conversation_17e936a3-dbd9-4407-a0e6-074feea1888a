<template>
  <Modal ref="ModalRef" title="人员管理">
    <template #title> 人员管理 </template>

    <!-- Tab选项卡 -->
    <div class="progress-tabs">
      <div
        v-for="tab in tabs"
        :key="tab.key"
        class="tab-item"
        :class="{ active: activeTab === tab.key }"
        @click="handleTabClick(tab.key)"
      >
        {{ tab.label }}
      </div>
    </div>

    <!-- Tab内容区域 -->
    <div class="tab-content">
      <div v-if="activeTab === 'project-daily'" class="tab-panel">
        <!-- 卡片展示 -->
        <div class="card-list">
          <div class="card-item-selectd">
            <div class="card-title">项目管理人员总数(人)</div>
            <div class="number">123,234</div>
          </div>
        </div>
        <!-- 项目日报人员内容 -->
        <div class="table-container">
          <div class="people-table">
            <div class="table-header">
              <span>姓名</span>
              <span>职位</span>
              <span>部门</span>
              <span>联系方式</span>
              <span>状态</span>
            </div>
            <div class="table-body">
              <div
                class="table-row"
                v-for="(person, idx) in getCurrentPageData('project-daily')"
                :key="idx"
              >
                <span>{{ person.name }}</span>
                <span>{{ person.position }}</span>
                <span>{{ person.department }}</span>
                <span>{{ person.contact }}</span>
                <span class="status">
                  <span
                    class="dot"
                    :class="{
                      active: person.status === '在岗',
                      inactive: person.status !== '在岗',
                    }"
                  ></span>
                  <span
                    :class="{
                      'status-active': person.status === '在岗',
                      'status-inactive': person.status !== '在岗',
                    }"
                  >
                    {{ person.status }}
                  </span>
                </span>
              </div>
            </div>
          </div>
          <!-- 分页组件 -->
          <div class="pagination-container">
            <div class="pagination-info">
              共<span class="pagination-number">{{ getTotalPages("project-daily") }}</span
              >页/<span class="pagination-number">{{ projectDailyPersonnel.length }}</span
              >条数据
            </div>
            <div class="pagination">
              <div class="pagination-info">
                共<span class="pagination-number">{{ getTotalPages("project-daily") }}</span
                >页
              </div>
              <button
                class="pagination-btn prev"
                :disabled="pagination.projectDaily.current === 1"
                @click="changePage('project-daily', pagination.projectDaily.current - 1)"
              >
                <IconLeft />
              </button>
              <button
                v-for="page in getPageNumbers('project-daily')"
                :key="page"
                class="pagination-btn"
                :class="{
                  active: page === pagination.projectDaily.current,
                  ellipsis: page === '...',
                }"
                :disabled="page === '...'"
                @click="page !== '...' && changePage('project-daily', page)"
              >
                {{ page }}
              </button>
              <button
                class="pagination-btn next"
                :disabled="pagination.projectDaily.current === getTotalPages('project-daily')"
                @click="changePage('project-daily', pagination.projectDaily.current + 1)"
              >
                <IconRight />
              </button>
            </div>
          </div>
        </div>
      </div>

      <div v-if="activeTab === 'safety-management'" class="tab-panel">
        <!-- 安全管理人员内容 -->
        <div class="table-container">
          <div class="people-table">
            <div class="table-header">
              <span>姓名</span>
              <span>职位</span>
              <span>证书编号</span>
              <span>有效期</span>
              <span>状态</span>
            </div>
            <div class="table-body">
              <div
                class="table-row"
                v-for="(person, idx) in getCurrentPageData('safety-management')"
                :key="idx"
              >
                <span>{{ person.name }}</span>
                <span>{{ person.position }}</span>
                <span>{{ person.certificateNo }}</span>
                <span>{{ person.validUntil }}</span>
                <span class="status">
                  <span
                    class="dot"
                    :class="{
                      active: person.status === '有效',
                      inactive: person.status !== '有效',
                    }"
                  ></span>
                  <span
                    :class="{
                      'status-active': person.status === '有效',
                      'status-inactive': person.status !== '有效',
                    }"
                  >
                    {{ person.status }}
                  </span>
                </span>
              </div>
            </div>
          </div>
          <!-- 分页组件 -->
          <div class="pagination-container">
            <div class="pagination-info">
              共<span class="pagination-number">{{ getTotalPages("safety-management") }}</span
              >页/<span class="pagination-number">{{ safetyManagementPersonnel.length }}</span
              >条数据
            </div>
            <div class="pagination">
              <div class="pagination-info">
                共<span class="pagination-number">{{ getTotalPages("safety-management") }}</span
                >页
              </div>
              <button
                class="pagination-btn prev"
                :disabled="pagination.safetyManagement.current === 1"
                @click="changePage('safety-management', pagination.safetyManagement.current - 1)"
              >
                <IconLeft />
              </button>
              <button
                v-for="page in getPageNumbers('safety-management')"
                :key="page"
                class="pagination-btn"
                :class="{
                  active: page === pagination.safetyManagement.current,
                  ellipsis: page === '...',
                }"
                :disabled="page === '...'"
                @click="page !== '...' && changePage('safety-management', page)"
              >
                {{ page }}
              </button>
              <button
                class="pagination-btn next"
                :disabled="
                  pagination.safetyManagement.current === getTotalPages('safety-management')
                "
                @click="changePage('safety-management', pagination.safetyManagement.current + 1)"
              >
                <IconRight />
              </button>
            </div>
          </div>
        </div>
      </div>

      <div v-if="activeTab === 'special-operation'" class="tab-panel">
        <!-- 特种作业人员内容 -->
        <div class="table-container">
          <div class="people-table">
            <div class="table-header">
              <span>姓名</span>
              <span>作业类型</span>
              <span>操作证号</span>
              <span>有效期</span>
              <span>状态</span>
            </div>
            <div class="table-body">
              <div
                class="table-row"
                v-for="(person, idx) in getCurrentPageData('special-operation')"
                :key="idx"
              >
                <span>{{ person.name }}</span>
                <span>{{ person.operationType }}</span>
                <span>{{ person.licenseNo }}</span>
                <span>{{ person.validUntil }}</span>
                <span class="status">
                  <span
                    class="dot"
                    :class="{
                      active: person.status === '有效',
                      inactive: person.status !== '有效',
                    }"
                  ></span>
                  <span
                    :class="{
                      'status-active': person.status === '有效',
                      'status-inactive': person.status !== '有效',
                    }"
                  >
                    {{ person.status }}
                  </span>
                </span>
              </div>
            </div>
          </div>
          <!-- 分页组件 -->
          <div class="pagination-container">
            <div class="pagination-info">
              共<span class="pagination-number">{{ getTotalPages("special-operation") }}</span
              >页/<span class="pagination-number">{{ specialOperationPersonnel.length }}</span
              >条数据
            </div>
            <div class="pagination">
              <div class="pagination-info">
                共<span class="pagination-number">{{ getTotalPages("special-operation") }}</span
                >页
              </div>
              <button
                class="pagination-btn prev"
                :disabled="pagination.specialOperation.current === 1"
                @click="changePage('special-operation', pagination.specialOperation.current - 1)"
              >
                <IconLeft />
              </button>
              <button
                v-for="page in getPageNumbers('special-operation')"
                :key="page"
                class="pagination-btn"
                :class="{
                  active: page === pagination.specialOperation.current,
                  ellipsis: page === '...',
                }"
                :disabled="page === '...'"
                @click="page !== '...' && changePage('special-operation', page)"
              >
                {{ page }}
              </button>
              <button
                class="pagination-btn next"
                :disabled="
                  pagination.specialOperation.current === getTotalPages('special-operation')
                "
                @click="changePage('special-operation', pagination.specialOperation.current + 1)"
              >
                <IconRight />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Modal>
</template>

<script setup>
import Modal from "@/views/gdMap/components/traffic/components/Modal.vue";
import { IconLeft, IconRight } from "@arco-design/web-vue/es/icon";

const ModalRef = ref(null);

// Tab相关数据
const tabs = [
  { key: "project-daily", label: "项目日报人员" },
  { key: "safety-management", label: "安全管理人员" },
  { key: "special-operation", label: "特种作业人员" },
];

const activeTab = ref("project-daily");

// 分页配置
const pageSize = 5; // 每页显示条数
const pagination = ref({
  projectDaily: { current: 1 },
  safetyManagement: { current: 1 },
  specialOperation: { current: 1 },
});

// 模拟数据
const projectDailyPersonnel = ref([
  {
    name: "张三",
    position: "项目经理",
    department: "工程部",
    contact: "138****1234",
    status: "在岗",
  },
  {
    name: "李四",
    position: "技术负责人",
    department: "技术部",
    contact: "139****5678",
    status: "在岗",
  },
  {
    name: "王五",
    position: "质量员",
    department: "质检部",
    contact: "137****9012",
    status: "休假",
  },
  {
    name: "赵六",
    position: "安全员",
    department: "安全部",
    contact: "136****3456",
    status: "在岗",
  },
  {
    name: "孙七",
    position: "施工员",
    department: "工程部",
    contact: "135****7890",
    status: "在岗",
  },
  {
    name: "周八",
    position: "材料员",
    department: "物资部",
    contact: "134****2345",
    status: "在岗",
  },
  {
    name: "吴九",
    position: "测量员",
    department: "测量部",
    contact: "133****6789",
    status: "在岗",
  },
  {
    name: "郑十",
    position: "预算员",
    department: "财务部",
    contact: "132****0123",
    status: "休假",
  },
  {
    name: "钱十一",
    position: "资料员",
    department: "档案部",
    contact: "131****4567",
    status: "在岗",
  },
  {
    name: "陈十二",
    position: "机械员",
    department: "设备部",
    contact: "130****8901",
    status: "在岗",
  },
  {
    name: "林十三",
    position: "试验员",
    department: "试验部",
    contact: "129****2345",
    status: "在岗",
  },
  {
    name: "黄十四",
    position: "劳务员",
    department: "人事部",
    contact: "128****6789",
    status: "休假",
  },
]);

const safetyManagementPersonnel = ref([
  {
    name: "刘安全",
    position: "安全总监",
    certificateNo: "AQ001234",
    validUntil: "2025-12-31",
    status: "有效",
  },
  {
    name: "陈管理",
    position: "安全主管",
    certificateNo: "AQ005678",
    validUntil: "2024-08-15",
    status: "即将到期",
  },
  {
    name: "周监督",
    position: "安全监督员",
    certificateNo: "AQ009012",
    validUntil: "2026-03-20",
    status: "有效",
  },
  {
    name: "吴检查",
    position: "安全检查员",
    certificateNo: "AQ003456",
    validUntil: "2025-06-10",
    status: "有效",
  },
]);

const specialOperationPersonnel = ref([
  {
    name: "高师傅",
    operationType: "电工作业",
    licenseNo: "DG123456",
    validUntil: "2025-10-15",
    status: "有效",
  },
  {
    name: "马师傅",
    operationType: "焊工作业",
    licenseNo: "HG789012",
    validUntil: "2024-12-20",
    status: "有效",
  },
  {
    name: "林师傅",
    operationType: "起重机械",
    licenseNo: "QZ345678",
    validUntil: "2024-09-30",
    status: "即将到期",
  },
  {
    name: "郭师傅",
    operationType: "登高作业",
    licenseNo: "DG901234",
    validUntil: "2025-07-25",
    status: "有效",
  },
  {
    name: "何师傅",
    operationType: "压力容器",
    licenseNo: "YL567890",
    validUntil: "2026-01-10",
    status: "有效",
  },
]);

// Tab切换处理
const handleTabClick = (tabKey) => {
  activeTab.value = tabKey;
};

// 分页相关方法
const getDataByType = (type) => {
  switch (type) {
    case "project-daily":
      return projectDailyPersonnel.value;
    case "safety-management":
      return safetyManagementPersonnel.value;
    case "special-operation":
      return specialOperationPersonnel.value;
    default:
      return [];
  }
};

const getPaginationByType = (type) => {
  switch (type) {
    case "project-daily":
      return pagination.value.projectDaily;
    case "safety-management":
      return pagination.value.safetyManagement;
    case "special-operation":
      return pagination.value.specialOperation;
    default:
      return { current: 1 };
  }
};

const getTotalPages = (type) => {
  const data = getDataByType(type);
  return Math.ceil(data.length / pageSize);
};

const getCurrentPageData = (type) => {
  const data = getDataByType(type);
  const currentPagination = getPaginationByType(type);
  const start = (currentPagination.current - 1) * pageSize;
  const end = start + pageSize;
  return data.slice(start, end);
};

const changePage = (type, page) => {
  const totalPages = getTotalPages(type);
  if (page < 1 || page > totalPages) return;

  switch (type) {
    case "project-daily":
      pagination.value.projectDaily.current = page;
      break;
    case "safety-management":
      pagination.value.safetyManagement.current = page;
      break;
    case "special-operation":
      pagination.value.specialOperation.current = page;
      break;
  }
};

const getPageNumbers = (type) => {
  const totalPages = getTotalPages(type);
  const currentPagination = getPaginationByType(type);
  const current = currentPagination.current;
  const pages = [];

  if (totalPages <= 7) {
    // 如果总页数小于等于7，显示所有页码
    for (let i = 1; i <= totalPages; i++) {
      pages.push(i);
    }
  } else {
    // 总页数大于7时，显示省略号
    if (current <= 4) {
      // 当前页在前面
      for (let i = 1; i <= 5; i++) {
        pages.push(i);
      }
      pages.push("...");
      pages.push(totalPages);
    } else if (current >= totalPages - 3) {
      // 当前页在后面
      pages.push(1);
      pages.push("...");
      for (let i = totalPages - 4; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // 当前页在中间
      pages.push(1);
      pages.push("...");
      for (let i = current - 1; i <= current + 1; i++) {
        pages.push(i);
      }
      pages.push("...");
      pages.push(totalPages);
    }
  }

  return pages;
};

const onOpen = () => {
  ModalRef.value?.open();
};

defineExpose({
  open: onOpen,
});
</script>

<style lang="scss" scoped>
.progress-tabs {
  display: flex;
  margin-bottom: 10px;
  background-color: #001220;
}

.tab-item {
  font-family: Alibaba PuHuiTi;
  padding: 10px 54px;
  font-size: 18px;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  color: #8a9ba8;
  background: transparent;
  border: none;

  &:hover {
    color: #c4e5ff;
  }

  &.active {
    color: #ffffff;
    background: linear-gradient(180deg, rgba(0, 138, 255, 0) 0%, #008aff 100%);
    border-radius: 0px 0px 0px 0px;

    &::before {
      content: "";
      position: absolute;
      top: -1px;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, #008aff 0%, #6dbcff 50%, #008aff 100%);
    }
  }
}

.tab-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 400px;
  .card-list {
    .card-item-selectd {
      width: 244px;
      height: 80px;
      margin: 10px 20px;
      /* background: linear-gradient(
        90deg,
        rgba(1, 82, 231, 0) 0%,
        rgb(1, 82, 231,0.5) 52%,
        rgba(1, 82, 231, 0) 100%
      ); */
      background-image: url("@/assets/images/detail/card-selected.png");
      background-size: cover;
      background-repeat: no-repeat;
      font-family: Alibaba PuHuiTi;
      font-weight: normal;
      font-size: 18px;
      color: #ffffff;
      text-align: center;
      font-style: normal;
      text-transform: none;
      .card-title {
        padding: 10px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 12px;
        /* color: rgb(197, 217, 255); */
        text-align: center;
        font-style: normal;
        text-transform: none;
        background: linear-gradient(90deg, rgb(197, 217, 255) 0%, #ffffff 100%);
        -moz-background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .number {
        font-family: D-DIN-PRO;
        font-weight: 800;
        font-size: 28px;
        /* text-shadow: 0px 2px 0px rgba(0, 43, 59, 0.5); */
        text-align: center;
        font-style: normal;
        text-transform: none;
        background: linear-gradient(90deg, #ffffff 0%, #29a9ff 100%);
        -moz-background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
    .card-item {
      width: 244px;
      height: 80px;
      margin: 10px 20px;
      /* background: linear-gradient(
        90deg,
        rgba(1, 82, 231, 0) 0%,
        rgb(1, 82, 231,0.5) 52%,
        rgba(1, 82, 231, 0) 100%
      ); */
      background-image: url("@/assets/images/detail/card-selected.png");
      background-size: cover;
      background-repeat: no-repeat;
      font-family: Alibaba PuHuiTi;
      font-weight: normal;
      font-size: 18px;
      color: #ffffff;
      text-align: center;
      font-style: normal;
      text-transform: none;
      .card-title {
        padding: 10px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 12px;
        /* color: rgb(197, 217, 255); */
        text-align: center;
        font-style: normal;
        text-transform: none;
        background: linear-gradient(90deg, rgb(197, 217, 255) 0%, #ffffff 100%);
        -moz-background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .number {
        font-family: D-DIN-PRO;
        font-weight: 800;
        font-size: 28px;
        /* text-shadow: 0px 2px 0px rgba(0, 43, 59, 0.5); */
        text-align: center;
        font-style: normal;
        text-transform: none;
        background: linear-gradient(90deg, #ffffff 0%, #29a9ff 100%);
        -moz-background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }
}

.tab-panel {
  flex: 1;
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  min-height: 0; // 确保flex子项能正确收缩
}

.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; // 确保flex子项能正确收缩
}

.content-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 350px;
  text-align: center;
}

.placeholder-text {
  font-size: 24px;
  color: #ffffff;
  font-family: Alibaba PuHuiTi;
  margin-bottom: 16px;
  background: linear-gradient(180deg, #dff0fa 19%, #e0f0ff 46%, #a8d4f9 82%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.placeholder-desc {
  font-size: 16px;
  color: #8a9ba8;
  font-family: Alibaba PuHuiTi;
  line-height: 1.6;
}

/* 表格样式 */
.people-table {
  font-family: Alibaba PuHuiTi;
  display: flex;
  flex-direction: column;
  flex: 1;
  background: rgba(30, 60, 100, 0.1);
  overflow: hidden;
  min-height: 0; // 确保表格能正确收缩
  /* border-radius: 8px; */
}

.table-header {
  font-size: 14px;
  color: #ffffff;
  line-height: 40px;
  display: flex;
  align-items: center;
  background: linear-gradient(180deg, rgba(0, 138, 255, 0) 47%, #008aff 100%),
    rgba(0, 138, 255, 0.1);
  box-shadow: inset 0px 0px 8px 1px rgba(0, 138, 255, 0.25);
  /* border-radius: 8px 8px 0px 0px; */
  border: 1px solid #008aff;
  border-image: linear-gradient(
      90deg,
      rgba(0, 138, 255, 0),
      rgba(0, 138, 255, 1),
      rgba(0, 138, 255, 0.2),
      rgba(0, 138, 255, 0)
    )
    1 1;
  padding: 0 16px;
  font-family: Alibaba PuHuiTi;
  font-weight: 500;

  span {
    flex: 1;
    text-align: center;
  }
}

.table-body {
  flex: 1;
  overflow-y: auto;
  min-height: 0; // 确保能正确收缩
}

.table-row {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: #fff;
  font-size: 13px;
  min-height: 48px;
  border-bottom: 1px solid rgba(0, 138, 255, 0.2);
  font-family: Alibaba PuHuiTi;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: rgba(0, 138, 255, 0.05);
  }

  &:last-child {
    border-bottom: none;
  }

  span {
    flex: 1;
    text-align: center;
    word-break: break-all;
  }

  .status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;

    .dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      flex-shrink: 0;

      &.active {
        background: #27f3bd;
        box-shadow: 0px 0px 8px 0px rgba(41, 255, 198, 0.6);
      }

      &.inactive {
        background: #f3b927;
        box-shadow: 0px 0px 8px 0px rgba(255, 194, 41, 0.6);
      }
    }

    .status-active {
      color: #27f3bd;
    }

    .status-inactive {
      color: #f3b927;
    }
  }
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: rgba(30, 60, 100, 0.05);
  border-top: 1px solid rgba(0, 138, 255, 0.2);
  font-family: Alibaba PuHuiTi;
  flex-shrink: 0; // 分页区域不收缩，固定在底部
}

.pagination-info {
  font-size: 14px;
  color: #8a9ba8;

  .pagination-number {
    color: #008aff;
    font-weight: 500;
  }
}

.pagination {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-btn {
  min-width: 32px;
  height: 32px;
  padding: 0 8px;
  border: 1px solid rgba(0, 138, 255, 0.3);
  background: transparent;
  color: #8a9ba8;
  font-size: 14px;
  font-family: Alibaba PuHuiTi;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover:not(:disabled):not(.ellipsis) {
    border-color: #008aff;
    color: #c4e5ff;
    background: rgba(0, 138, 255, 0.1);
  }

  &.active {
    border-color: #008aff;
    background: linear-gradient(180deg, rgba(0, 138, 255, 0) 0%, #008aff 100%);
    color: #ffffff;
    box-shadow: 0px 0px 8px 0px rgba(0, 138, 255, 0.4);
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  &.ellipsis {
    cursor: default;
    border: none;
    background: transparent;

    &:hover {
      border: none;
      background: transparent;
      color: #8a9ba8;
    }
  }

  &.prev,
  &.next {
    width: 32px;

    svg {
      width: 14px;
      height: 14px;
    }
  }
}
</style>

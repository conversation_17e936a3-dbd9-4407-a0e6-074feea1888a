.marker-panel {
	position: absolute;
	width: 230px;
	background: rgba(7, 7, 7, 0.5);
	backdrop-filter: blur(8px);
	border: 1px solid rgba(166, 190, 212, 0.4);
	box-sizing: border-box;

	&::after {
		content: "";
		position: absolute;
		right: 1px;
		top: 1px;
		width: calc(100% - 2px);
		height: calc(100% - 2px);
		border: 1px solid rgba(166, 190, 212, 0.4);
		box-sizing: border-box;
	}

	.panel-line-dotted {
		position: absolute;
		right: 0;
		top: 0;
		width: 135px;
		height: 3px;
		background-image: url("@/assets/modules/maplibre/icon/marker-line-dotted.svg");
		background-size: 100% 100%;
		background-repeat: no-repeat;
	}

	.panel-line-rhombus {
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 2px;
		height: 20px;
		background-image: url("@/assets/modules/maplibre/icon/marker-line-rhombus.svg");
		background-size: 100% 100%;
		background-repeat: no-repeat;
	}

	.panel-header {
		position: relative; /* 添加相对定位 */
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 4px 12px;
		width: calc(100% - 24px);
		height: 29px;
		z-index: 1;

		&-title {
			font-family: Alibaba PuHuiTi;
			font-size: 14px;
			color: #d9e5f5;
		}

		&::after {
			content: "";
			position: absolute;
			left: 8px;
			bottom: 0;
			width: calc(100% - 16px);
			height: 1px;
			background-color: #4f6070;
		}
	}

	.panel-content {
		padding: 12px;
		height: calc(100% - 37px);
		overflow-y: auto;
		font-family: Alibaba PuHuiTi;
		font-size: 12px;
		color: rgba(217, 229, 245, 0.7);

		/* 添加表单数据样式 */
		:deep(.data-list) {
			display: flex;
			flex-direction: column;
			gap: 2px;

			.data-tabs {
				height: 16px;
				display: flex;
				flex-direction: row;

				&-item {
					width: 60px;
					background: #331213;

					&.is-active {
						border-top: 1px solid;
						border-image: linear-gradient(90deg, #ff0d00 0%, #ff746d 50%, #ff0d00 100%);
						background-image: linear-gradient(180deg, rgba(255, 13, 0, 0) 0%, #ff0d00 100%);
					}
				}
			}

			.data-item {
				margin-bottom: 8px;
				display: flex;
				align-items: center;
				&:last-child {
					margin-bottom: 0;
				}

				.data-key {
					min-width: 60px;
				}
			}

			.no-data {
				color: rgba(255, 255, 255, 0.6);
				text-align: center;
				padding: 20px 0;
			}
		}
	}

	&.is-danger {
		background: rgba(85, 0, 0, 0.5);

		.panel-line-dotted {
			background-image: url("@/assets/modules/maplibre/icon/marker-line-dotted-red.svg");
		}

		.panel-line-rhombus {
			background-image: url("@/assets/modules/maplibre/icon/marker-line-rhombus-red.svg");
		}
	}
}

.common-marker {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	// box-shadow: 0px 0px 16px 0px var(--color-shadow), 0px 0px 8px 0px var(--color-shadow);
	position: relative;

	background-size: 100% 100%;
	background-repeat: no-repeat;

	&::before {
		// content: "";
		position: absolute;
		top: -1px;
		left: -1px;
		right: -1px;
		bottom: -1px;
		background-image: linear-gradient(
			224deg,
			rgba(var(--color-border), 0),
			rgba(var(--color-border), 1)
		);
		border-radius: 50%;
		mask: radial-gradient(circle 16px at center, transparent 16px, black 16px);
	}

	&::after {
		// content: "";
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		width: 42px;
		height: 42px;
		border-radius: 50%;
		background-image: linear-gradient(
			224deg,
			rgba(var(--color-border), 0),
			rgba(var(--color-border), 1)
		);
		mask: radial-gradient(circle 19px at center, transparent 19px, black 19px);
	}

	.icon {
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		width: calc(32 / 64 * 100%);
		height: calc(32 / 64 * 100%);
		display: flex;
		align-items: center;
		justify-content: center;
		// background-color: rgba(0, 0, 0, 0.8);
		border-radius: 50%;
		// box-shadow: inset 0 0 36px 10px var(--color-shadow);
		cursor: pointer;

		:deep(svg) {
			width: 62.5%;
			height: 62.5%;
			fill: var(--fill-color, #fff);
		}
	}

	&.is-blue {
		--fill-color: #79bcff;
		background-image: url("@/assets/modules/common/bg/marker-bg-blue.svg");
	}

	&.is-orange {
		--fill-color: #ff711b;
		background-image: url("@/assets/modules/common/bg/marker-bg-orange.svg");
	}

	&.is-red {
		--fill-color: #ff2a1b;
		background-image: url("@/assets/modules/common/bg/marker-bg-red.svg");
	}

	&.is-cyan {
		--fill-color: #79fff8;
		background-image: url("@/assets/modules/common/bg/marker-bg-cyan.svg");
	}

	&.is-yellow {
		--fill-color: #fff279;
		background-image: url("@/assets/modules/common/bg/marker-bg-yellow.svg");
	}

	// transform: scale(3);
}

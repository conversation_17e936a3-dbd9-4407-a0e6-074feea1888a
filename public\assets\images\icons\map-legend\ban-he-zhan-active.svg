<svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#230;&#139;&#140;&#229;&#144;&#136;&#231;&#171;&#153;">
<g id="Ellipse 15" filter="url(#filter0_d_505_2979)">
<circle cx="10.9998" cy="11.2998" r="8" fill="url(#paint0_radial_505_2979)" fill-opacity="0.7"/>
<circle cx="10.9998" cy="11.2998" r="7.55492" stroke="url(#paint1_linear_505_2979)" stroke-width="0.890162"/>
</g>
<circle id="Ellipse 16" cx="11.0737" cy="11.3732" r="7.07681" fill="url(#paint2_radial_505_2979)" stroke="url(#paint3_linear_505_2979)" stroke-width="0.200286"/>
<g id="&#229;&#176;&#143;&#230;&#161;&#165;" filter="url(#filter1_d_505_2979)">
<path id="Vector" d="M14.9526 8.43931H14.9512C14.9522 8.4356 14.9526 8.43179 14.9526 8.42797C14.9526 8.30879 14.5098 8.21217 13.9635 8.21217C13.8361 8.21217 13.7145 8.21748 13.6026 8.22705V12.7125L13.6023 12.7127L13.537 12.7967H14.9526V8.43931H14.9526ZM14.1096 13.8121L14.9524 12.7968H13.5369L13.2647 13.1468L13.817 13.8121H12.3552L13.3818 12.4922H11.0546L12.0812 13.8121H9.63847V12.467H11.0493V12.492H13.3821V7.06705H13.3805C13.3815 7.06267 13.3821 7.05819 13.3821 7.05369C13.3821 6.91347 12.8611 6.7998 12.2184 6.7998C11.5758 6.7998 11.0548 6.91346 11.0548 7.05369C11.0548 7.05816 11.0553 7.06261 11.0564 7.06705H11.0493V10.6808H9.63847V9.88699H6.94821V13.8121H6V14.7383H15.9011V13.8121H14.1096ZM9.63847 11.1439H11.0493V12.0039H9.63847V11.1439ZM8.82258 12.8639H7.85232V12.1559H8.82258V12.8639ZM8.82258 11.3889H7.85232V10.6808H8.82258V11.3889Z" fill="url(#paint4_linear_505_2979)"/>
</g>
</g>
<defs>
<filter id="filter0_d_505_2979" x="0.32927" y="0.629319" width="21.341" height="21.341" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.33524"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.388235 0 0 0 0 0.640523 0 0 0 0 0.807843 0 0 0 0.51 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_505_2979"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_505_2979" result="shape"/>
</filter>
<filter id="filter1_d_505_2979" x="6" y="6.7998" width="9.90112" height="8.38356" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.445081"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.150327 0 0 0 0 0.247059 0 0 0 0 0.145098 0 0 0 0.75 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_505_2979"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_505_2979" result="shape"/>
</filter>
<radialGradient id="paint0_radial_505_2979" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(10.9998 11.2998) scale(8)">
<stop stop-color="#000101" stop-opacity="0"/>
<stop offset="1" stop-color="#07B5FA"/>
</radialGradient>
<linearGradient id="paint1_linear_505_2979" x1="10.9998" y1="19.2998" x2="10.9998" y2="3.2998" gradientUnits="userSpaceOnUse">
<stop stop-color="#07B5FA"/>
<stop offset="0.49" stop-color="#0783FA" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#0783FA" stop-opacity="0.13"/>
</linearGradient>
<radialGradient id="paint2_radial_505_2979" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(11.0737 11.3732) scale(7.17695)">
<stop stop-color="#07B5FA"/>
<stop offset="1" stop-color="#0B103B" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint3_linear_505_2979" x1="11.0737" y1="18.5502" x2="11.0737" y2="4.19629" gradientUnits="userSpaceOnUse">
<stop stop-color="#07B5FA"/>
<stop offset="0.49" stop-color="#07B5FA" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#07B5FA" stop-opacity="0.13"/>
</linearGradient>
<linearGradient id="paint4_linear_505_2979" x1="8.31517" y1="14.2796" x2="10.0572" y2="6.58518" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#07B5FA"/>
</linearGradient>
</defs>
</svg>

<template>
	<div v-if="headTab === 0">
		<RealTimeFlow />
		<Quality />
		<Craft />
		<Facility />
	</div>
</template>

<script setup>
import emitter from "@/utils/emitter";
import RealTimeFlow from "@/views/gdMap/components/traffic/RealTimeFlow.vue";
import Quality from "./Quality.vue";
import Craft from "./Craft.vue";
import Facility from "./Facility.vue";

const headTab = ref(0);

onMounted(() => {
	emitter.$on("header-tab-change", handleHeadTabChange);
});

onUnmounted(() => {
	emitter.$off("header-tab-change", handleHeadTabChange);
});

const handleHeadTabChange = (value) => {
	headTab.value = value;
};
</script>

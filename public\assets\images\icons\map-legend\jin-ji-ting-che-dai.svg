<svg width="23" height="23" viewBox="0 0 23 23" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#231;&#180;&#167;&#230;&#128;&#165;&#229;&#129;&#156;&#232;&#189;&#166;&#229;&#184;&#166;">
<g id="Ellipse 15" filter="url(#filter0_d_342_3730)">
<circle cx="11.5222" cy="11.5518" r="8" fill="url(#paint0_radial_342_3730)" fill-opacity="0.7"/>
<circle cx="11.5222" cy="11.5518" r="7.55492" stroke="url(#paint1_linear_342_3730)" stroke-width="0.890162"/>
</g>
<circle id="Ellipse 16" cx="11.5958" cy="11.6252" r="7.07681" fill="url(#paint2_radial_342_3730)" stroke="url(#paint3_linear_342_3730)" stroke-width="0.200286"/>
<g id="&#229;&#176;&#143;&#230;&#161;&#165;" filter="url(#filter1_d_342_3730)">
<g id="Frame" clip-path="url(#clip0_342_3730)">
<path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M12.3069 7.61149L15.8378 13.7269C16.2695 14.4753 15.9164 15.086 15.0538 15.086H7.99078C7.1279 15.086 6.77467 14.4755 7.20631 13.7269L10.7374 7.61149C11.1692 6.86388 11.8753 6.86388 12.3069 7.61149ZM11.6792 12.5912C12.2984 12.5912 12.7381 12.4544 12.986 12.1847C13.231 11.918 13.3553 11.5886 13.3553 11.2056C13.3553 10.9821 13.3087 10.7734 13.2168 10.5854C13.1237 10.3953 12.9981 10.2446 12.8433 10.1375C12.6912 10.0323 12.5025 9.9603 12.2825 9.92357C12.1308 9.89607 11.9117 9.88212 11.6312 9.88212H9.94382V14.2724H10.7459V12.5912H11.6792ZM11.6763 10.6258C11.9703 10.6258 12.0982 10.6433 12.153 10.6578C12.2679 10.6896 12.3581 10.7534 12.4287 10.8528C12.5002 10.9535 12.5364 11.0782 12.5364 11.2234C12.5364 11.4259 12.4775 11.5765 12.3564 11.6838C12.2325 11.7936 12.0075 11.8493 11.6876 11.8493H10.7457V10.6258H11.6763Z" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_342_3730" x="0.851731" y="0.881272" width="21.341" height="21.341" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.33524"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.388235 0 0 0 0 0.640523 0 0 0 0 0.807843 0 0 0 0.51 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_342_3730"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3730" result="shape"/>
</filter>
<filter id="filter1_d_342_3730" x="6.45764" y="5.9668" width="10" height="10.4451" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.445081"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.150327 0 0 0 0 0.247059 0 0 0 0 0.145098 0 0 0 0.75 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_342_3730"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3730" result="shape"/>
</filter>
<radialGradient id="paint0_radial_342_3730" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(11.5222 11.5518) scale(8)">
<stop stop-color="#D3D3D3"/>
<stop offset="0.03" stop-color="#D3D3D3" stop-opacity="0.9216"/>
<stop offset="0.13" stop-color="#D3D3D3" stop-opacity="0.7056"/>
<stop offset="0.23" stop-color="#D3D3D3" stop-opacity="0.5329"/>
<stop offset="0.35" stop-color="#D3D3D3" stop-opacity="0.4225"/>
<stop offset="0.47" stop-color="#D3D3D3" stop-opacity="0.3364"/>
<stop offset="0.6" stop-color="#D3D3D3" stop-opacity="0.2916"/>
<stop offset="0.76" stop-color="#D3D3D3" stop-opacity="0.2601"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.5"/>
</radialGradient>
<linearGradient id="paint1_linear_342_3730" x1="11.5222" y1="19.5518" x2="11.5222" y2="3.55176" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<radialGradient id="paint2_radial_342_3730" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(11.5958 11.6252) scale(7.17695)">
<stop stop-color="#D3D3D3"/>
<stop offset="1" stop-color="#0B103B" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint3_linear_342_3730" x1="11.5958" y1="18.8022" x2="11.5958" y2="4.44824" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<clipPath id="clip0_342_3730">
<rect width="10" height="10" fill="white" transform="translate(6.45764 5.9668)"/>
</clipPath>
</defs>
</svg>

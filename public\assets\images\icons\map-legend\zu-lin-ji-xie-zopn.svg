<svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#231;&#167;&#159;&#232;&#181;&#129;&#230;&#156;&#186;&#230;&#162;&#176;">
<g id="Ellipse 15" filter="url(#filter0_d_342_3956)">
<circle cx="10.9956" cy="10.6943" r="8" fill="url(#paint0_radial_342_3956)" fill-opacity="0.7"/>
<circle cx="10.9956" cy="10.6943" r="7.55492" stroke="url(#paint1_linear_342_3956)" stroke-width="0.890162"/>
</g>
<circle id="Ellipse 16" cx="10.9958" cy="10.6941" r="7.01097" fill="url(#paint2_radial_342_3956)" stroke="url(#paint3_linear_342_3956)" stroke-width="0.200286"/>
<g id="Group 1321315401" filter="url(#filter1_d_342_3956)">
<g id="Frame" clip-path="url(#clip0_342_3956)">
<path id="Vector" d="M14.8074 13.8971C14.4388 13.8913 14.1172 13.6467 14.0133 13.2932H13.0442C13.1129 13.1692 13.1632 13.0361 13.1925 12.8979H13.9714V12.5653H13.6439L13.3516 12.2102H13.1724C13.1347 12.072 13.0761 11.9405 12.999 11.819H13.5225L13.8148 12.1742H13.9723V11.5209C13.971 11.4621 13.993 11.4053 14.0335 11.3628C14.074 11.3202 14.1297 11.2955 14.1884 11.2939H14.5469C14.6633 11.2955 14.7646 11.3743 14.7956 11.4865L15.4146 13.506H15.8091C15.9155 13.5093 15.9984 13.5981 15.9959 13.7036C15.9976 13.8092 15.9147 13.8963 15.8091 13.8996L14.8074 13.8971ZM7.15172 13.8092C6.48081 13.7757 5.96401 13.2036 5.99751 12.5327C6.0285 11.9087 6.52771 11.4103 7.15172 11.3785H11.8699C12.5408 11.412 13.0576 11.984 13.0241 12.655C12.9931 13.279 12.4939 13.7773 11.8699 13.8092H7.15172ZM11.3581 12.7798C11.3928 12.8746 11.4556 12.9567 11.538 13.015C11.6204 13.0734 11.7187 13.1052 11.8197 13.1064C11.8883 13.1027 11.9556 13.0856 12.0176 13.0559C12.0796 13.0262 12.1351 12.9845 12.181 12.9333C12.2269 12.8821 12.2623 12.8224 12.2851 12.7575C12.3079 12.6927 12.3176 12.6239 12.3138 12.5553C12.307 12.4265 12.2527 12.3047 12.1614 12.2135C12.0702 12.1223 11.9485 12.068 11.8197 12.0611C11.7187 12.0626 11.6207 12.0946 11.5383 12.1529C11.4559 12.2112 11.3931 12.2931 11.3581 12.3878H7.66601C7.63132 12.2929 7.56859 12.2109 7.48615 12.1525C7.40372 12.0942 7.30548 12.0623 7.20449 12.0611C7.13583 12.0648 7.06858 12.082 7.00657 12.1117C6.94456 12.1414 6.88902 12.183 6.84312 12.2342C6.79722 12.2854 6.76186 12.3451 6.73907 12.41C6.71628 12.4749 6.70651 12.5436 6.71031 12.6122C6.71719 12.7411 6.77147 12.8628 6.8627 12.954C6.95392 13.0453 7.07566 13.0995 7.20449 13.1064C7.3054 13.1049 7.40348 13.0729 7.48585 13.0146C7.56822 12.9563 7.63103 12.8744 7.66601 12.7798H11.3581ZM11.9017 11.2026H7.04619C6.95656 11.2026 6.86778 11.2118 6.78067 11.231V9.31211C6.78067 9.19903 6.81668 9.08931 6.88453 8.99884L7.11822 8.69145C7.16345 8.6303 7.23129 8.58926 7.30668 8.57837V7.16869C7.30393 7.04662 7.3496 6.92841 7.43372 6.8399C7.51784 6.75138 7.63357 6.69975 7.75563 6.69629H10.0917C10.1972 6.69964 10.2801 6.78675 10.2785 6.89229C10.281 6.99782 10.1972 7.08493 10.0917 7.08828H9.74074L10.2299 8.57418H11.2434V7.79857C11.2451 7.72486 11.1873 7.66455 11.1136 7.66204H11.0038C10.9531 7.66044 10.905 7.63887 10.87 7.60202C10.835 7.56516 10.816 7.516 10.817 7.4652C10.8161 7.41467 10.8351 7.36581 10.8699 7.32915C10.9047 7.29248 10.9525 7.27096 11.003 7.26921H11.4176C11.5298 7.27172 11.6186 7.36469 11.6161 7.47693V8.57251H12.7159C12.927 8.57753 13.0928 8.75259 13.0878 8.96367V10.1438H13.4747C13.5577 10.1464 13.623 10.215 13.6222 10.298V10.8994C13.6226 10.964 13.609 11.0281 13.5824 11.087C13.5558 11.146 13.5168 11.1986 13.468 11.2411L12.9203 11.7085C12.6757 11.3919 12.2996 11.2059 11.9001 11.2026H11.9017ZM7.51021 10.4847C7.5093 10.5353 7.52829 10.5841 7.56307 10.6208C7.59786 10.6575 7.64565 10.679 7.69616 10.6807H9.05893C9.08462 10.6792 9.10974 10.6725 9.13286 10.6612C9.15598 10.6499 9.17665 10.6342 9.19368 10.6149C9.21072 10.5956 9.22378 10.5731 9.23214 10.5488C9.24049 10.5245 9.24397 10.4987 9.24237 10.473C9.23956 10.4253 9.21933 10.3802 9.18552 10.3464C9.15171 10.3126 9.10666 10.2924 9.05893 10.2896H7.69616C7.64568 10.291 7.59781 10.3123 7.56298 10.3489C7.52814 10.3854 7.50918 10.4343 7.51021 10.4847ZM11.9445 9.71499V10.148C11.9445 10.2511 12.0274 10.3348 12.1304 10.3348C12.2334 10.3348 12.3172 10.2519 12.3172 10.1489V9.71415C12.3172 9.61113 12.2334 9.52737 12.1304 9.52737C12.0274 9.52737 11.9436 9.61113 11.9436 9.71415L11.9445 9.71499ZM11.2593 9.71499V10.148C11.2593 10.2511 11.3431 10.3348 11.4461 10.3348C11.5491 10.3348 11.6329 10.2511 11.6329 10.148V9.71415C11.6329 9.61113 11.5491 9.52737 11.4461 9.52737C11.3431 9.52737 11.2593 9.61113 11.2593 9.71415V9.71499ZM7.51021 9.67981C7.5093 9.73034 7.52829 9.77921 7.56307 9.81587C7.59786 9.85253 7.64565 9.87406 7.69616 9.87581H8.29253C8.31822 9.87422 8.34334 9.86759 8.36646 9.85628C8.38958 9.84498 8.41025 9.82924 8.42728 9.80995C8.44432 9.79066 8.45738 9.7682 8.46574 9.74386C8.47409 9.71952 8.47756 9.69377 8.47596 9.66809C8.47315 9.62035 8.45293 9.57531 8.41912 9.5415C8.38531 9.50769 8.34026 9.48746 8.29253 9.48465H7.69616C7.64568 9.48606 7.59781 9.50736 7.56298 9.54392C7.52814 9.58048 7.50918 9.62933 7.51021 9.67981ZM8.52706 8.57251H8.77582L9.22561 9.08344H9.662L9.90072 8.77102L9.34623 7.08745H8.52706V8.57251ZM7.68025 7.16786V8.57251H8.15433V7.08745H7.75647C7.71375 7.08745 7.68025 7.12263 7.68025 7.16534V7.16786Z" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_342_3956" x="0.325119" y="0.02385" width="21.341" height="21.341" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.33524"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.388235 0 0 0 0 0.640523 0 0 0 0 0.807843 0 0 0 0.51 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_342_3956"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3956" result="shape"/>
</filter>
<filter id="filter1_d_342_3956" x="5.99597" y="5.69531" width="9.9082" height="10.3533" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.445081"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.150327 0 0 0 0 0.247059 0 0 0 0 0.145098 0 0 0 0.75 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_342_3956"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3956" result="shape"/>
</filter>
<radialGradient id="paint0_radial_342_3956" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(10.9956 10.6943) scale(8)">
<stop stop-color="#D3D3D3"/>
<stop offset="0.03" stop-color="#D3D3D3" stop-opacity="0.9216"/>
<stop offset="0.13" stop-color="#D3D3D3" stop-opacity="0.7056"/>
<stop offset="0.23" stop-color="#D3D3D3" stop-opacity="0.5329"/>
<stop offset="0.35" stop-color="#D3D3D3" stop-opacity="0.4225"/>
<stop offset="0.47" stop-color="#D3D3D3" stop-opacity="0.3364"/>
<stop offset="0.6" stop-color="#D3D3D3" stop-opacity="0.2916"/>
<stop offset="0.76" stop-color="#D3D3D3" stop-opacity="0.2601"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.5"/>
</radialGradient>
<linearGradient id="paint1_linear_342_3956" x1="10.9956" y1="18.6943" x2="10.9956" y2="2.69434" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<radialGradient id="paint2_radial_342_3956" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(10.9958 10.6941) scale(7.11111)">
<stop stop-color="#D3D3D3"/>
<stop offset="1" stop-color="#0B103B" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint3_linear_342_3956" x1="10.9958" y1="17.8052" x2="10.9958" y2="3.58301" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<clipPath id="clip0_342_3956">
<rect width="9.90826" height="9.90826" fill="white" transform="translate(5.99597 5.69531)"/>
</clipPath>
</defs>
</svg>

<template>
  <div class="alert-marker">
    <!-- 顶部描述信息 -->
    <div class="alert-header">
      <span class="alert-icon">
        <img :src="HeaderIcon" />
      </span>
      <span class="alert-header-label">设备预警</span>
    </div>

    <div class="alert-content">
      <div class="alert-info">
        <div class="alert-info-label">设备名称：</div>
        <div class="alert-info-content">
          {{ properties?.name }}
        </div>
      </div>

      <div class="alert-chart">
        <v-chart class="chart" :option="chartOption" autoresize />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from "vue";
import HeaderIcon from "@/assets/modules/common/icons/yujing-card-icon-2.svg";
import { use } from "echarts/core";
import { CanvasRenderer } from "echarts/renderers";
import { LineChart } from "echarts/charts";
import {
  GridComponent,
  TooltipComponent,
  TitleComponent,
  LegendComponent,
} from "echarts/components";
import VChart from "vue-echarts";

// 注册必须的组件
use([
  CanvasRenderer,
  LineChart,
  GridComponent,
  TooltipComponent,
  TitleComponent,
  LegendComponent,
]);

// 定义组件名称
defineOptions({
  name: "AlertMarker",
});

// 定义props
const props = defineProps({
  properties: {
    type: Object,
    default: () => ({}),
  },
});

// 图表配置
const generateRandomData = () => {
  return Array(5).fill(0).map(() => Math.floor(Math.random() * 50) + 30);
};

const chartOption = ref({
  tooltip: {
    trigger: "axis",
  },
  grid: {
    top: "15%",
    left: "0%",
    right: "0%",
    bottom: "5%",
    containLabel: true,
  },
  xAxis: {
    type: "category",
    data: ["0:00", "6:00", "12:00", "18:00", "24:00"],
    axisLabel: {
      // 坐标轴字体颜色
      color: "rgba(255, 255, 255, 0.6)",
      fontSize: 14,
    },
    axisLine: {
      lineStyle: {
        color: "rgba(255, 255, 255, 0.6)",
      },
    },
    axisTick: {
      // x轴刻度线
      show: true,
    },
    splitLine: {
      // 网格
      show: false,
    },
    boundaryGap: false,
    // 设置两侧padding
    offset: 10,
    // 设置x轴两侧留白
    boundaryGap: ["10%", "10%"],
  },
  yAxis: {
    type: "value",
    min: 0,
    name: "单位（个）",
    nameTextStyle: {
      color: "rgba(255, 255, 255, 0.6)",
      fontSize: 16,
      padding: [0, 0, 0, 80],
    },
    axisLabel: {
      // 坐标轴字体颜色
      color: "rgba(255, 255, 255, 0.6)",
      fontSize: 14,
    },
    axisLine: {
      show: true,
      lineStyle: {
        color: "rgba(255, 255, 255, 0.6)",
      },
    },
    axisTick: {
      // y轴刻度线
      show: false,
    },
    splitLine: {
      // 网格
      show: true,
      lineStyle: {
        color: "rgba(255, 255, 255, 0.2)",
        type: "dashed",
      },
    },
  },
  series: [
    {
      name: "单位（个）",
      type: "line",
      symbol: "circle",
      symbolSize: 10,
      z: 1,
      // 修改为红色折线
      itemStyle: {
        color: "#fff", // 数据点为白色
        borderColor: "#d93526", // 边框为红色
        borderWidth: 2,
        shadowColor: "rgba(255, 255, 255, 0.8)",
        shadowBlur: 8,
      },
      lineStyle: {
        color: "#d93526", // 折线为红色
        width: 3,
      },
      // 使用随机生成的数据
      data: generateRandomData(),
    },
  ],
});
</script>

<style scoped lang="scss">
.alert-marker {
  display: flex;
  flex-direction: column;
  width: 516px;
  height: 384px;
  background: rgba(17, 20, 43, 0.74);
  backdrop-filter: blur(8px);
  box-shadow: 0px 6px 16px 0px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  border: 1px solid #d93526;
  box-sizing: border-box;
}

.alert-header {
  position: relative; /* 添加相对定位 */
  display: flex;
  align-items: center;
  padding: 0 12px;
  height: 32px;
  background: rgba(17, 20, 43, 0.74);
  box-shadow: 0px 5px 12px 0px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid #d93526;
  &::before {
    content: "";
    position: absolute;
    left: 4px;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 100%;
    background: #d93526;
  }
  &-label {
    font-size: 18px;
    font-family: YouSheBiaoTiHei;
    color: #fff;
  }
}

.alert-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 6px;
  img {
    width: 100%;
    height: 100%;
  }
}

.alert-content {
  flex: 1;
  overflow: hidden;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  .alert-info {
    display: flex;
    font-size: 18px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    gap: 12px;
    color: #ffffff;
    &-label {
      color: rgba(255, 255, 255, 0.6);
    }
  }

  .alert-chart {
    flex: 1;
    width: 100%;
    height: 250px;
    margin-top: 10px;
    display: flex;

    .chart {
      width: 100% !important;
      height: 100% !important;
      flex: 1;
    }
  }

  .alert-image {
    flex: 1;
    display: flex;
    align-items: center;
    width: 100%;
    // height: 200px;
    overflow: hidden;
  }
}
</style>

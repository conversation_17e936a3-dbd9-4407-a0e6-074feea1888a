<template>
	<div ref="markerRef" style="width: 100%; height: 100%">
		<div class="division-marker" @click="onClickIcon">
			{{ props.properties["index"] }}
			<template v-if="isExpanded">
				<div class="wave wave1"></div>
				<div class="wave wave2"></div>
			</template>
		</div>
		<MarkerPanel v-if="isExpanded" :title="props.properties['name']" anchor="right-bottom">
			<template #content>
				<div class="data-list" v-if="formData && formData.length">
					<div v-for="(item, index) in formData" :key="index" class="data-item">
						<span class="data-key">{{ item.label }}:</span>
						<span class="data-value">{{ item.value }} {{ item.unit }}</span>
					</div>
				</div>
				<div v-else class="no-data">暂无数据</div>
			</template>
		</MarkerPanel>
	</div>
</template>

<script setup>
import request from "@/utils/request";
import emitter from "@/utils/emitter";
import { v4 } from "uuid";
import MarkerPanel from "./MarkerPanel.vue";

const props = defineProps({
	properties: {
		type: Object,
		required: true,
	},
});

const markerId = ref(v4());
const markerRef = ref(null);
const isExpanded = ref(false);

const formData = ref([
	{ key: "roadLength", label: "公路里程", unit: "km", value: "" },
	{ key: "segmentation", label: "分管段落", value: "" },
	{ key: "managerName", label: "工区主任", value: "" },
	{ key: "averageSpeedG", label: "平均通过速度(格尔木方向)", unit: "km/h", value: "" },
	{ key: "averageSpeedN", label: "平均通过速度(那曲方向)", unit: "km/h", value: "" },
]);

onMounted(() => {
	document.addEventListener("click", handleClickOutside);
	emitter.$on("marker-expand", handleOtherMarkerExpand);
	getDetail();
});

onUnmounted(() => {
	document.removeEventListener("click", handleClickOutside);
	emitter.$off("marker-expand", handleOtherMarkerExpand);
});

watch(
	() => isExpanded.value,
	(val) => {
		const parent = markerRef.value.parentElement;
		parent.style["z-index"] = val ? 1 : 0;
	}
);

const getDetail = () => {
	request
		.get("/api/screen/staff/center/workarea/detail", {
			workAreaId: props.properties.id,
		})
		.then((res) => {
			const data = res.data || {};
			unref(formData).forEach((item) => {
				item.value = data[item.key];
			});
		});
};

const onClickIcon = () => {
	if (!isExpanded.value) {
		emitter.$emit("marker-expand", markerId.value);
	}
	isExpanded.value = !isExpanded.value;
};

const handleClickOutside = (event) => {
	if (isExpanded.value && markerRef.value && !markerRef.value.contains(event.target)) {
		isExpanded.value = false;
	}
};

const handleOtherMarkerExpand = (id) => {
	if (id !== markerId.value && isExpanded.value) {
		isExpanded.value = false;
	}
};
</script>

<style scoped lang="scss">
.division-marker {
	width: 100%;
	height: 100%;
	background-color: #00a6ff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-family: "D-DIN-PRO";
	font-weight: 800;
	color: #ffffff;
	cursor: pointer;
	position: relative;

	&::after {
		content: "";
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 450%;
		height: 450%;
		background: rgba(91, 222, 255, 0.1);
		border-radius: 50%;
		box-shadow: inset 0px 0px 20px 0px rgba(73, 255, 189, 0.16);
		border: 2px solid rgba(40, 151, 255, 0.2);
	}

	.wave {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 450%;
		height: 450%;
		border-radius: 50%;
	}

	.wave1 {
		background: rgba(216, 232, 255, 0.12);
		z-index: 3;
		animation: w1 2s 0.2s infinite;

		@keyframes w1 {
			0% {
				width: 450%;
				height: 450%;
				opacity: 1;
			}
			100% {
				width: 900%;
				height: 900%;
				opacity: 0;
			}
		}
	}

	.wave2 {
		background: rgba(216, 232, 255, 0.06);
		z-index: 3;
		animation: w2 2s 0.2s infinite;

		@keyframes w2 {
			0% {
				width: 450%;
				height: 450%;
				opacity: 1;
			}
			100% {
				width: 1800%;
				height: 1800%;
				opacity: 0;
			}
		}
	}
}

.data-key {
	width: 78px;
}

.marker-panel {
	width: 260px;
}
</style>

<template>
  <div ref="markerRef" class="project-marker">
    <div class="marker-content">
      <div class="marker-panel">
        <div class="panel-header">
          <span class="panel-header-label">{{ label }}</span>
          <div class="header-btn">
            <div
              class="header-btn-item"
              :class="{ disabled: !canUseUnrealEngine }"
              @click.stop="handleUnrealEngineClick"
              title="虚幻引擎"
            >
              <img
                src="@/assets/modules/common/icons/unreal-engine.svg"
                alt="虚幻"
              />
            </div>
            <div
              class="header-btn-item"
              :class="{ disabled: !canUseBimEngine }"
              @click.stop="handleBimEngineClick"
              title="BIM模型"
            >
              <img
                src="@/assets/modules/common/icons/bim-engine.svg"
                alt="BIM模型"
              />
            </div>
            <div class="header-btn-item" @click.stop="handleClose">
              <img
                src="@/assets/modules/common/icons/expand-icon.svg"
                alt="关闭"
              />
            </div>
          </div>
        </div>
        <div class="panel-content">
          <div class="form-data-list" v-if="formData && formData.length">
            <div
              v-for="(item, index) in formData"
              :key="index"
              class="form-data-item"
            >
              <span class="form-data-key">{{ item.key }}：</span>
              <span class="form-data-value">{{ item.value }}</span>
            </div>
          </div>
          <div v-else class="no-data">暂无数据</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from "vue";
import emitter from "@/utils/emitter";

// 添加 markerRef 引用
const markerRef = ref(null);

const props = defineProps({
  label: {
    type: String,
    default: "项目部",
  },
  properties: {
    type: Object,
    default: () => ({}),
  },
  engineType: {
    type: String,
    default: "maplibre",
  },
});

const emit = defineEmits(["close"]);

// 尝试解析JSON字符串
const tryParseJSON = (str) => {
  if (typeof str !== "string") return str;

  try {
    return JSON.parse(str);
  } catch (error) {
    console.error("解析JSON字符串失败:", error);
    return str;
  }
};

// 处理properties中的数据，将字符串类型的JSON转换为对象或数组
const processedProperties = computed(() => {
  const result = { ...props.properties };

  // 需要特殊处理的字段列表
  const jsonFields = ["formData", "engines"];

  // 只处理特定字段，避免不必要的解析
  jsonFields.forEach((field) => {
    if (field in result && typeof result[field] === "string") {
      result[field] = tryParseJSON(result[field]);
    }
  });

  // 处理嵌套对象中的特定字段
  if (result.data && typeof result.data === "object") {
    jsonFields.forEach((field) => {
      if (field in result.data && typeof result.data[field] === "string") {
        result.data[field] = tryParseJSON(result.data[field]);
      }
    });
  }

  console.log("处理后的properties:", result);
  return result;
});

// 从处理后的properties中提取formData
const formData = computed(() => {
  console.log("处理后的properties", processedProperties.value);

  // 获取formData
  let data = processedProperties.value?.formData;

  // 确保返回数组
  return Array.isArray(data) ? data : [];
});

// 判断是否可以使用BIM引擎
const canUseBimEngine = computed(() => {
  const engines = processedProperties.value?.engines;
  return engines && engines.bim === true;
});

// 判断是否可以使用虚幻引擎
const canUseUnrealEngine = computed(() => {
  const engines = processedProperties.value?.engines;
  return engines && engines.unreal === true;
});

// 处理虚幻引擎点击事件
const handleUnrealEngineClick = () => {
  if (canUseUnrealEngine.value) {
    // 切换到虚幻引擎并设置相机位置
    let position;

    // 检查position是否已经是对象或需要解析
    if (typeof processedProperties.value.position === "string") {
      try {
        position = JSON.parse(processedProperties.value.position);
      } catch (error) {
        console.error("解析position失败:", error);
        position = { unreal: {} }; // 提供默认值
      }
    } else if (typeof processedProperties.value.position === "object") {
      position = processedProperties.value.position;
    }

    emitter.$emit("switchEngine", {
      targetEngine: "unreal",
      callback: (engineRef) => {
        // 获取虚幻引擎实例
        const unrealEngine = engineRef.getEngineInstance("unreal");

        // 调用引擎内的方法，例如设置相机位置
        if (position && position.unreal) {
          unrealEngine.setCameraPosition(position.unreal);
        } else {
          console.warn("未找到有效的虚幻引擎相机位置");
        }
      },
    });
  }
};

// 处理BIM引擎点击事件
const handleBimEngineClick = () => {
  if (canUseBimEngine.value) {
    console.log("打开BIM引擎", processedProperties.value);
  }
};

// 处理点击外部事件
const handleClickOutside = (event) => {
  if (markerRef.value && !markerRef.value.contains(event.target)) {
    handleClose();
  }
};

// 组件挂载时添加点击事件监听
onMounted(() => {
  document.addEventListener("click", handleClickOutside);
});

// 组件卸载时移除点击事件监听
onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
});

// 关闭回调函数
const closeCallback = ref(null);

// 设置关闭回调的方法
const setCloseCallback = (callback) => {
  closeCallback.value = callback;
};

// 处理关闭事件
const handleClose = () => {
  if (closeCallback.value) {
    closeCallback.value();
  }

  emit("close");
};

// 暴露方法给父组件
defineExpose({
  setCloseCallback,
});
</script>

<style scoped lang="scss">
.project-marker {
  min-width: 230px; /* 修改最小宽度为230px */
  width: auto; /* 添加自适应宽度 */
  max-width: 350px; /* 添加最大宽度限制 */
  cursor: pointer;
}

.marker-panel {
  width: 100%; /* 修改为100%以适应父容器 */
  background: rgba(19, 51, 92, 0.6);
  backdrop-filter: blur(8px);
  box-shadow: 0px 5px 12px 0px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  border: 1px solid #58a1e7;

  .panel-header {
    position: relative; /* 添加相对定位 */
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px;
    width: 100%;
    height: 29px;
    background: rgba(19, 51, 92, 0.55);
    border-radius: 5px;
    border: 1px solid #58a1e7;
    box-sizing: border-box;

    &::before {
      content: "";
      position: absolute;
      left: 4px;
      top: 50%;
      transform: translateY(-50%);
      width: 1px;
      height: 100%;
      background: #58a1e7;
    }

    .header-btn {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-left: 10px;
      flex-shrink: 0; /* 防止按钮区域被压缩 */
      &-item {
        height: 14px;
        width: 14px;
        cursor: pointer;
        img {
          height: 100%;
          width: 100%;
        }

        &.disabled {
          opacity: 0.4;
          cursor: not-allowed;
          filter: grayscale(100%);
        }
      }
    }
  }
}

.panel-header-label {
  font-size: 16px;
  font-family: YouSheBiaoTiHei;
  color: #fff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1; /* 让标题文本占据剩余空间 */
  margin-right: 40px; /* 确保与按钮区域至少有40px的间距 */
}

.panel-content {
  padding: 12px;
  height: calc(100% - 37px);
  overflow-y: auto;
}

/* 添加表单数据样式 */
.form-data-list {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.form-data-item {
  margin-bottom: 8px;
  display: flex;
  align-items: flex-start; /* 修改为flex-start以便于长文本换行时对齐 */
  font-size: 12px;
  color: #ffffff;
  &:last-child {
    margin-bottom: 0;
  }
}

.form-data-key {
  min-width: 60px;
  flex-shrink: 0; /* 防止键名被压缩 */
}

.form-data-value {
  word-break: break-word; /* 允许长文本在任意位置换行 */
  flex: 1; /* 让值占据剩余空间 */
}

.no-data {
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  padding: 20px 0;
}
</style>

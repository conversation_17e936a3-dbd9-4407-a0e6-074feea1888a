<svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#231;&#137;&#185;&#229;&#164;&#167;&#230;&#161;&#165;">
<g id="Ellipse 15" filter="url(#filter0_d_342_3789)">
<ellipse cx="10.9651" cy="10.9253" rx="8" ry="7.93796" fill="url(#paint0_radial_342_3789)" fill-opacity="0.7"/>
<path d="M18.52 10.9253C18.52 15.0602 15.1408 18.4181 10.9651 18.4181C6.78935 18.4181 3.41017 15.0602 3.41017 10.9253C3.41017 6.79032 6.78935 3.43239 10.9651 3.43239C15.1408 3.43239 18.52 6.79032 18.52 10.9253Z" stroke="url(#paint1_linear_342_3789)" stroke-width="0.890162"/>
</g>
<path id="Ellipse 16" d="M17.976 10.9261C17.976 14.7669 14.8378 17.8819 10.965 17.8819C7.09221 17.8819 3.95403 14.7669 3.95403 10.9261C3.95403 7.08522 7.09221 3.97026 10.965 3.97026C14.8378 3.97026 17.976 7.08522 17.976 10.9261Z" fill="url(#paint2_radial_342_3789)" stroke="url(#paint3_linear_342_3789)" stroke-width="0.200286"/>
<g id="Group 1321315401" filter="url(#filter1_d_342_3789)">
<g id="Frame" clip-path="url(#clip0_342_3789)">
<path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M12.2173 7.09416V6.65332H12.4979V7.09077L12.4982 7.09073C12.6404 8.2436 14.5727 10.9443 16.1012 11.5372H16.1783C16.2478 11.5372 16.3042 11.5931 16.3042 11.6621V12.091C16.3042 12.16 16.2479 12.2159 16.1784 12.2159H6.02639C5.95692 12.2159 5.90051 12.1601 5.90051 12.091V11.6621C5.90051 11.5932 5.9568 11.5372 6.02639 11.5372H6.07384C6.46634 11.4055 7.26135 10.7035 8.04067 9.79577C8.8814 8.81656 9.5102 7.83628 9.70375 7.20777V6.65332H9.98435V7.09416H10.4275V6.98131C10.4275 6.96708 10.4332 6.95344 10.4433 6.94338C10.4535 6.93333 10.4672 6.92768 10.4816 6.92768C10.4959 6.92768 10.5096 6.93333 10.5198 6.94338C10.5299 6.95344 10.5356 6.96708 10.5356 6.98131V7.09416H11.0468V6.98353C11.0468 6.9693 11.0525 6.95566 11.0626 6.9456C11.0727 6.93555 11.0865 6.9299 11.1008 6.9299C11.1152 6.9299 11.1289 6.93555 11.139 6.9456C11.1492 6.95566 11.1549 6.9693 11.1549 6.98353V7.09416H11.666V6.98353C11.666 6.9693 11.6717 6.95566 11.6819 6.9456C11.692 6.93555 11.7057 6.9299 11.7201 6.9299C11.7344 6.9299 11.7482 6.93555 11.7583 6.9456C11.7684 6.95566 11.7741 6.9693 11.7741 6.98353V7.09416H12.2173ZM7.07328 11.5372H9.70375V7.95681C9.39099 8.55036 8.89013 9.26259 8.26763 9.98766C7.90684 10.4078 7.48173 10.8552 7.08099 11.2065C7.6542 10.9318 8.4024 10.5041 9.2103 9.95627C9.31204 9.88733 9.41189 9.81828 9.50974 9.74935L9.50986 10.1107C9.46335 10.1429 9.41943 10.1729 9.37857 10.2006C8.62978 10.7083 7.7603 11.221 7.07328 11.5372ZM12.4979 9.98309V8.03515C12.7417 8.56041 13.1173 9.16529 13.5717 9.74824C14.1654 10.5099 14.798 11.1154 15.3871 11.496C14.5645 11.2722 13.525 10.7327 12.4979 9.98309ZM11.7906 9.81245L11.7906 11.5372H10.3701V8.85446C10.3701 8.73362 10.4008 8.61987 10.4548 8.52047C10.8319 8.97226 11.3087 9.42012 11.7906 9.81245ZM11.7906 9.42799C11.3369 9.04404 10.9496 8.65664 10.6518 8.29238C10.7709 8.20282 10.9194 8.14968 11.0804 8.14968C11.4726 8.14968 11.7907 8.46515 11.7907 8.85446L11.7906 9.42799ZM12.4979 10.3477C13.1314 10.7931 13.8828 11.2376 14.6068 11.5372H12.4979V10.3477ZM9.89892 14.533L12.3702 14.1576C12.4063 14.15 12.4388 14.1302 12.462 14.1017C12.4853 14.0732 12.498 14.0376 12.4981 14.001V12.5464C12.4981 12.4875 12.45 12.4398 12.3907 12.4398H9.81119C9.75184 12.4398 9.7038 12.4875 9.7038 12.5464V14.3764C9.7038 14.4784 9.79847 14.5543 9.89892 14.533Z" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_342_3789" x="0.294602" y="0.316819" width="21.341" height="21.2169" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.33524"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.388235 0 0 0 0 0.640523 0 0 0 0 0.807843 0 0 0 0.51 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_342_3789"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3789" result="shape"/>
</filter>
<filter id="filter1_d_342_3789" x="5.96521" y="5.47168" width="10" height="10.3679" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.445081"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.150327 0 0 0 0 0.247059 0 0 0 0 0.145098 0 0 0 0.75 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_342_3789"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3789" result="shape"/>
</filter>
<radialGradient id="paint0_radial_342_3789" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(10.9651 10.9253) scale(8 7.93796)">
<stop stop-color="#D3D3D3"/>
<stop offset="0.03" stop-color="#D3D3D3" stop-opacity="0.9216"/>
<stop offset="0.13" stop-color="#D3D3D3" stop-opacity="0.7056"/>
<stop offset="0.23" stop-color="#D3D3D3" stop-opacity="0.5329"/>
<stop offset="0.35" stop-color="#D3D3D3" stop-opacity="0.4225"/>
<stop offset="0.47" stop-color="#D3D3D3" stop-opacity="0.3364"/>
<stop offset="0.6" stop-color="#D3D3D3" stop-opacity="0.2916"/>
<stop offset="0.76" stop-color="#D3D3D3" stop-opacity="0.2601"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.5"/>
</radialGradient>
<linearGradient id="paint1_linear_342_3789" x1="10.9651" y1="18.8632" x2="10.9651" y2="2.9873" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<radialGradient id="paint2_radial_342_3789" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(10.965 10.9261) scale(7.11111 7.05596)">
<stop stop-color="#D3D3D3"/>
<stop offset="1" stop-color="#0B103B" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint3_linear_342_3789" x1="10.965" y1="17.982" x2="10.965" y2="3.87012" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<clipPath id="clip0_342_3789">
<rect width="10" height="9.92245" fill="white" transform="translate(5.96521 5.47168)"/>
</clipPath>
</defs>
</svg>

<template>
	<Modal ref="ModalRef" title="基层党建">
		<div class="tab">
			<template v-for="tab in tabList" :key="tab.value">
				<div
					class="tab-item"
					:class="{ 'is-active': tab.value === activeTab }"
					@click="onClickTab(tab)"
				>
					{{ tab.label }}
				</div>
			</template>
		</div>
		<div class="news">
			<template v-for="item in data" :key="item.value">
				<div class="news-item">
					<div class="news-item-time">「{{ item.publishDate }}」</div>
					<div class="news-item-desc">
						{{ item.abstractInfo }}
					</div>
					<div class="news-item-time"></div>
					<div class="news-item-link" @click="onInfo(item)">查看详情 <icon-double-right /></div>
				</div>
			</template>
		</div>
	</Modal>
	<ParagraphModal ref="ParagraphModalRef" :mask="false" />
</template>

<script setup>
import { usePartyBuildingStore } from "@/store";
import request from "@/utils/request";
import Modal from "./Modal.vue";
import ParagraphModal from "./ParagraphModal.vue";

const PartyBuildingStore = usePartyBuildingStore();
const tabList = [
	{ label: "三会一课", value: "三会一课" },
	{ label: "党组织建设", value: "党组织建设" },
	{ label: "身边的榜样", value: "身边的榜样" },
];
const ModalRef = ref(null);
const ParagraphModalRef = ref(null);
const activeTab = ref("三会一课");
const data = ref([]);

const onOpen = () => {
	ModalRef.value?.open();
	getData();
};

const getData = () => {
	const params = {
		type: "基层党建",
		department: PartyBuildingStore.getDepartment,
		grassrootType: unref(activeTab),
	};
	request.get("/api/screen/dangjian/article/list", params).then((res) => {
		if (res.code == 200) {
			data.value = res.data;
		}
	});
};

const onClickTab = (tab) => {
	activeTab.value = tab.value;
	getData();
};

const onInfo = (item) => {
	ParagraphModalRef.value.open(item.id);
};

defineExpose({
	open: onOpen,
});
</script>

<style lang="scss" scoped>
.news {
	margin-top: 12px;
}
</style>

<svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#233;&#151;&#178;&#231;&#189;&#174;&#230;&#156;&#186;&#230;&#162;&#176;">
<g id="Ellipse 15" filter="url(#filter0_d_342_3980)">
<circle cx="11.0266" cy="10.8789" r="8" fill="url(#paint0_radial_342_3980)" fill-opacity="0.7"/>
<circle cx="11.0266" cy="10.8789" r="7.55492" stroke="url(#paint1_linear_342_3980)" stroke-width="0.890162"/>
</g>
<circle id="Ellipse 16" cx="11.0263" cy="10.8787" r="7.01097" fill="url(#paint2_radial_342_3980)" stroke="url(#paint3_linear_342_3980)" stroke-width="0.200286"/>
<g id="Group 1321315401" filter="url(#filter1_d_342_3980)">
<g id="Frame" clip-path="url(#clip0_342_3980)">
<path id="Vector" d="M11.5795 15.1846H12.127C12.1881 15.1846 12.2289 15.1337 12.2289 15.073V13.891C12.2289 13.8604 12.259 13.8303 12.2896 13.8303H14.8458C14.8764 13.8303 14.9065 13.8604 14.9065 13.891V15.073C14.9065 15.1337 14.9574 15.1846 15.0181 15.1846H15.5559C15.5959 15.1836 15.634 15.1672 15.6623 15.1388C15.6906 15.1105 15.7069 15.0723 15.7078 15.0322V13.3129C15.7069 13.2729 15.6906 13.2347 15.6623 13.2063C15.634 13.1779 15.5959 13.1616 15.5559 13.1606H11.5795C11.5394 13.1615 11.5012 13.1778 11.4728 13.2062C11.4444 13.2346 11.4281 13.2728 11.4272 13.3129V15.0424C11.4373 15.1235 11.4984 15.1846 11.5795 15.1846ZM8.47536 10.432V6.73959C8.47536 6.57706 8.47536 6.56727 8.57718 6.70904L11.417 11.1115C11.4272 11.1319 11.4373 11.1522 11.4373 11.1726V12.7039C11.4373 12.765 11.4883 12.8057 11.5392 12.8057H15.5864C15.6133 12.8056 15.6391 12.7949 15.6582 12.7758C15.6772 12.7567 15.6878 12.7308 15.6878 12.7039V10.929L14.744 7.93684C14.7342 7.9059 14.7138 7.87574 14.6735 7.86556C14.6429 7.85538 14.6022 7.86556 14.5717 7.88593L13.3243 9.06242C13.3067 9.07882 13.284 9.08876 13.2601 9.09061C13.2361 9.09246 13.2122 9.08611 13.1923 9.0726L8.58697 6.26257C8.42483 6.16113 8.29285 6.13098 8.29285 6.34403V10.432C8.29285 10.6247 8.29285 10.8479 8.46517 10.9595C8.57679 11.0406 8.52627 11.2333 8.37392 11.2333C8.33387 11.2323 8.29573 11.2159 8.26744 11.1875C8.23914 11.1592 8.22286 11.121 8.22196 11.0809C8.23215 10.8883 7.98855 10.9086 8.02928 11.1319C8.02951 11.1424 8.02677 11.1527 8.02137 11.1617C8.01596 11.1707 8.00811 11.178 7.99873 11.1828L6.599 11.9535C6.55827 11.9739 6.55827 11.9841 6.51754 11.9841C6.47227 11.9854 6.42923 12.004 6.39721 12.0361C6.36519 12.0681 6.34659 12.1111 6.34521 12.1564V14.7025C6.34521 14.8039 6.42628 14.885 6.51754 14.885H10.3114C10.4026 14.885 10.4837 14.8039 10.4837 14.7126V12.1564C10.4837 12.0546 10.4026 12.004 10.3114 11.9841L10.2303 11.9535L8.76948 11.142C8.74951 11.1319 8.73932 11.1217 8.73932 11.1013C8.73847 11.0528 8.72715 11.005 8.70613 10.9613C8.6851 10.9176 8.65488 10.8789 8.61752 10.8479C8.5059 10.7261 8.47536 10.6043 8.47536 10.432ZM14.3284 9.09258C14.3692 9.05224 14.4401 9.0726 14.4604 9.12352L14.8356 10.2291C14.856 10.28 14.8153 10.3306 14.7546 10.3306L13.2025 10.3407C12.9589 10.3509 12.9996 10.3407 13.1418 10.2088L14.3284 9.09258ZM8.15069 11.3856C8.21955 11.4441 8.30791 11.4746 8.39819 11.4709C8.48847 11.4672 8.57405 11.4296 8.63789 11.3657C8.65786 11.3453 8.67823 11.3453 8.69859 11.3555L9.69258 11.9234C9.71295 11.9335 9.70276 11.9743 9.67222 11.9743H7.13633C7.10618 11.9743 7.09599 11.9335 7.11597 11.9234L8.0798 11.3657C8.12053 11.3657 8.1405 11.3657 8.15069 11.3856Z" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_342_3980" x="0.356125" y="0.20842" width="21.341" height="21.341" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.33524"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.388235 0 0 0 0 0.640523 0 0 0 0 0.807843 0 0 0 0.51 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_342_3980"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3980" result="shape"/>
</filter>
<filter id="filter1_d_342_3980" x="6.02637" y="5.87891" width="10" height="10.4451" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.445081"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.150327 0 0 0 0 0.247059 0 0 0 0 0.145098 0 0 0 0.75 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_342_3980"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3980" result="shape"/>
</filter>
<radialGradient id="paint0_radial_342_3980" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(11.0266 10.8789) scale(8)">
<stop stop-color="#D3D3D3"/>
<stop offset="0.03" stop-color="#D3D3D3" stop-opacity="0.9216"/>
<stop offset="0.13" stop-color="#D3D3D3" stop-opacity="0.7056"/>
<stop offset="0.23" stop-color="#D3D3D3" stop-opacity="0.5329"/>
<stop offset="0.35" stop-color="#D3D3D3" stop-opacity="0.4225"/>
<stop offset="0.47" stop-color="#D3D3D3" stop-opacity="0.3364"/>
<stop offset="0.6" stop-color="#D3D3D3" stop-opacity="0.2916"/>
<stop offset="0.76" stop-color="#D3D3D3" stop-opacity="0.2601"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.5"/>
</radialGradient>
<linearGradient id="paint1_linear_342_3980" x1="11.0266" y1="18.8789" x2="11.0266" y2="2.87891" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<radialGradient id="paint2_radial_342_3980" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(11.0263 10.8787) scale(7.11111)">
<stop stop-color="#D3D3D3"/>
<stop offset="1" stop-color="#0B103B" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint3_linear_342_3980" x1="11.0263" y1="17.9898" x2="11.0263" y2="3.76758" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<clipPath id="clip0_342_3980">
<rect width="10" height="10" fill="white" transform="translate(6.02637 5.87891)"/>
</clipPath>
</defs>
</svg>

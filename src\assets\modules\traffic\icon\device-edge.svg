<svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#232;&#190;&#185;&#231;&#188;&#152;&#232;&#174;&#161;&#231;&#174;&#151;1">
<g id="&#232;&#190;&#185;&#231;&#188;&#152;&#232;&#174;&#161;&#231;&#174;&#151;1_2">
<g id="Vector" filter="url(#filter0_dddi_1143_3706)">
<path d="M3.77172 8.74439C3.91276 8.74498 4.04904 8.79547 4.15641 8.88693C4.26379 8.97839 4.33531 9.10491 4.35833 9.24406L4.36671 9.34043V14.3559L10.0914 17.6608C10.3501 17.4649 10.657 17.3288 11.0058 17.3288C11.3123 17.3286 11.612 17.4193 11.867 17.5894C12.1219 17.7596 12.3206 18.0015 12.438 18.2847C12.5554 18.5678 12.5862 18.8794 12.5264 19.18C12.4667 19.4807 12.3191 19.7568 12.1024 19.9735C11.8857 20.1903 11.6095 20.3378 11.3089 20.3976C11.0083 20.4573 10.6967 20.4266 10.4135 20.3092C10.1304 20.1918 9.88844 19.9931 9.71828 19.7381C9.54813 19.4832 9.45741 19.1835 9.45762 18.877C9.45762 18.834 9.47019 18.7932 9.48171 18.7544L9.49428 18.6926L3.47318 15.2149C3.39807 15.1717 3.33335 15.1125 3.28358 15.0415C3.23381 14.9706 3.2002 14.8896 3.18511 14.8043L3.17464 14.6995V9.34043C3.17464 9.01151 3.44175 8.74439 3.77172 8.74439ZM19.5839 5.56938C19.7893 5.92515 19.845 6.34791 19.7387 6.7447C19.6324 7.1415 19.3729 7.47985 19.0172 7.68536C18.9795 7.70736 18.9387 7.71678 18.8978 7.72726L18.8381 7.74611V14.6995C18.8381 14.7863 18.8191 14.872 18.7826 14.9507C18.746 15.0294 18.6928 15.0992 18.6265 15.1552L18.5406 15.2159L13.9001 17.8955C13.7713 17.9731 13.6178 17.9991 13.4706 17.9682C13.3234 17.9373 13.1934 17.8518 13.1067 17.7288C13.02 17.6059 12.9831 17.4547 13.0034 17.3056C13.0237 17.1566 13.0997 17.0207 13.2161 16.9255L13.3041 16.8626L17.6471 14.357V7.74507C17.3475 7.61727 17.0762 7.42034 16.9012 7.11761C16.6958 6.76172 16.6401 6.3388 16.7465 5.94188C16.8529 5.54496 17.1126 5.20655 17.4685 5.00111C17.8244 4.79566 18.2473 4.74 18.6442 4.84638C19.0411 4.95275 19.3795 5.21245 19.585 5.56833L19.5839 5.56938ZM5.55144 8.06037L10.4098 10.8656V16.4771L6.14643 14.0155C5.98606 13.9227 5.84954 13.7939 5.7477 13.6391C5.64587 13.4844 5.58154 13.308 5.55982 13.124L5.55144 12.9837V8.06037ZM16.4613 8.06037V12.9837C16.4613 13.3608 16.2822 13.7138 15.9826 13.9369L15.8653 14.0155L11.6019 16.4771V10.8656L16.4613 8.06037ZM11.4751 4.50301L11.6019 4.56586L15.8653 7.02752L11.0058 9.83486L6.14748 7.02752L10.4109 4.56586C10.7377 4.37731 11.1336 4.35636 11.4762 4.50301H11.4751ZM11.2101 1.60664L11.3044 1.65063L15.9459 4.32913C16.0137 4.36827 16.073 4.4204 16.1206 4.48251C16.1682 4.54462 16.203 4.61551 16.2231 4.69111C16.2433 4.76671 16.2483 4.84554 16.238 4.92309C16.2276 5.00064 16.202 5.07538 16.1628 5.14304C16.0941 5.26439 15.9847 5.35749 15.8539 5.40581C15.7231 5.45414 15.5794 5.45457 15.4483 5.40702L15.3499 5.36093L11.0069 2.85423L5.28223 6.15913C5.32099 6.48072 5.28538 6.81592 5.11149 7.11865C4.95836 7.38373 4.73007 7.59747 4.4555 7.73284C4.18092 7.86821 3.87238 7.91914 3.56887 7.87919C3.26535 7.83925 2.98049 7.71022 2.75029 7.50842C2.52009 7.30662 2.35488 7.0411 2.27555 6.74543C2.19621 6.44975 2.20632 6.1372 2.30458 5.84726C2.40284 5.55733 2.58484 5.30304 2.8276 5.11652C3.07035 4.93 3.36294 4.81964 3.6684 4.79937C3.97386 4.77911 4.27847 4.84986 4.54374 5.00268C4.58249 5.02363 4.61078 5.05505 4.64011 5.08438L4.68724 5.12733L10.7094 1.64959C10.7848 1.6067 10.8687 1.58075 10.9551 1.57351C11.0416 1.56627 11.1286 1.57794 11.2101 1.60769V1.60664Z" fill="#7699B9"/>
<path d="M3.77172 8.74439C3.91276 8.74498 4.04904 8.79547 4.15641 8.88693C4.26379 8.97839 4.33531 9.10491 4.35833 9.24406L4.36671 9.34043V14.3559L10.0914 17.6608C10.3501 17.4649 10.657 17.3288 11.0058 17.3288C11.3123 17.3286 11.612 17.4193 11.867 17.5894C12.1219 17.7596 12.3206 18.0015 12.438 18.2847C12.5554 18.5678 12.5862 18.8794 12.5264 19.18C12.4667 19.4807 12.3191 19.7568 12.1024 19.9735C11.8857 20.1903 11.6095 20.3378 11.3089 20.3976C11.0083 20.4573 10.6967 20.4266 10.4135 20.3092C10.1304 20.1918 9.88844 19.9931 9.71828 19.7381C9.54813 19.4832 9.45741 19.1835 9.45762 18.877C9.45762 18.834 9.47019 18.7932 9.48171 18.7544L9.49428 18.6926L3.47318 15.2149C3.39807 15.1717 3.33335 15.1125 3.28358 15.0415C3.23381 14.9706 3.2002 14.8896 3.18511 14.8043L3.17464 14.6995V9.34043C3.17464 9.01151 3.44175 8.74439 3.77172 8.74439ZM19.5839 5.56938C19.7893 5.92515 19.845 6.34791 19.7387 6.7447C19.6324 7.1415 19.3729 7.47985 19.0172 7.68536C18.9795 7.70736 18.9387 7.71678 18.8978 7.72726L18.8381 7.74611V14.6995C18.8381 14.7863 18.8191 14.872 18.7826 14.9507C18.746 15.0294 18.6928 15.0992 18.6265 15.1552L18.5406 15.2159L13.9001 17.8955C13.7713 17.9731 13.6178 17.9991 13.4706 17.9682C13.3234 17.9373 13.1934 17.8518 13.1067 17.7288C13.02 17.6059 12.9831 17.4547 13.0034 17.3056C13.0237 17.1566 13.0997 17.0207 13.2161 16.9255L13.3041 16.8626L17.6471 14.357V7.74507C17.3475 7.61727 17.0762 7.42034 16.9012 7.11761C16.6958 6.76172 16.6401 6.3388 16.7465 5.94188C16.8529 5.54496 17.1126 5.20655 17.4685 5.00111C17.8244 4.79566 18.2473 4.74 18.6442 4.84638C19.0411 4.95275 19.3795 5.21245 19.585 5.56833L19.5839 5.56938ZM5.55144 8.06037L10.4098 10.8656V16.4771L6.14643 14.0155C5.98606 13.9227 5.84954 13.7939 5.7477 13.6391C5.64587 13.4844 5.58154 13.308 5.55982 13.124L5.55144 12.9837V8.06037ZM16.4613 8.06037V12.9837C16.4613 13.3608 16.2822 13.7138 15.9826 13.9369L15.8653 14.0155L11.6019 16.4771V10.8656L16.4613 8.06037ZM11.4751 4.50301L11.6019 4.56586L15.8653 7.02752L11.0058 9.83486L6.14748 7.02752L10.4109 4.56586C10.7377 4.37731 11.1336 4.35636 11.4762 4.50301H11.4751ZM11.2101 1.60664L11.3044 1.65063L15.9459 4.32913C16.0137 4.36827 16.073 4.4204 16.1206 4.48251C16.1682 4.54462 16.203 4.61551 16.2231 4.69111C16.2433 4.76671 16.2483 4.84554 16.238 4.92309C16.2276 5.00064 16.202 5.07538 16.1628 5.14304C16.0941 5.26439 15.9847 5.35749 15.8539 5.40581C15.7231 5.45414 15.5794 5.45457 15.4483 5.40702L15.3499 5.36093L11.0069 2.85423L5.28223 6.15913C5.32099 6.48072 5.28538 6.81592 5.11149 7.11865C4.95836 7.38373 4.73007 7.59747 4.4555 7.73284C4.18092 7.86821 3.87238 7.91914 3.56887 7.87919C3.26535 7.83925 2.98049 7.71022 2.75029 7.50842C2.52009 7.30662 2.35488 7.0411 2.27555 6.74543C2.19621 6.44975 2.20632 6.1372 2.30458 5.84726C2.40284 5.55733 2.58484 5.30304 2.8276 5.11652C3.07035 4.93 3.36294 4.81964 3.6684 4.79937C3.97386 4.77911 4.27847 4.84986 4.54374 5.00268C4.58249 5.02363 4.61078 5.05505 4.64011 5.08438L4.68724 5.12733L10.7094 1.64959C10.7848 1.6067 10.8687 1.58075 10.9551 1.57351C11.0416 1.56627 11.1286 1.57794 11.2101 1.60769V1.60664Z" fill="url(#paint0_linear_1143_3706)"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_dddi_1143_3706" x="-5.77734" y="-5.42857" width="33.5687" height="41.8559" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.380392 0 0 0 0 0.568627 0 0 0 0 1 0 0 0 0.28 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1143_3706"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.380392 0 0 0 0 0.568627 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1143_3706" result="effect2_dropShadow_1143_3706"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.709804 0 0 0 0 0.835294 0 0 0 0 0.972549 0 0 0 0.03 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_1143_3706" result="effect3_dropShadow_1143_3706"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_1143_3706" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.165999"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect4_innerShadow_1143_3706"/>
</filter>
<linearGradient id="paint0_linear_1143_3706" x1="3.02495" y1="6.96784" x2="19.7306" y2="6.96784" gradientUnits="userSpaceOnUse">
<stop stop-color="#EDFEFF"/>
<stop offset="1" stop-color="#9EC5F5"/>
</linearGradient>
</defs>
</svg>

<svg width="23" height="22" viewBox="0 0 23 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#228;&#189;&#142;&#231;&#162;&#179;">
<g id="&#228;&#189;&#142;&#231;&#162;&#179;_2">
<g id="Vector" filter="url(#filter0_dddi_1579_5210)">
<path d="M3.17722 14.8089C4.10964 8.89731 6.53738 6.64532 9.42918 5.80077C13.0682 4.67477 16.1473 5.51932 19.226 3.92411C21.6521 2.79811 21.0922 3.83022 20.9989 4.95536C20.5323 8.89645 18.6662 12.7439 14.5605 14.5266C10.0823 16.4042 5.69669 12.7447 2.5241 18.4686L2.43085 18.5625H1.96421C1.96421 18.5625 1.87097 18.4686 1.87097 18.3747V18.281C3.08398 15.6535 4.76363 13.4015 7.00294 11.6185C9.14923 9.92963 11.482 8.61522 14.0945 7.77131C14.2812 7.67743 14.188 7.30209 14.0013 7.39598C11.9484 7.8652 10.0823 8.61522 8.30941 9.74186C6.25658 11.0556 4.48369 12.7447 3.17722 14.8089Z" fill="url(#paint0_linear_1579_5210)"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_dddi_1579_5210" x="-6.12903" y="-3.5625" width="35.2493" height="38.125" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.380392 0 0 0 0 0.568627 0 0 0 0 1 0 0 0 0.28 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1579_5210"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.380392 0 0 0 0 0.568627 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1579_5210" result="effect2_dropShadow_1579_5210"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.709804 0 0 0 0 0.835294 0 0 0 0 0.972549 0 0 0 0.03 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_1579_5210" result="effect3_dropShadow_1579_5210"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_1579_5210" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.165999"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect4_innerShadow_1579_5210"/>
</filter>
<linearGradient id="paint0_linear_1579_5210" x1="2.75001" y1="7.76616" x2="21.0537" y2="7.76616" gradientUnits="userSpaceOnUse">
<stop stop-color="#EDFEFF"/>
<stop offset="1" stop-color="#9EC5F5"/>
</linearGradient>
</defs>
</svg>

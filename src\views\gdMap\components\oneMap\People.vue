<template>
	<div class="people-device-panel">
		<a-spin class="card-loading" dot :loading="loading">
			<TrafficCard title="人员&设备">
				<div class="card-content">
					<!-- Tabs -->
					<div class="tabs">
						<div :class="['tab', activeTab === 0 ? 'active' : '']" @click="onClickTab(0)">
							在线人员及设备
						</div>
						<div :class="['tab', activeTab === 1 ? 'active' : '']" @click="onClickTab(1)">
							全部人员及设备
						</div>
					</div>
					<!-- 搜索框 -->
					<div class="search-box">
						<input class="search" placeholder="搜索..." v-model="search" @change="onSearch" />
						<img src="@/assets/images/people/search.svg" alt="" />
					</div>
					<!-- 分组列表 -->
					<div class="group-list">
						<PeopleTreeItem v-for="group in groups" :key="group.name" :item="group" />
					</div>
				</div>
			</TrafficCard>
		</a-spin>
	</div>
</template>

<script setup>
import request from "@/utils/request";
import PeopleTreeItem from "./PeopleTreeItem.vue";

const loading = ref(false);
const activeTab = ref(0);
const search = ref("");
const groups = ref([]);

onMounted(() => {
	getData();
});

const getData = async () => {
	const params = {
		keyword: unref(search),
	};
	if (unref(activeTab) === 0) {
		params.online = true;
	}
	await request.get("/api/screen/staff/left/tree", params).then((res) => {
		if (res.code === 200) {
			groups.value = res.data;
		}
	});
};

const onSearch = async () => {
	try {
		loading.value = true;
		await getData();
	} finally {
		loading.value = false;
	}
};

const onClickTab = (tab) => {
	activeTab.value = tab;
	getData();
};
</script>

<style scoped lang="scss">
.people-device-panel {
	height: 100%;
	background: linear-gradient(
		90deg,
		rgba(0, 138, 255, 0.08) 0%,
		rgba(0, 138, 255, 0.12) 50%,
		rgba(0, 138, 255, 0.08) 100%
	);
	border-radius: 0px 0px 0px 0px;
	position: relative;
	.card-content {
		padding: 12px;
		.tabs {
			display: flex;
			margin: 10px 0 10px 0;
			width: 100%;
			.tab {
				flex: 1;
				text-align: center;
				padding: 8px 0;
				cursor: pointer;
				// border-radius: 6px 6px 0 0;
				border-top: 2px solid #10477b;
				background: #122333;
				color: #8dbfff;
				font-size: 14px;
				&.active {
					border-top: 2px solid #008aff;
					background: linear-gradient(180deg, rgba(0, 138, 255, 0) 0%, #008aff 100%);
					color: #fff;
				}
			}
		}
		.search-box {
			width: 100%;
			background: #001120;
			border-radius: 4px 4px 4px 4px;
			border: 1px solid #10477b;
			height: 32px;
			color: #fff;
			font-size: 12px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin: 16px 0;
			overflow: hidden;

			.search {
				flex: 1;
				height: 32px;
				padding: 0 15px 0 12px;
				color: rgba(255, 255, 255, 0.7);
				border: none;
				background: #001120;
				outline: none;
			}
			img {
				padding-right: 15px;
			}
		}

		.group-list {
			.group {
				padding: 8px;
				margin-bottom: 10px;
				background: rgba(0, 0, 0, 0.5);
				font-size: 12px;
				.group-header {
					display: flex;
					align-items: center;
					color: #ffffff;
					cursor: pointer;
				}
				.group-content {
					//   background: #19283a;
					border-radius: 0 0 6px 6px;
					padding: 8px 0 8px 20px;
					color: rgba(255, 255, 255, 0.7);
					.subgroup {
						color: #9bb3d1;
						font-size: 13px;
						margin-bottom: 6px;
					}
					.member {
						display: flex;
						align-items: center;
						font-size: 13px;
						margin-bottom: 6px;
						.status-dot {
							width: 8px;
							height: 8px;
							border-radius: 50%;
							margin-right: 8px;
							&.online {
								background: #00e676;
							}
							&.offline {
								background: #ff5252;
							}
						}
					}
				}
			}
		}
	}
	//   .chevrons-right{
	//     position: absolute;
	//     visibility:visible;
	//     top: 0;
	//     right: -32px;
	//     width: 32px;
	//     z-index: 5;
	//     height: 32px;
	//     background: linear-gradient( 180deg, rgba(0,138,255,0) 47%, #008AFF 100%), radial-gradient( 0% 87% at -13% 112%, #64C6FF 0%, rgba(8,62,115,0) 100%), rgba(0,138,255,0.3);
	//     box-shadow: inset 0px 0px 8px 0px rgba(0,138,255,0.25);
	//     border: 1px solid;
	//     border-image: linear-gradient(90deg, rgba(0, 138, 255, 0), rgba(0, 138, 255, 1), rgba(0, 138, 255, 0.2), rgba(0, 138, 255, 0)) 1 1;
	//   }
}
</style>

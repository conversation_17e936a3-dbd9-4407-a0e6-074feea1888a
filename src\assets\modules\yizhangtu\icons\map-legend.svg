<svg width="80" height="81" viewBox="0 0 80 81" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#229;&#156;&#176;&#229;&#155;&#190;&#229;&#155;&#190;&#228;&#190;&#139;">
<path id="Vector 3940" d="M40.9158 61.8506L5.2793 50.6063L40.9158 43.1782L75.1213 50.6063L40.9158 61.8506Z" fill="url(#paint0_linear_233_1253)"/>
<g id="Vector 3938" opacity="0.8" filter="url(#filter0_i_233_1253)">
<path d="M3.42578 50.2991L38.8083 0.472168V61.819C38.8083 62.4418 15.22 54.3986 3.42578 50.2991Z" fill="url(#paint1_linear_233_1253)"/>
</g>
<path id="Vector 3939" opacity="0.8" d="M74.1895 51.2327L38.8069 1.40576V62.7526C38.8069 63.3754 62.3953 55.3322 74.1895 51.2327Z" fill="url(#paint2_linear_233_1253)"/>
<ellipse id="Ellipse 4046" opacity="0.3" cx="40.1984" cy="70.4734" rx="39.5793" ry="9.9983" fill="url(#paint3_linear_233_1253)"/>
<ellipse id="Ellipse 4045" cx="40.1969" cy="70.4707" rx="22.2613" ry="5.62354" fill="url(#paint4_linear_233_1253)"/>
<g id="Ellipse 4047" filter="url(#filter1_f_233_1253)">
<ellipse cx="40.1965" cy="63.4463" rx="18.8391" ry="7.02586" fill="#1E9FE3" fill-opacity="0.4"/>
</g>
<ellipse id="Ellipse 4044" cx="40.1986" cy="70.4701" rx="8.67514" ry="2.59122" fill="url(#paint5_linear_233_1253)"/>
</g>
<defs>
<filter id="filter0_i_233_1253" x="3.42578" y="0.472168" width="37.92" height="61.3809" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2.53722"/>
<feGaussianBlur stdDeviation="2.53722"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_233_1253"/>
</filter>
<filter id="filter1_f_233_1253" x="11.2086" y="46.2715" width="57.9764" height="34.3495" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="5.07443" result="effect1_foregroundBlur_233_1253"/>
</filter>
<linearGradient id="paint0_linear_233_1253" x1="38.9789" y1="44.0537" x2="39.3853" y2="63.1927" gradientUnits="userSpaceOnUse">
<stop stop-color="#1F9B49"/>
<stop offset="1" stop-color="#CEFFF0"/>
</linearGradient>
<linearGradient id="paint1_linear_233_1253" x1="37.0587" y1="4.98927" x2="28.4059" y2="52.8764" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#0066EC"/>
</linearGradient>
<linearGradient id="paint2_linear_233_1253" x1="40.5565" y1="5.92286" x2="49.2093" y2="53.81" gradientUnits="userSpaceOnUse">
<stop stop-color="#4393FD"/>
<stop offset="1" stop-color="#0066EC"/>
</linearGradient>
<linearGradient id="paint3_linear_233_1253" x1="36.3442" y1="82.5104" x2="36.3442" y2="60.4751" gradientUnits="userSpaceOnUse">
<stop stop-color="#0C87EF"/>
<stop offset="1" stop-color="#074D89"/>
</linearGradient>
<linearGradient id="paint4_linear_233_1253" x1="38.0291" y1="77.2409" x2="38.0291" y2="64.8472" gradientUnits="userSpaceOnUse">
<stop stop-color="#0C87EF"/>
<stop offset="1" stop-color="#074D89"/>
</linearGradient>
<linearGradient id="paint5_linear_233_1253" x1="40.1986" y1="67.8789" x2="40.1986" y2="73.0613" gradientUnits="userSpaceOnUse">
<stop stop-color="#90D7FC"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
</defs>
</svg>

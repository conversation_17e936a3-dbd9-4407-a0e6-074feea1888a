<svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#228;&#189;&#141;&#231;&#167;&#187;&#231;&#155;&#145;&#230;&#181;&#139;">
<g id="Ellipse 15" filter="url(#filter0_d_342_3866)">
<circle cx="11.0264" cy="11.2041" r="8" fill="url(#paint0_radial_342_3866)" fill-opacity="0.7"/>
<circle cx="11.0264" cy="11.2041" r="7.55492" stroke="url(#paint1_linear_342_3866)" stroke-width="0.890162"/>
</g>
<circle id="Ellipse 16" cx="11.0259" cy="11.2039" r="7.01097" fill="url(#paint2_radial_342_3866)" stroke="url(#paint3_linear_342_3866)" stroke-width="0.200286"/>
<g id="Group 1321315401" filter="url(#filter1_d_342_3866)">
<g id="Frame" clip-path="url(#clip0_342_3866)">
<path id="Vector" d="M13.4723 7.48535V8.37009L13.4669 8.36569V9.77096H11.594V11.8519H13.4669L13.4671 13.2571L13.4723 13.2528V13.7462H14.744V14.9201H7.30922V13.7462H8.58096V13.2516L8.58771 13.257V11.8519H10.4607V9.77096H8.58781V8.36569L8.58096 8.37117V7.48535H13.4723ZM13.9614 9.5397L15.5266 10.8114L13.9614 12.0832L13.9613 11.3006H12.2005V10.3223H13.9614V9.5397ZM8.09183 9.5397V10.3223H9.8527V11.3006H8.09193L8.09183 12.0832L6.52661 10.8114L8.09183 9.5397Z" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_342_3866" x="0.355881" y="0.533616" width="21.341" height="21.341" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.33524"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.388235 0 0 0 0 0.640523 0 0 0 0 0.807843 0 0 0 0.51 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_342_3866"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3866" result="shape"/>
</filter>
<filter id="filter1_d_342_3866" x="6.02661" y="6.20312" width="9.9082" height="10.3533" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.445081"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.150327 0 0 0 0 0.247059 0 0 0 0 0.145098 0 0 0 0.75 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_342_3866"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_342_3866" result="shape"/>
</filter>
<radialGradient id="paint0_radial_342_3866" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(11.0264 11.2041) scale(8)">
<stop stop-color="#D3D3D3"/>
<stop offset="0.03" stop-color="#D3D3D3" stop-opacity="0.9216"/>
<stop offset="0.13" stop-color="#D3D3D3" stop-opacity="0.7056"/>
<stop offset="0.23" stop-color="#D3D3D3" stop-opacity="0.5329"/>
<stop offset="0.35" stop-color="#D3D3D3" stop-opacity="0.4225"/>
<stop offset="0.47" stop-color="#D3D3D3" stop-opacity="0.3364"/>
<stop offset="0.6" stop-color="#D3D3D3" stop-opacity="0.2916"/>
<stop offset="0.76" stop-color="#D3D3D3" stop-opacity="0.2601"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.5"/>
</radialGradient>
<linearGradient id="paint1_linear_342_3866" x1="11.0264" y1="19.2041" x2="11.0264" y2="3.2041" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<radialGradient id="paint2_radial_342_3866" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(11.0259 11.2039) scale(7.11111)">
<stop stop-color="#D3D3D3"/>
<stop offset="1" stop-color="#0B103B" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint3_linear_342_3866" x1="11.0259" y1="18.315" x2="11.0259" y2="4.09277" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3D3D3"/>
<stop offset="0.49" stop-color="#D3D3D3" stop-opacity="0.5737"/>
<stop offset="1" stop-color="#D3D3D3" stop-opacity="0.13"/>
</linearGradient>
<clipPath id="clip0_342_3866">
<rect width="9.90826" height="9.90826" fill="white" transform="translate(6.02661 6.20312)"/>
</clipPath>
</defs>
</svg>

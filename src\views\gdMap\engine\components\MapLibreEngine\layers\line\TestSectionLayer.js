import maplibregl from 'maplibre-gl';
import mapConfig from '@/config/engine/maplibre/map.config.js';
import { createApp } from 'vue';
import MarkerInfo from '../../components/MarkerInfo.vue';

// 图层缩放级别常量
const LAYER_ZOOM_LEVELS = {
  BASE: {
    MIN: 11,
    MAX: mapConfig.maxzoom,
  },
  GLOW: {
    MIN: 11,
    MAX: mapConfig.maxzoom,
  },
  FLOW: {
    MIN: 11,
    MAX: mapConfig.maxzoom,
  },
  MARKER: {
    MIN: 11,
    MAX: mapConfig.maxzoom,
  },
};

export class TestSectionLayer {
  constructor(map) {
    this.map = map;
    this.animationFrame = null;
    this.visible = true;
    this.sourceId = 'test-section'; // 数据源ID
    this.baseLayerId = 'test-section-base'; // 基础图层ID
    this.glowLayerId = 'test-section-glow'; // 发光效果图层ID
    this.flowLayerId = 'test-section-flow-light'; // 流光图层ID
    this.markers = []; // 存储创建的标记点
    this.infoMarkers = new Map(); // 存储信息面板标记
  }

  async init() {
    try {
      // 加载试验段数据
      const response = await fetch('/config/engine/map/json/line/test-section.json');
      const geoJSON = await response.json();

      // 添加试验段数据源，启用lineMetrics以支持line-progress
      this.map.addSource(this.sourceId, {
        type: 'geojson',
        data: geoJSON,
        tolerance: 0.1,
        lineMetrics: true, // 启用线性度量，支持line-progress
      });

      // 添加基础试验段图层
      this.map.addLayer({
        id: this.baseLayerId,
        type: 'line',
        source: this.sourceId,
        layout: {
          'line-join': 'round',
          'line-cap': 'round',
          visibility: 'visible',
        },
        paint: {
          'line-color': '#FF8E48', // 试验段基础颜色
          'line-width': 6, // 基础宽度
          'line-opacity': 0.8,
        },
        minzoom: LAYER_ZOOM_LEVELS.BASE.MIN,
        maxzoom: LAYER_ZOOM_LEVELS.BASE.MAX,
      });

      // 添加试验段发光效果图层
      this.map.addLayer({
        id: this.glowLayerId,
        type: 'line',
        source: this.sourceId,
        layout: {
          'line-join': 'round',
          'line-cap': 'round',
          visibility: 'visible',
        },
        paint: {
          'line-color': '#FF8E48', // 发光颜色
          'line-width': 12, // 较宽的线条
          'line-opacity': 0.3, // 低不透明度
          'line-blur': 6, // 较大的模糊值，创建发光效果
        },
        minzoom: LAYER_ZOOM_LEVELS.GLOW.MIN,
        maxzoom: LAYER_ZOOM_LEVELS.GLOW.MAX,
      });

      // 添加流光效果图层 - 使用线性渐变
      this.map.addLayer({
        id: this.flowLayerId,
        type: 'line',
        source: this.sourceId,
        layout: {
          'line-join': 'round',
          'line-cap': 'round',
          visibility: 'visible',
        },
        paint: {
          'line-color': 'transparent',
          // 初始渐变设置，将在动画中动态更新
          'line-gradient': [
            'interpolate',
            ['linear'],
            ['line-progress'],
            0,
            'rgba(255,142,72,0)',
            0.5,
            'rgba(255,142,72,0)',
            1,
            'rgba(255,142,72,0)',
          ],
          'line-width': 8,
          'line-opacity': 0.9,
        },
        minzoom: LAYER_ZOOM_LEVELS.FLOW.MIN,
        maxzoom: LAYER_ZOOM_LEVELS.FLOW.MAX,
      });

      // 为每个feature添加中间点标记
      this.addMidpointMarkers(geoJSON);

      // 启动流光动画
      this.startFlowLightAnimation();

      // 监听地图缩放事件，控制标记点显示
      this.map.on('zoom', this.handleZoomChange.bind(this));
      this.handleZoomChange(); // 初始化时执行一次
    } catch (error) {
      console.error('初始化试验段图层失败:', error);
    }
  }

  // 处理地图缩放事件
  handleZoomChange() {
    const currentZoom = this.map.getZoom();

    // 根据当前缩放级别控制标记点显示
    if (this.visible) {
      if (
        currentZoom >= LAYER_ZOOM_LEVELS.MARKER.MIN &&
        currentZoom <= LAYER_ZOOM_LEVELS.MARKER.MAX
      ) {
        // 在指定缩放范围内显示标记
        this.markers.forEach(marker => marker.addTo(this.map));
      } else {
        // 在范围外隐藏标记
        this.markers.forEach(marker => marker.remove());
      }
    }
  }

  // 添加中间点标记方法
  addMidpointMarkers(geoJSON) {
    // 清除之前的标记
    this.clearMarkers();

    // 遍历每个feature
    geoJSON.features.forEach((feature, index) => {
      if (feature.geometry.type === 'LineString') {
        const coordinates = feature.geometry.coordinates;

        // 获取线段中间点的坐标
        const midIndex = Math.floor(coordinates.length / 2);
        const midpoint = coordinates[midIndex];

        // 创建标记元素
        const markerEl = document.createElement('div');
        markerEl.className = 'test-section-marker';
        markerEl.style.width = '36px';
        markerEl.style.height = '53px';

        // 修正图标路径，使用绝对路径确保加载
        const iconPath = `${window.location.origin}/assets/images/icons/map/shi-yan-duan-point.svg`;
        markerEl.style.backgroundImage = `url(${iconPath})`;
        markerEl.style.backgroundSize = 'contain';
        markerEl.style.backgroundRepeat = 'no-repeat';
        markerEl.style.backgroundPosition = 'center';
        markerEl.style.cursor = 'pointer';

        // 恢复点击事件处理
        markerEl.addEventListener('click', e => {
          // 阻止事件冒泡，防止地图接收到点击事件
          e.stopPropagation();

          console.log('点击了试验段标记', feature.properties);

          // 生成唯一ID
          const id = `test-section-${index}-${Date.now()}`;

          // 检查是否已经有信息面板打开
          if (this.infoMarkers.size > 0) {
            // 清除所有已打开的信息面板
            this.clearInfoMarkers();
          } else {
            // 创建信息面板
            this.createInfoMarker(id, midpoint, feature, index);
          }
        });

        // 创建标记但不立即添加到地图，将根据缩放级别决定是否显示
        const marker = new maplibregl.Marker({
          element: markerEl,
          anchor: 'center', // 确保标记居中对齐
          offset: [0, 0], // 无偏移
        }).setLngLat(midpoint);

        // 存储标记引用以便后续管理
        this.markers.push(marker);
      }
    });

    // 初始检查是否应该显示标记
    this.handleZoomChange();
  }

  // 清除所有标记
  clearMarkers() {
    this.markers.forEach(marker => marker.remove());
    this.markers = [];
  }

  // 流光动画方法 - 使用时间表达式
  startFlowLightAnimation() {
    // 如果已经有动画在运行，先停止它
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
    }

    let startTime = performance.now();
    const duration = 8000; // 流光完整周期时间

    // 创建多个流光点，确保连贯性
    const flowPoints = 2; // 同时存在的流光点数量

    const animate = () => {
      const currentTime = performance.now();
      const elapsed = currentTime - startTime;

      // 更新流光图层的渐变位置
      if (this.map && this.map.getLayer(this.flowLayerId)) {
        try {
          // 使用简化的方法创建渐变
          // 避免复杂的点集合和排序，直接使用固定的渐变模式
          const t = (elapsed / duration) % 1; // 当前动画进度 (0-1)

          // 创建基础渐变
          const gradientStops = [
            'interpolate',
            ['linear'],
            ['line-progress'],
            0,
            'rgba(255,142,72,0)', // 起始点始终透明
          ];

          // 为每个流光点添加固定间隔的渐变点
          for (let i = 0; i < flowPoints; i++) {
            // 计算当前流光点的位置 (0-1范围内)
            const pointPos = (t + i / flowPoints) % 1;

            // 计算流光前后的位置，确保在0-1范围内
            const beforePos = Math.max(0, pointPos - 0.1);
            const startPos = Math.max(0, pointPos - 0.05);
            const endPos = Math.min(1, pointPos + 0.05);
            const afterPos = Math.min(1, pointPos + 0.1);

            // 确保位置值严格递增且不重复
            // 只有当位置值大于前一个位置时才添加
            if (beforePos > gradientStops[gradientStops.length - 2]) {
              gradientStops.push(
                beforePos,
                'rgba(255,142,72,0)' // 流光前透明
              );
            }

            if (startPos > gradientStops[gradientStops.length - 2]) {
              gradientStops.push(
                startPos,
                'rgba(255,255,255,0.3)' // 流光开始过渡
              );
            }

            if (pointPos > gradientStops[gradientStops.length - 2]) {
              gradientStops.push(
                pointPos,
                '#FFFFFF' // 流光中心点
              );
            }

            if (endPos > gradientStops[gradientStops.length - 2]) {
              gradientStops.push(
                endPos,
                'rgba(255,255,255,0.3)' // 流光结束过渡
              );
            }

            if (afterPos > gradientStops[gradientStops.length - 2]) {
              gradientStops.push(
                afterPos,
                'rgba(255,142,72,0)' // 流光后透明
              );
            }
          }

          // 确保结束点存在且值为1
          if (gradientStops[gradientStops.length - 2] < 1) {
            gradientStops.push(
              1,
              'rgba(255,142,72,0)' // 结束点透明
            );
          }

          // 应用渐变
          this.map.setPaintProperty(this.flowLayerId, 'line-gradient', gradientStops);

          // 继续下一帧动画
          this.animationFrame = requestAnimationFrame(animate);
        } catch (error) {
          console.warn('流光动画更新失败:', error);
          // 出错时尝试重新启动动画
          setTimeout(() => this.startFlowLightAnimation(), 1000);
        }
      } else if (this.visible) {
        // 如果图层不存在但应该可见，尝试重新创建
        console.warn('流光图层不存在，尝试重新初始化');
        setTimeout(() => this.startFlowLightAnimation(), 1000);
      }
    };

    // 开始动画循环
    this.animationFrame = requestAnimationFrame(animate);
  }

  // 设置图层可见性
  setVisible(visible) {
    this.visible = visible;
    const visibility = visible ? 'visible' : 'none';

    if (this.map.getLayer(this.baseLayerId)) {
      this.map.setLayoutProperty(this.baseLayerId, 'visibility', visibility);
    }

    if (this.map.getLayer(this.glowLayerId)) {
      this.map.setLayoutProperty(this.glowLayerId, 'visibility', visibility);
    }

    if (this.map.getLayer(this.flowLayerId)) {
      this.map.setLayoutProperty(this.flowLayerId, 'visibility', visibility);
    }

    // 同时控制标记的可见性
    if (visible) {
      // 如果设为可见，根据当前缩放级别决定是否显示标记
      this.handleZoomChange();
    } else {
      // 如果设为不可见，直接移除所有标记
      this.markers.forEach(marker => marker.remove());
    }

    // 如果不可见，停止动画以节省资源
    if (!visible && this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
    } else if (visible && !this.animationFrame) {
      this.startFlowLightAnimation();
    }
  }

  // 销毁图层
  destroy() {
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
    }

    // 移除地图缩放事件监听
    this.map.off('zoom', this.handleZoomChange);

    // 清除所有标记
    this.clearMarkers();

    // 清除所有信息面板标记
    this.clearInfoMarkers();

    if (this.map.getLayer(this.flowLayerId)) {
      this.map.removeLayer(this.flowLayerId);
    }

    if (this.map.getLayer(this.glowLayerId)) {
      this.map.removeLayer(this.glowLayerId);
    }

    if (this.map.getLayer(this.baseLayerId)) {
      this.map.removeLayer(this.baseLayerId);
    }

    if (this.map.getSource(this.sourceId)) {
      this.map.removeSource(this.sourceId);
    }
  }

  // 创建信息面板标记
  createInfoMarker(id, lngLat, feature, index) {
    const el = document.createElement('div');

    // 准备属性数据
    const properties = feature.properties || {};
    const label = properties.name || `试验段 ${index + 1}`;

    // 创建Vue应用实例
    const app = createApp(MarkerInfo, {
      label,
      properties,
    });

    // 获取组件实例
    const componentInstance = app.mount(el);

    // 设置关闭回调
    if (componentInstance.setCloseCallback) {
      componentInstance.setCloseCallback(() => {
        this.removeInfoMarker(id);
      });
    }

    // 创建 Marker
    const marker = new maplibregl.Marker({
      element: el,
      anchor: 'bottom', // 使用bottom锚点使其位于坐标点正上方
      offset: [0, -25], // 添加垂直偏移，与图标保持距离
    })
      .setLngLat(lngLat)
      .addTo(this.map);

    // 保存标记信息
    this.infoMarkers.set(id, {
      marker,
      app,
      el,
      properties,
      componentInstance,
    });

    return marker;
  }

  // 移除信息面板标记
  removeInfoMarker(id) {
    if (this.infoMarkers.has(id)) {
      const { marker, app } = this.infoMarkers.get(id);
      marker.remove();
      app.unmount();
      this.infoMarkers.delete(id);
    }
  }

  // 清除所有信息面板标记
  clearInfoMarkers() {
    this.infoMarkers.forEach(({ marker, app }) => {
      marker.remove();
      app.unmount();
    });
    this.infoMarkers.clear();
  }
}

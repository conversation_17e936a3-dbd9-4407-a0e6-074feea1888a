import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue';

/**
 * 跑马灯效果Hook - 循环无缝滚动
 * @param {Object} options 配置选项
 * @param {number} options.speed 滚动速度(px/s)，默认为30
 * @param {number} options.delay 每轮滚动后的停顿时间(ms)，默认为2000
 * @param {number} options.step 每次滚动的像素，默认为1
 * @param {boolean} options.hoverPause 是否启用鼠标悬停暂停，默认为true
 * @param {boolean} options.wheelScroll 是否启用滚轮滚动，默认为true
 * @returns {Object} 包含ref和控制方法
 */
export function useMarquee(options = {}) {
  const containerRef = ref(null);
  const isScrolling = ref(false);
  const needScroll = ref(false);
  const isPaused = ref(false); // 是否处于暂停状态
  const isManualScrolling = ref(false); // 是否正在手动滚动
  const isHovering = ref(false); // 新增：是否鼠标悬停中

  // 默认配置
  const config = {
    speed: options.speed || 30,
    delay: options.delay || 2000,
    step: options.step || 1,
    hoverPause: options.hoverPause !== false, // 默认启用鼠标悬停暂停
    wheelScroll: options.wheelScroll !== false, // 默认启用滚轮滚动
  };

  let pauseTimer = null;
  let animationFrame = null;
  let contentElement = null;
  let lastTimestamp = 0; // 时间戳记录
  let currentScrollTop = 0; // 记录当前滚动位置
  let wheelScrollTimeout = null; // 滚轮滚动后的延时器
  let resumeTimer = null; // 新增：恢复计时器

  // 检查是否需要滚动
  const checkNeedScroll = () => {
    if (!containerRef.value) return false;

    // 获取容器的第一个子元素作为内容元素
    contentElement = containerRef.value.children[0];
    if (!contentElement) return false;

    const containerHeight = containerRef.value.clientHeight;
    const contentHeight = contentElement.scrollHeight;

    // 只有当内容高度大于容器高度时才需要滚动
    needScroll.value = contentHeight > containerHeight;
    return needScroll.value;
  };

  // 初始化滚动内容
  const initScrollContent = () => {
    if (!containerRef.value) return;

    // 获取容器的第一个子元素作为内容元素
    contentElement = containerRef.value.children[0];
    if (!contentElement) return;

    // 清除可能存在的克隆节点
    const clones = containerRef.value.querySelectorAll('.marquee-clone');
    clones.forEach(clone => clone.remove());

    if (checkNeedScroll()) {
      // 克隆内容用于无缝滚动
      const clone = contentElement.cloneNode(true);
      clone.classList.add('marquee-clone');
      
      // 为克隆元素中的所有可点击元素添加事件代理
      setupClickDelegation(clone);
      
      containerRef.value.appendChild(clone);
  
      // 设置容器样式
      containerRef.value.style.overflow = 'hidden';
  
      // 添加鼠标悬停事件
      if (config.hoverPause) {
        setupHoverEvents();
      }
  
      // 添加滚轮事件
      if (config.wheelScroll) {
        setupWheelEvents();
      }
    }
  };

  // 为克隆元素设置点击事件代理
  const setupClickDelegation = (cloneElement) => {
    // 为克隆元素添加点击事件监听器
    cloneElement.addEventListener('click', (event) => {
      // 获取被点击的元素
      const clickedElement = event.target;
      
      // 查找对应的原始元素
      const originalElement = findOriginalElement(clickedElement, cloneElement, contentElement);
      
      if (originalElement) {
        // 触发原始元素的点击事件
        originalElement.click();
        // 阻止事件冒泡，避免重复触发
        event.stopPropagation();
      }
    });
  };

  // 查找克隆元素对应的原始元素
  const findOriginalElement = (clickedElement, cloneRoot, originalRoot) => {
    // 如果点击的是克隆根元素本身，返回原始根元素
    if (clickedElement === cloneRoot) {
      return originalRoot;
    }
    
    // 构建从点击元素到克隆根元素的路径
    const path = [];
    let current = clickedElement;
    
    while (current !== cloneRoot && current.parentElement) {
      const parent = current.parentElement;
      const index = Array.from(parent.children).indexOf(current);
      path.unshift(index);
      current = parent;
    }
    
    // 如果无法构建完整路径，返回null
    if (current !== cloneRoot) {
      return null;
    }
    
    // 根据路径在原始元素中查找对应元素
    let originalElement = originalRoot;
    for (const index of path) {
      if (!originalElement.children || index >= originalElement.children.length) {
        return null;
      }
      originalElement = originalElement.children[index];
    }
    
    return originalElement;
  };

  // 设置鼠标悬停事件
  const setupHoverEvents = () => {
    if (!containerRef.value) return;

    // 移除可能存在的旧事件监听器
    containerRef.value.removeEventListener('mouseenter', handleMouseEnter);
    containerRef.value.removeEventListener('mouseleave', handleMouseLeave);

    // 添加新的事件监听器
    containerRef.value.addEventListener('mouseenter', handleMouseEnter);
    containerRef.value.addEventListener('mouseleave', handleMouseLeave);
  };

  // 设置滚轮事件
  const setupWheelEvents = () => {
    if (!containerRef.value) return;

    // 移除可能存在的旧事件监听器
    containerRef.value.removeEventListener('wheel', handleWheel);

    // 添加新的事件监听器
    containerRef.value.addEventListener('wheel', handleWheel, { passive: false });
  };

  // 滚轮事件处理函数
  const handleWheel = event => {
    if (!isPaused.value || !containerRef.value || !contentElement) return;

    // 阻止默认滚动行为
    event.preventDefault();

    isManualScrolling.value = true;
    clearTimeout(wheelScrollTimeout);

    // 获取滚动方向和距离
    const delta = event.deltaY || event.detail || event.wheelDelta;
    const scrollAmount = delta > 0 ? 30 : -30; // 调整滚动速度

    // 更新滚动位置
    containerRef.value.scrollTop += scrollAmount;
    currentScrollTop = containerRef.value.scrollTop;

    // 设置滚轮滚动结束的延时
    wheelScrollTimeout = setTimeout(() => {
      isManualScrolling.value = false;
      // 如果此时不在悬停状态，尝试恢复滚动
      if (!isHovering.value && isPaused.value) {
        resumeScroll();
      }
    }, 200);
  };

  // 鼠标进入处理函数
  const handleMouseEnter = () => {
    isHovering.value = true;
    clearTimeout(resumeTimer); // 清除可能存在的恢复计时器

    if (isScrolling.value && !isPaused.value) {
      pauseScroll();
    }
  };

  // 鼠标离开处理函数
  const handleMouseLeave = () => {
    isHovering.value = false;

    // 添加短暂延迟，防止快速移入移出导致的异常
    clearTimeout(resumeTimer);
    resumeTimer = setTimeout(() => {
      if (isScrolling.value && isPaused.value && !isManualScrolling.value) {
        resumeScroll();
      }
    }, 50);
  };

  // 暂停滚动
  const pauseScroll = () => {
    if (!isScrolling.value || isPaused.value) return;

    isPaused.value = true;

    // 保存当前滚动位置
    if (containerRef.value) {
      currentScrollTop = containerRef.value.scrollTop;
    }

    // 取消动画帧
    if (animationFrame) {
      cancelAnimationFrame(animationFrame);
      animationFrame = null;
    }
  };

  // 恢复滚动
  const resumeScroll = () => {
    if (!isScrolling.value || !isPaused.value || isHovering.value) return;

    // 检查是否还需要滚动
    if (!checkNeedScroll()) return;

    isPaused.value = false;
    lastTimestamp = 0; // 重置时间戳

    // 从当前位置继续滚动
    const container = containerRef.value;
    if (!container || !contentElement) return;

    const contentHeight = contentElement.scrollHeight;
    let scrollTop = currentScrollTop; // 使用当前滚动位置

    // 确保滚动位置合法
    if (scrollTop < 0) scrollTop = 0;
    if (scrollTop >= contentHeight) scrollTop = 0;

    // 使用requestAnimationFrame实现平滑滚动
    const scroll = timestamp => {
      if (!container || !contentElement) {
        cancelAnimationFrame(animationFrame);
        return;
      }

      // 如果处于暂停状态或正在手动滚动，不进行滚动
      if (isPaused.value || isManualScrolling.value) {
        cancelAnimationFrame(animationFrame);
        return;
      }

      // 计算时间差和应该滚动的距离
      if (!lastTimestamp) lastTimestamp = timestamp;
      const elapsed = timestamp - lastTimestamp;
      lastTimestamp = timestamp;

      // 根据速度计算每毫秒应该滚动的像素数，然后乘以经过的时间
      const pixelsToScroll = (config.speed / 1000) * elapsed;

      // 增加滚动位置
      scrollTop += pixelsToScroll;
      currentScrollTop = scrollTop; // 更新当前滚动位置

      // 当滚动到第一个内容的底部时，重置位置实现无缝衔接
      if (scrollTop >= contentHeight) {
        // 暂停一段时间
        clearTimeout(pauseTimer);
        pauseTimer = setTimeout(() => {
          // 重置滚动位置
          scrollTop = 0;
          currentScrollTop = 0;
          container.scrollTop = 0;
          lastTimestamp = 0; // 重置时间戳

          // 继续滚动，只有在非暂停和非手动滚动状态下才继续
          if (!isPaused.value && !isManualScrolling.value) {
            animationFrame = requestAnimationFrame(scroll);
          }
        }, config.delay);

        return;
      }

      // 设置滚动位置
      container.scrollTop = scrollTop;

      // 继续下一帧动画
      animationFrame = requestAnimationFrame(scroll);
    };

    // 开始动画
    animationFrame = requestAnimationFrame(scroll);
  };

  // 开始滚动
  const startScroll = () => {
    // 如果不需要滚动或已经在滚动，则直接返回
    if (!needScroll.value || isScrolling.value || !containerRef.value) return;

    isScrolling.value = true;
    isPaused.value = false;
    isManualScrolling.value = false;
    isHovering.value = false;

    const container = containerRef.value;

    // 重新获取内容元素，确保是最新的
    contentElement = container.children[0];
    if (!contentElement) return;

    let scrollTop = 0;
    currentScrollTop = 0;
    const contentHeight = contentElement.scrollHeight;
    lastTimestamp = 0; // 重置时间戳

    // 使用requestAnimationFrame实现平滑滚动
    const scroll = timestamp => {
      if (!container || !contentElement) {
        cancelAnimationFrame(animationFrame);
        return;
      }

      // 如果处于暂停状态或正在手动滚动，不进行滚动
      if (isPaused.value || isManualScrolling.value) {
        cancelAnimationFrame(animationFrame);
        return;
      }

      // 计算时间差和应该滚动的距离
      if (!lastTimestamp) lastTimestamp = timestamp;
      const elapsed = timestamp - lastTimestamp;
      lastTimestamp = timestamp;

      // 根据速度计算每毫秒应该滚动的像素数，然后乘以经过的时间
      const pixelsToScroll = (config.speed / 1000) * elapsed;

      // 增加滚动位置
      scrollTop += pixelsToScroll;
      currentScrollTop = scrollTop; // 更新当前滚动位置

      // 当滚动到第一个内容的底部时，重置位置实现无缝衔接
      if (scrollTop >= contentHeight) {
        // 暂停一段时间
        clearTimeout(pauseTimer);
        pauseTimer = setTimeout(() => {
          // 重置滚动位置
          scrollTop = 0;
          currentScrollTop = 0;
          container.scrollTop = 0;
          lastTimestamp = 0; // 重置时间戳

          // 继续滚动，只有在非暂停和非手动滚动状态下才继续
          if (!isPaused.value && !isManualScrolling.value) {
            animationFrame = requestAnimationFrame(scroll);
          }
        }, config.delay);

        return;
      }

      // 设置滚动位置
      container.scrollTop = scrollTop;

      // 继续下一帧动画
      animationFrame = requestAnimationFrame(scroll);
    };

    // 开始动画
    animationFrame = requestAnimationFrame(scroll);
  };

  // 停止滚动
  const stopScroll = () => {
    isScrolling.value = false;
    isPaused.value = false;
    isManualScrolling.value = false;
    isHovering.value = false;

    clearTimeout(pauseTimer);
    clearTimeout(wheelScrollTimeout);
    clearTimeout(resumeTimer);

    if (animationFrame) {
      cancelAnimationFrame(animationFrame);
      animationFrame = null;
    }

    // 移除事件监听器
    if (containerRef.value) {
      if (config.hoverPause) {
        containerRef.value.removeEventListener('mouseenter', handleMouseEnter);
        containerRef.value.removeEventListener('mouseleave', handleMouseLeave);
      }
      if (config.wheelScroll) {
        containerRef.value.removeEventListener('wheel', handleWheel);
      }
    }
  };

  // 强制重新开始滚动（可用于外部调用解决滚动卡住的问题）
  const forceRestart = () => {
    stopScroll();

    // 重新初始化滚动
    nextTick(() => {
      initScrollContent();
      if (checkNeedScroll()) {
        startScroll();
      }
    });
  };

  // 重置滚动
  const resetScroll = () => {
    stopScroll();
    if (containerRef.value) {
      containerRef.value.scrollTop = 0;
      currentScrollTop = 0;
    }

    // 重新初始化内容
    nextTick(() => {
      initScrollContent();
      if (checkNeedScroll()) {
        startScroll();
      }
    });
  };

  // 组件挂载后初始化
  onMounted(() => {
    nextTick(() => {
      initScrollContent();
      if (checkNeedScroll()) {
        startScroll();
      }
    });

    // 监听窗口大小变化
    window.addEventListener('resize', resetScroll);
  });

  // 组件卸载前清理
  onBeforeUnmount(() => {
    stopScroll();
    window.removeEventListener('resize', resetScroll);
  });

  return {
    containerRef,
    isScrolling,
    needScroll,
    isPaused,
    isManualScrolling,
    isHovering,
    startScroll,
    stopScroll,
    pauseScroll,
    resumeScroll,
    resetScroll,
    forceRestart, // 导出强制重新开始方法
  };
}

/**
 * 地图位置跳转配置
 * 可以根据项目需要随时添加新的位置点
 */
export default {
  // 各个场景的位置配置
  locations: {
    gegong1: {
      center: [94.689236, 35.913044],
      zoom: 12,
    },
    gegong2: {
      center: [93.08008, 35.220299],
      zoom: 12,
    },
    // 格贡3
    gegong3: {
      center: [92.4596624165, 34.2386620156],
      zoom: 12,
    },
    gegong4: {
      center: [92.3572587039, 33.9019103819],
      zoom: 12,
    },
    gongna1: {
      center: [91.905986, 33.060192],
      zoom: 12,
    },
    gongna2: {
      center: [91.8951901784, 32.7319838777],
      zoom: 12,
    },
    // 高速
    gaosu: {
      center: [92.36004462425, 33.97821170525],
      zoom: 11,
    },
    // 五道梁试验段
    wudaoliang: {
      center: [93.05151790235, 35.1773446997],
      zoom: 13,
    },
    // 贡玛日试验段
    gongmari: {
      center: [91.91774784425, 33.06369878335],
      zoom: 13,
      // duration: 3000,
      // bearing: 0,
      // pitch: 0,
    },
    // 唐荣藏占试验段
    tangrongzangzhan: {
      center: [91.8883031229, 32.7345287786],
      zoom: 13,
    },

    // 项目总览
    all: {
      center: [90.804531, 33.31002],
      zoom: 5,
      duration: 3000,
      bearing: 0,
      pitch: 0,
    },
  },

  // 默认飞行参数
  defaultFlyOptions: {
    duration: 3000,
    bearing: 0,
    pitch: 0,
  },
};

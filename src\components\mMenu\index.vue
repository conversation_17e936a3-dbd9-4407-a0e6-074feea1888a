<template>
	<div class="m-menu">
		<slot></slot>
	</div>
</template>
<script setup>
import { ref, watch, provide } from "vue";
const emit = defineEmits(["select"]);
const props = defineProps({
	defaultActive: {
		type: [String, Number],
		default: "",
	},
});
const activeIndex = ref(props.defaultActive);
// 提供一个方法来供子组件调用，用于更新活动项
const updateActive = (index) => {
	activeIndex.value = index;
	emit("select", index);
};
// 使用 provide 使得子组件可以访问 updateActive
provide("updateActive", updateActive);
provide("activeIndex", activeIndex);
// 监听 props.defaultActive 的变化，以便从外部更新时同步内部状态
watch(
	() => props.defaultActive,
	(newVal) => {
		if (newVal !== activeIndex.value) {
			activeIndex.value = newVal;
		}
	}
);
</script>
<style lang="scss">
.m-menu {
	padding: 12px 0;
	display: flex;
	position: relative;

	// // 添加时间轴线
	// &::before {
	// 	// padding: 8px 0;
	// 	content: "";
	// 	position: absolute;
	// 	bottom: 10px;
	// 	left: 0;
	// 	right: 0;
	// 	height: 4px;
	// 	background: linear-gradient(
	// 		90deg,
	// 		rgba(122, 139, 161, 0) 0%,
	// 		rgba(122, 139, 161, 0.2) 3%,
	// 		rgba(122, 139, 161, 0.4) 30%,
	// 		rgba(122, 139, 161, 0.2) 98%,
	// 		rgba(122, 139, 161, 0) 100%
	// 	);
	// }
}
</style>

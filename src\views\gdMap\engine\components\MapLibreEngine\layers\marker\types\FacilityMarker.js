import { BaseMarker } from '../BaseMarker';
import maplibregl from 'maplibre-gl';
import { createApp } from 'vue';
import MarkerInfo from '../../../components/MarkerInfo.vue';
import mapConfig from '@/config/engine/maplibre/map.config.js';

// 图层缩放级别常量
const LAYER_ZOOM_LEVELS = {
  ICON: {
    MIN: 9,
    MAX: mapConfig.maxzoom,
    SIZE: {
      DEFAULT: 1,
    },
  },
  CLUSTER: {
    MIN: 5, // 从5级开始显示聚合
    MAX: 9, // 到9级结束聚合
    SIZE: {
      DEFAULT: 1,
    },
  },
  BOARD: {
    MIN: 9,
    MAX: mapConfig.maxzoom,
  },
};

// 聚合点图标映射
const CLUSTER_ICON_MAPPING = {
  super_large_bridge: 'da-qiao',
  mixing_plant: 'ban-he-zhan',
  culvert: 'culvert-icon',
  default: 'da-qiao', // 修改默认图标为更明显的图标
};

/**
 * 工程设施标记基类
 * 所有工程设施标记类型都继承自这个类
 */
export class FacilityMarker extends BaseMarker {
  /**
   * 渲染工程设施标记
   * @param {Object} map - MapLibre地图实例
   * @param {String} sourceId - 数据源ID
   * @param {Object} options - 渲染选项
   * @returns {Object} 渲染结果
   */
  static render(map, sourceId, options = {}) {
    const { data, iconMapping = {} } = options;

    // 修改移除逻辑，确保先移除图层再移除数据源
    if (map.getSource(sourceId)) {
      // 获取所有使用该数据源的图层
      const layersToRemove = [];
      const clusterLayerId = `${sourceId}-cluster-layer`;
      const layerId = `${sourceId}-layer`;

      // 检查并添加到待移除列表
      if (map.getLayer(clusterLayerId)) {
        layersToRemove.push(clusterLayerId);
      }
      if (map.getLayer(layerId)) {
        layersToRemove.push(layerId);
      }

      // 移除所有相关图层
      layersToRemove.forEach(id => {
        map.removeLayer(id);
      });

      // 然后移除数据源
      map.removeSource(sourceId);
    }

    // 确保每个feature都有id属性
    if (data && data.features) {
      data.features = data.features.map((feature, index) => {
        if (!feature.id) {
          feature.id = `facility-${index}`;
        }
        return feature;
      });
    }

    // 添加数据源，启用generateId选项或指定idProperty
    map.addSource(sourceId, {
      type: 'geojson',
      data: data,
      generateId: true,
      cluster: true,
      clusterMaxZoom: LAYER_ZOOM_LEVELS.CLUSTER.MAX,
      clusterRadius: 100,
      clusterProperties: {
        // 收集聚合点中的类型信息，用于后续确定聚合点图标
        super_large_bridge_count: [
          '+',
          ['case', ['==', ['get', 'category'], 'super_large_bridge'], 1, 0],
        ],
        mixing_plant_count: ['+', ['case', ['==', ['get', 'category'], 'mixing_plant'], 1, 0]],
        culvert_count: ['+', ['case', ['==', ['get', 'category'], 'culvert'], 1, 0]],
        // 记录主要类型，用于确定聚合点图标
        dominant_category: ['concat', ['get', 'category']],
      },
    });

    // 添加聚合图层
    const clusterLayerId = `${sourceId}-cluster-layer`;

    map.addLayer({
      id: clusterLayerId,
      type: 'symbol',
      source: sourceId,
      filter: ['has', 'point_count'],
      layout: {
        'icon-image': [
          'case',
          ['>', ['get', 'super_large_bridge_count'], 0],
          CLUSTER_ICON_MAPPING['super_large_bridge'],
          ['>', ['get', 'mixing_plant_count'], 0],
          CLUSTER_ICON_MAPPING['mixing_plant'],
          ['>', ['get', 'culvert_count'], 0],
          CLUSTER_ICON_MAPPING['culvert'],
          CLUSTER_ICON_MAPPING['default'],
        ],

        'icon-allow-overlap': true,
        'icon-ignore-placement': true,
        'icon-size': LAYER_ZOOM_LEVELS.CLUSTER.SIZE.DEFAULT,

        'text-size': 14, // 增大文字尺寸
        'text-offset': [0, 0.1],
        'text-anchor': 'center',
      },
      paint: {
        'text-color': '#ffffff',
        'text-halo-color': '#000000',
        'text-halo-width': 2, // 增加文字描边宽度，提高可见性
        'icon-opacity': 1, // 确保图标完全不透明
      },
      minzoom: LAYER_ZOOM_LEVELS.CLUSTER.MIN,
      maxzoom: LAYER_ZOOM_LEVELS.CLUSTER.MAX,
    });

    // 添加单点图层
    const layerId = `${sourceId}-layer`;
    map.addLayer({
      id: layerId,
      type: 'symbol',
      source: sourceId,
      filter: ['!', ['has', 'point_count']],
      layout: {
        'icon-image': ['get', 'icon', ['get', 'style']],
        'icon-allow-overlap': true,
        'icon-ignore-placement': true,
        'icon-size': LAYER_ZOOM_LEVELS.ICON.SIZE.DEFAULT,
      },
      paint: {
        'icon-color-transition': { duration: 300 },
        'icon-opacity': 0.9,
      },
      minzoom: LAYER_ZOOM_LEVELS.ICON.MIN,
      maxzoom: LAYER_ZOOM_LEVELS.ICON.MAX,
    });

    // 添加点击事件
    this.setupClickHandler(map, layerId, options);

    return {
      layerId,
      sourceId,
      clusterLayerId,
    };
  }

  /**
   * 设置点击事件处理
   * @param {Object} map - MapLibre地图实例
   * @param {String} layerId - 图层ID
   * @param {Object} options - 选项
   */
  static setupClickHandler(map, layerId, options = {}) {
    const handlerKey = `_${this.name}ClickHandler`;

    // 移除已有的点击事件
    if (map[handlerKey]) {
      map.off('click', layerId, map[handlerKey]);
    }

    // 创建新的点击事件处理函数
    map[handlerKey] = e => {
      const features = map.queryRenderedFeatures(e.point, {
        layers: [layerId],
      });

      if (features.length > 0) {
        const feature = features[0];

        const coordinates = feature.geometry.coordinates.slice();
        const properties = feature.properties;
        // 尝试从多个可能的来源获取ID
        const id = feature.id || (properties && properties.id) || `facility-marker-${Date.now()}`;

        // 防止地图缩放时弹出窗口位置偏移
        while (Math.abs(e.lngLat.lng - coordinates[0]) > 180) {
          coordinates[0] += e.lngLat.lng > coordinates[0] ? 360 : -360;
        }

        // 检查当前点击的标记是否已存在
        const isCurrentMarkerExist = map._facilityBoardMarkers && map._facilityBoardMarkers.has(id);

        // 如果当前标记存在，则移除它（实现切换效果）
        if (isCurrentMarkerExist) {
          this.removeBoardMarker(map, id);
        } else {
          // 如果当前标记不存在，则先清除其他所有标记，再创建新标记
          this.clearBoardMarkers(map);
          this.createBoardMarker(map, id, coordinates, properties.label, properties);
        }

        // 阻止事件继续传播
        e.originalEvent.stopPropagation();
      }
    };

    // 添加点击事件
    map.on('click', layerId, map[handlerKey]);
  }

  /**
   * 创建立牌标记
   * @param {Object} map - MapLibre地图实例
   * @param {String} id - 标记ID
   * @param {Array} lngLat - 经纬度坐标
   * @param {String} label - 标记标签
   * @param {Object} properties - 标记属性
   * @returns {Object} 标记对象
   */
  static createBoardMarker(map, id, lngLat, label, properties = {}) {
    const el = document.createElement('div');

    // 创建Vue应用实例
    const app = createApp(MarkerInfo, {
      label,
      properties,
    });

    // 获取组件实例
    const componentInstance = app.mount(el);

    // 设置关闭回调
    if (componentInstance.setCloseCallback) {
      componentInstance.setCloseCallback(() => {
        this.removeBoardMarker(map, id);
      });
    }

    // 创建 Marker
    const marker = new maplibregl.Marker({
      element: el,
      anchor: 'bottom', // 使用bottom锚点使其位于坐标点正上方
      offset: [0, -25], // 添加垂直偏移，与图标保持10px距离
    })
      .setLngLat(lngLat)
      .addTo(map);

    // 保存标记信息
    if (!map._facilityBoardMarkers) {
      map._facilityBoardMarkers = new Map();
    }

    map._facilityBoardMarkers.set(id, {
      marker,
      app,
      el,
      properties,
      componentInstance,
    });

    return marker;
  }

  /**
   * 移除立牌标记
   * @param {Object} map - MapLibre地图实例
   * @param {String} id - 标记ID
   */
  static removeBoardMarker(map, id) {
    if (map._facilityBoardMarkers && map._facilityBoardMarkers.has(id)) {
      const { marker, app } = map._facilityBoardMarkers.get(id);
      marker.remove();
      app.unmount();
      map._facilityBoardMarkers.delete(id);
    }
  }

  /**
   * 清除所有立牌标记
   * @param {Object} map - MapLibre地图实例
   */
  static clearBoardMarkers(map) {
    if (map._facilityBoardMarkers) {
      map._facilityBoardMarkers.forEach(({ marker, app }) => {
        marker.remove();
        app.unmount();
      });
      map._facilityBoardMarkers.clear();
    }
  }

  /**
   * 过滤特定类型的设施标记
   * @param {Array} features - GeoJSON特征数组
   * @param {String} facilityType - 设施类型
   * @returns {Array} 过滤后的特征数组
   */
  static filterByType(features, facilityType) {
    return features.filter(feature => {
      if (!feature.properties) return false;

      // 检查category属性
      const category = feature.properties.category || feature.properties.bridgeType;
      return category === facilityType;
    });
  }

  /**
   * 准备标记数据
   * @param {Array} features - GeoJSON特征数组
   * @param {Object} iconMapping - 图标映射
   * @returns {Object} GeoJSON数据
   */
  static prepareMarkerData(features, iconMapping = {}) {
    const markerFeatures = [];

    features.forEach((feature, index) => {
      // 确保每个feature都有唯一的id
      const id = feature.id || `facility-${index}`;
      const coordinates = feature.geometry.coordinates;
      const properties = feature.properties || {};

      // 同时在properties中也保存id，以便在某些情况下可以从properties中获取
      properties.id = id;

      // 移除左右显示的相关属性
      properties.markerIndex = index;
      // 移除 markerPosition 属性，不再使用左右显示逻辑

      // 检查坐标是否有效
      if (coordinates && coordinates.length === 2 && coordinates[0] && coordinates[1]) {
        // 根据category设置正确的图标URL
        const category = properties.category || properties.bridgeType;
        if (category && iconMapping[category]) {
          if (!properties.style) {
            properties.style = {};
          }
          properties.style.iconUrl = iconMapping[category];
          properties.style.icon = `${category}-icon`;
        }

        // 标记为工程设施标记
        properties.markerType = 'facility';

        // 添加到工程设施标记集合
        markerFeatures.push({
          ...feature,
          id,
          properties,
        });
      }
    });

    return {
      type: 'FeatureCollection',
      features: markerFeatures,
    };
  }
}

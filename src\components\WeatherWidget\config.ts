import CloudyIcon from '@/assets/modules/common/icons/cloudy.svg';

/**
 * 天气组件基于和风天气（https://www.qweather.com/）免费额度 1000/天
 */
// API KEY
export const WEATHER_API_KEY = '5c71cd3b34834cf0bdf5e37d285f877c';

// 和风天气API接口
export const WEATHER_API = {
  // 城市查询接口
  CITY_LOOKUP: 'https://geoapi.qweather.com/v2/city/lookup',
  // 实时天气接口
  REAL_TIME: 'https://devapi.qweather.com/v7/weather/now',
};

// 天气图标映射表 (和风天气图标代码 -> 本地图标路径)
export const WEATHER_ICON_MAP = {
  '100': '/assets/images/icons/weather/100.png', // 晴天
  '101': '/assets/images/icons/weather/101.png', // 多云
  '102': '/assets/images/icons/weather/101.png', // 少云
  '103': '/assets/images/icons/weather/101.png', // 晴间多云
  '104': '/assets/images/icons/weather/104.png', // 阴天
  '150': '/assets/images/icons/weather/150.png',
  '151': '/assets/images/icons/weather/151.png',
  '305': '/assets/images/icons/weather/305.png', // 小雨
  // '300': '/icons/weather/shower.svg', // 阵雨
  // '301': '/icons/weather/rain.svg', // 强阵雨
  // '302': '/icons/weather/thunder.svg', // 雷阵雨
  // '305': '/icons/weather/rain.svg', // 小雨
  // '306': '/icons/weather/rain.svg', // 中雨
  // '307': '/icons/weather/heavy-rain.svg', // 大雨
  // '400': '/icons/weather/snow.svg', // 小雪
  // '401': '/icons/weather/snow.svg', // 中雪
  // '402': '/icons/weather/heavy-snow.svg', // 大雪
  // '501': '/icons/weather/fog.svg', // 雾
  '502': '/assets/images/icons/weather/502.png', // 霾
  // 可根据需要添加更多天气类型
};

// 默认城市
export const DEFAULT_CITY = '青海';

export const DEFAULT_CITY_ID = '101150714'; // 格尔木市

<template>
	<DefaultMarker panelTitle="交通事件详情" :markerClass="markerClass">
		<template #icon>
			<component :is="icon"></component>
		</template>
		<template #content>
			<div class="data-list">
				<div class="data-item">
					<span class="data-key">事故类型:</span>
					<span class="data-value">
						{{ TrafficAccidentType.getLabel(props.properties["eventType"]) }}
					</span>
				</div>
				<div class="data-item">
					<span class="data-key">更新时间:</span>
					<span class="data-value"> {{ props.properties["updateTime"] }} </span>
				</div>
				<div class="data-item">
					<span class="data-key">位置:</span>
					<span class="data-value"> {{ props.properties["location"] }} </span>
				</div>
			</div>
		</template>
	</DefaultMarker>
</template>

<script setup>
import { TrafficAccidentType } from "@/utils/dict";
import jam from "@/assets/modules/traffic/icon/jam.svg?component";
import roadClosure from "@/assets/modules/traffic/icon/roadClosure.svg?component";
import accident from "@/assets/modules/traffic/icon/accident.svg?component";
import otherAccident from "@/assets/modules/traffic/icon/otherAccident.svg?component";
import DefaultMarker from "./DefaultMarker.vue";

const props = defineProps({
	properties: Object,
});

const markerClass = "is-orange";

const icon = computed(() => {
	const item = TrafficAccidentType.getItem(props.properties["eventType"]);
	if (item) {
		return { jam, roadClosure, accident, otherAccident }[item.icon];
	}
	return null;
});
</script>

<template>
  <TrafficCard title="绿色低碳">
    <div class="green-root">
      <!-- 上方栏 -->
      <div class="safe-top">
        <div class="safe-card" v-for="item in leftList" :key="item.name">
          <div class="safe-icon">
            <img :src="item.icon" />
          </div>
          <div class="safe-info">
            <div class="safe-name">{{ item.name }}</div>
            <div class="safe-value">{{ statusData[item.key] || 0 }}</div>
            <div class="safe-unit">{{ item.unit }}</div>
          </div>
        </div>
      </div>

      <!-- 下方列表 -->
      <div class="safe-right">
        <div class="green-table">
          <div class="table-header">
            <span class="th-icon">
              <!-- <img :src="tableGreen" alt="" /> -->
              工程类型
            </span>
            <span style="width: 75px">
              化石能源
              <span class="th-unit">(万吨)</span>
            </span>
            <span style="width: 51px">
              电力
              <span class="th-unit">(万吨)</span>
            </span>
            <span style="width: 105px">
              材料生产运输
              <span class="th-unit">(万吨)</span>
            </span>
            <span class="th-progress" style="width: 48px"> 进度比例 </span>
          </div>
          <div class="table-body">
            <div class="table-row" v-for="row in listData" :key="row.name">
              <span class="td-name">{{ row.name }}</span>
              <span>{{ formatThousand(row.huashiNum) }}</span>
              <span>{{ formatThousand(row.dianliNum) }}</span>
              <span>{{ formatThousand(row.cailiaoNum) }}</span>
              <span class="td-progress">{{ row.percent }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #extra>
      <div class="link" @click="onInfo">查看更多 <icon-double-right /></div>
    </template>
  </TrafficCard>
</template>

<script setup>
import request from "@/utils/request";
import dt from "@/assets/images/people/dt.svg";
import gcpf from "@/assets/images/people/gcpf.svg";
import jdgl from "@/assets/images/people/jdgl.svg";
import tableGreen from "@/assets/images/people/tableGreen.svg";
import emitter from "@/utils/emitter";

const leftList = [
  { icon: dt, name: "碳排放测算总量", key: "total", unit: "万吨" },
  { icon: gcpf, name: "过程排放量", key: "process", unit: "万吨" },
  { icon: jdgl, name: "进度比例", key: "percent", unit: "%" },
];

const statusData = ref({});
const listData = ref([]);
const currentDepartmentId = ref(null);

onMounted(() => {
  // 监听区域双击事件
  emitter.$on("division-area-dblclick", handleAreaChange);
  getData();
});

onBeforeUnmount(() => {
  emitter.$off("division-area-dblclick", handleAreaChange);
});

const handleAreaChange = (eventData) => {
  console.log("绿色低碳 - 接收到区域双击事件:", eventData);
  currentDepartmentId.value = eventData.properties.id;
  getData();
};

const getData = () => {
  getStatus();
  getListData();
};

const getStatus = () => {
  const params = currentDepartmentId.value ? { departmentId: currentDepartmentId.value } : {};
  request.get("/api/screen/board/left/lowcarbon/stat", params).then((res) => {
    if (res.code === 200) {
      statusData.value = res.data;
    }
  });
};

const getListData = () => {
  const params = currentDepartmentId.value ? { departmentId: currentDepartmentId.value } : {};
  request.get("/api/screen/board/left/lowcarbon/list", params).then((res) => {
    if (res.code === 200) {
      listData.value = res.data; // 移除重复数据，只显示原始数据
    }
  });
};

const formatThousand = (num) => {
  return Number(num).toLocaleString();
};

const onInfo = () => {};
</script>

<style lang="scss" scoped>
.green-root {
  height: 270px;
  display: flex;
  flex-direction: column;

  .safe-top {
    width: 100%;
    display: flex;
    // flex-direction: column;
    justify-content: space-around;
    align-items: center;
    padding: 8px 0px;
    // gap: 18px;

    .safe-card {
      // flex: 1;
      display: flex;
      align-items: center;
      // background: rgba(30, 60, 100, 0.15);
      // border-radius: 8px;
      // padding: 8px 10px;

      .safe-icon {
        width: 40px;
        height: 40px;
        margin-right: 8px;
        background-image: url("@/assets/modules/traffic/icon/device-bg.svg");
        background-size: 100% 100%;
        position: relative;

        img {
          position: absolute;
          left: 50%;
          bottom: 17px;
          transform: translateX(-50%);
          width: 20px;
          height: 20px;
        }
      }

      .safe-info {
        display: flex;
        flex-direction: column;

        .safe-name {
          font-family: Alibaba PuHuiTi;
          font-size: 12px;
          color: #c4e5ff;
          margin-bottom: 2px;
        }

        .safe-value {
          font-family: D-DIN-PRO, D-DIN-PRO;
          font-size: 18px;
          font-weight: bold;
          margin-right: 2px;
          text-shadow: 0px 0px 10px rgba(0, 198, 255, 0.25);
          background: -webkit-linear-gradient(top, #f7fdfd 0%, rgba(0, 198, 255, 0.25) 100%);
          background: linear-gradient(to bottom, #f7fdfd 0%, rgba(0, 198, 255, 0.25) 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .safe-unit {
          font-family: Alibaba PuHuiTi;
          font-size: 10px;
          color: #c4e5ff;
        }
      }
    }
  }

  .line {
    width: 3px;
    height: 100%;
    opacity: 0.3;
    background: linear-gradient(
      to bottom,
      rgba(0, 138, 255, 0) 0%,
      rgba(0, 138, 255, 1) 50%,
      rgba(0, 138, 255, 0) 100%
    );
    // border-radius: 1px;
  }

  .safe-right {
    flex: 1;
    width: 100%;
    padding: 0px;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .green-table {
      height: 100%;
      display: flex;
      flex-direction: column;

      .table-body {
        flex: 1;
        overflow: hidden; // 防止内容溢出
      }

      .table-header {
        display: flex;
        align-items: center;
        color: #fff;
        // font-weight: bold;
        font-size: 12px;
        height: 32px;
        white-space: nowrap;
        background: linear-gradient(180deg, rgba(0, 138, 255, 0) 47%, #008aff 100%),
          rgba(0, 138, 255, 0.1);
        box-shadow: inset 0px 0px 8px 1px rgba(0, 138, 255, 0.25);
        border-radius: 0px 0px 0px 0px;
        border: 1px solid #008aff;
        border-image: linear-gradient(
            90deg,
            rgba(0, 138, 255, 0),
            rgba(0, 138, 255, 1),
            rgba(0, 138, 255, 0.2),
            rgba(0, 138, 255, 0)
          )
          1 1;
        span {
          flex: 1;
          text-align: center;

          &:first-child {
            text-align: center;
            // width: 55px;
          }

          &.th-progress {
            text-align: center;
            // padding-right: 16px;
          }
        }

        .th-icon {
          // width: 55px;
        }
        span {
          font-size: 12px; // 主标题
          // font-weight: bold;
          .th-unit {
            font-size: 10px; // 单位
            font-weight: normal;
            // color: #8ecfff;
            // margin-left: 2px;
          }
        }
      }

      .table-row {
        display: flex;
        align-items: center;
        background: rgba(0, 138, 255, 0.05);
        color: #fff;
        font-size: 12px;
        height: 32px;
        border-bottom: 1px solid rgba(0, 138, 255, 0.08);

        &:last-child {
          border-bottom: none;
        }

        span {
          flex: 1;
          text-align: center;

          &:first-child {
            // text-align: center;
            // display: flex;
            // align-items: center;
            width: 55px;
            text-align: center;
          }

          &.td-progress {
            text-align: center;
            // padding-right: 16px;
            // color: #38e6fd;
          }
        }
      }
    }
  }
}
</style>
